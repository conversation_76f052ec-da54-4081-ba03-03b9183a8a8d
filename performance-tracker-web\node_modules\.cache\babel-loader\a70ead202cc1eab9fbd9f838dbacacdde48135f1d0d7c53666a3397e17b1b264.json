{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./styles/App.css';import HomePage from'./模块一/pages/HomePage';import WorkTarget from'./模块一/pages/WorkTarget';import WorkTracking from'./模块二/pages/WorkTracking';import WorldClass from'./模块三/pages/WorldClass';import MonthlyKPI from'./模块四/pages/MonthlyKPI';import ProjectOne from'./模块五/pages/ProjectOne';import ModuleSix from'./模块六/pages/ModuleSix';import Department from'./pages/Department';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[currentPage,setCurrentPage]=useState('home');const[isLoading,setIsLoading]=useState(true);useEffect(()=>{// 模拟系统初始化\nconst timer=setTimeout(()=>{setIsLoading(false);},2000);return()=>clearTimeout(timer);},[]);const renderPage=()=>{switch(currentPage){case'home':return/*#__PURE__*/_jsx(HomePage,{onNavigate:setCurrentPage});case'work-target':return/*#__PURE__*/_jsx(WorkTarget,{onNavigate:setCurrentPage});case'work-tracking':return/*#__PURE__*/_jsx(WorkTracking,{onNavigate:setCurrentPage});case'world-class':return/*#__PURE__*/_jsx(WorldClass,{onNavigate:setCurrentPage});case'monthly-kpi':return/*#__PURE__*/_jsx(MonthlyKPI,{onNavigate:setCurrentPage});case'project-one':return/*#__PURE__*/_jsx(ProjectOne,{onNavigate:setCurrentPage});case'module-six':return/*#__PURE__*/_jsx(ModuleSix,{onNavigate:setCurrentPage});case'department':return/*#__PURE__*/_jsx(Department,{onNavigate:setCurrentPage});default:return/*#__PURE__*/_jsx(HomePage,{onNavigate:setCurrentPage});}};if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"loading-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"loading-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"loading-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-text\",children:\"\\u667A\\u80FD\\u7EE9\\u6548\\u8DDF\\u8E2A\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsx(\"div\",{className:\"logo-subtitle\",children:\"INTELLIGENT PERFORMANCE TRACKING SYSTEM\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-progress\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-status\",children:\"\\u7CFB\\u7EDF\\u521D\\u59CB\\u5316\\u4E2D...\"})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:renderPage()});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "HomePage", "WorkTarget", "WorkTracking", "WorldClass", "MonthlyKPI", "ProjectOne", "ModuleSix", "Department", "jsx", "_jsx", "jsxs", "_jsxs", "App", "currentPage", "setCurrentPage", "isLoading", "setIsLoading", "timer", "setTimeout", "clearTimeout", "renderPage", "onNavigate", "className", "children"], "sources": ["E:/处理公司程序/重点工作网页化设计7.14/performance-tracker-web/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './styles/App.css';\r\nimport HomePage from './模块一/pages/HomePage';\r\nimport WorkTarget from './模块一/pages/WorkTarget';\r\nimport WorkTracking from './模块二/pages/WorkTracking';\r\nimport WorldClass from './模块三/pages/WorldClass';\r\nimport MonthlyKPI from './模块四/pages/MonthlyKPI';\r\nimport ProjectOne from './模块五/pages/ProjectOne';\r\nimport ModuleSix from './模块六/pages/ModuleSix';\r\nimport Department from './pages/Department';\r\n\r\nfunction App() {\r\n  const [currentPage, setCurrentPage] = useState('home');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // 模拟系统初始化\r\n    const timer = setTimeout(() => {\r\n      setIsLoading(false);\r\n    }, 2000);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  const renderPage = () => {\r\n    switch (currentPage) {\r\n      case 'home':\r\n        return <HomePage onNavigate={setCurrentPage} />;\r\n      case 'work-target':\r\n        return <WorkTarget onNavigate={setCurrentPage} />;\r\n      case 'work-tracking':\r\n        return <WorkTracking onNavigate={setCurrentPage} />;\r\n      case 'world-class':\r\n        return <WorldClass onNavigate={setCurrentPage} />;\r\n      case 'monthly-kpi':\r\n        return <MonthlyKPI onNavigate={setCurrentPage} />;\r\n      case 'project-one':\r\n        return <ProjectOne onNavigate={setCurrentPage} />;\r\n      case 'module-six':\r\n        return <ModuleSix onNavigate={setCurrentPage} />;\r\n      case 'department':\r\n        return <Department onNavigate={setCurrentPage} />;\r\n      default:\r\n        return <HomePage onNavigate={setCurrentPage} />;\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"loading-screen\">\r\n        <div className=\"loading-content\">\r\n          <div className=\"loading-logo\">\r\n            <div className=\"logo-text\">智能绩效跟踪系统</div>\r\n            <div className=\"logo-subtitle\">INTELLIGENT PERFORMANCE TRACKING SYSTEM</div>\r\n          </div>\r\n          <div className=\"loading-progress\">\r\n            <div className=\"progress-bar\"></div>\r\n          </div>\r\n          <div className=\"loading-status\">系统初始化中...</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      {renderPage()}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,kBAAkB,CACzB,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAC7C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,MAAM,CAAC,CACtD,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAkB,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMG,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB,OAAQP,WAAW,EACjB,IAAK,MAAM,CACT,mBAAOJ,IAAA,CAACT,QAAQ,EAACqB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACjD,IAAK,aAAa,CAChB,mBAAOL,IAAA,CAACR,UAAU,EAACoB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,IAAK,eAAe,CAClB,mBAAOL,IAAA,CAACP,YAAY,EAACmB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACrD,IAAK,aAAa,CAChB,mBAAOL,IAAA,CAACN,UAAU,EAACkB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,IAAK,aAAa,CAChB,mBAAOL,IAAA,CAACL,UAAU,EAACiB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,IAAK,aAAa,CAChB,mBAAOL,IAAA,CAACJ,UAAU,EAACgB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,IAAK,YAAY,CACf,mBAAOL,IAAA,CAACH,SAAS,EAACe,UAAU,CAAEP,cAAe,CAAE,CAAC,CAClD,IAAK,YAAY,CACf,mBAAOL,IAAA,CAACF,UAAU,EAACc,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,QACE,mBAAOL,IAAA,CAACT,QAAQ,EAACqB,UAAU,CAAEP,cAAe,CAAE,CAAC,CACnD,CACF,CAAC,CAED,GAAIC,SAAS,CAAE,CACb,mBACEN,IAAA,QAAKa,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BZ,KAAA,QAAKW,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BZ,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3Bd,IAAA,QAAKa,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,kDAAQ,CAAK,CAAC,cACzCd,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yCAAuC,CAAK,CAAC,EACzE,CAAC,cACNd,IAAA,QAAKa,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/Bd,IAAA,QAAKa,SAAS,CAAC,cAAc,CAAM,CAAC,CACjC,CAAC,cACNb,IAAA,QAAKa,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,yCAAS,CAAK,CAAC,EAC5C,CAAC,CACH,CAAC,CAEV,CAEA,mBACEd,IAAA,QAAKa,SAAS,CAAC,KAAK,CAAAC,QAAA,CACjBH,UAAU,CAAC,CAAC,CACV,CAAC,CAEV,CAEA,cAAe,CAAAR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}