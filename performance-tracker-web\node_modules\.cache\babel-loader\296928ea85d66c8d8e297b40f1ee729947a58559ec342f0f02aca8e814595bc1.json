{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./styles/index.css';import App from'./App';// 创建React根节点\nimport{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));// 渲染应用\nroot.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["E:/处理公司程序/重点工作网页化设计7.14/performance-tracker-web/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport './styles/index.css';\r\nimport App from './App';\r\n\r\n// 创建React根节点\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\n\r\n// 渲染应用\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);\r\n\r\n "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,oBAAoB,CAC3B,MAAO,CAAAC,GAAG,KAAM,OAAO,CAEvB;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,IAAI,CAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CAEjE;AACAH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACJ,KAAK,CAACU,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,GAAG,GAAE,CAAC,CACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}