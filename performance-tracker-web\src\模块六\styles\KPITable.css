/* KPI表格样式 - 模块六 */

/* 表格控制区域 */
.module6_table-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

/* 月份导航 */
.module6_month-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.module6_month-nav-btn {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border: none;
  border-radius: 8px;
  color: #000;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_month-nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.module6_month-display {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  min-width: 120px;
  text-align: center;
}

/* 导出按钮 */
.module6_export-btn {
  padding: 12px 24px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border: none;
  border-radius: 8px;
  color: #000;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}



/* 主表格 - 合并容器样式，修复表头固定，移除外边框 */
.module6_kpi-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: rgba(0, 0, 0, 0.4);
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.95rem;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  max-height: 70vh;
  overflow-y: auto;
  /* 确保表格本身没有外边框 */
  border: none !important;
  /* 移除所有可能的边框和outline */
  outline: none !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* 表头 - 修复固定表头 */
.module6_kpi-table thead {
  /* 移除thead的sticky，只在th上设置 */
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
}

.module6_kpi-table th {
  padding: 15px 12px;
  text-align: center;
  vertical-align: middle;
  font-weight: 700;
  color: #ffd700;
  border-bottom: 2px solid rgba(255, 215, 0, 0.4);
  /* 移除所有其他边框 */
  border-top: none;
  border-left: none;
  border-right: none;
  /* 强化sticky定位 - 确保表头固定 */
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(10px);
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
  white-space: nowrap;
  min-width: 100px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  /* 确保背景完全覆盖 */
  background-clip: padding-box;
}

.module6_month-header {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05)) !important;
  font-size: 0.9rem;
  min-width: 120px;
}

/* 表格行 - 确保最后一行没有边框 */
.module6_kpi-table tbody tr {
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  transition: all 0.3s ease;
}

/* 移除最后一行的下边框，防止外边框线 */
.module6_kpi-table tbody tr:last-child {
  border-bottom: none !important;
}

.module6_kpi-table tbody tr:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.001);
}

.module6_kpi-table tbody tr:nth-child(even) {
  background: rgba(255, 215, 0, 0.05);
}

.module6_kpi-table tbody tr:nth-child(even):hover {
  background: rgba(255, 215, 0, 0.15);
}

/* 表格单元格 - 垂直居中优化，移除重复边框 */
.module6_kpi-table td {
  padding: 12px 8px;
  text-align: center;
  vertical-align: middle;
  /* 移除所有边框 */
  border: none;
  position: relative;
  min-width: 100px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  max-width: 300px;
  height: auto;
  min-height: 50px;
}

/* 固定列样式 - 垂直居中优化，精确调整序号列宽度 */
.module6_sequence-cell {
  background: rgba(255, 215, 0, 0.1);
  font-weight: 700;
  color: #ffd700;
  /* 精确调整序号列宽度，仅容纳"序号"两字的最小宽度 */
  min-width: 60px;
  max-width: 80px;
  width: 70px;
  text-align: center;
  vertical-align: middle;
  padding: 12px 4px;
  font-size: 0.9rem;
}

.module6_indicator-cell {
  background: rgba(255, 215, 0, 0.05);
  text-align: left;
  padding: 12px 15px;
  font-weight: 600;
  /* 由于序号、权重、评分列减小，可以进一步扩大指标列 */
  min-width: 250px;
  max-width: 420px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  vertical-align: middle;
}

.module6_target-cell {
  text-align: left;
  padding: 12px 15px;
  /* 由于序号、权重、评分列减小，可以进一步扩大目标值列宽度 */
  min-width: 250px;
  max-width: 380px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  vertical-align: middle;
}

.module6_weight-cell {
  font-weight: 700;
  color: #ffd700;
  /* 权重列与序号列保持相同宽度 */
  min-width: 60px;
  max-width: 80px;
  width: 70px;
  text-align: center;
  vertical-align: middle;
  padding: 12px 4px;
}

.module6_category-cell {
  /* 由于序号、权重、评分列减小，可以进一步扩大指标分类列 */
  min-width: 150px;
  max-width: 200px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  vertical-align: middle;
  text-align: center;
}

.module6_frequency-cell {
  /* 由于序号、权重、评分列减小，可以适当扩大频次列 */
  min-width: 100px;
  max-width: 130px;
  width: 120px;
  text-align: center;
  vertical-align: middle;
  padding: 12px 6px;
}

/* 月份单元格 - 垂直居中优化，扩大显示空间 */
.module6_month-cell {
  /* 由于序号、权重、评分列减小，月份列可以进一步扩大 */
  min-width: 220px;
  max-width: 420px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  vertical-align: middle;
  padding: 12px 15px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  min-height: 60px;
  text-align: center;
}

/* 月份评分列专门样式 - 与序号列保持相同宽度 */
.module6_score-cell {
  min-width: 60px !important;
  max-width: 80px !important;
  width: 70px !important;
  padding: 12px 4px !important;
  text-align: center !important;
}

/* 可编辑单元格样式 */
.module6_month-cell.editable {
  background: rgba(255, 215, 0, 0.05);
  border-left: 3px solid rgba(255, 215, 0, 0.3);
}

.module6_month-cell.editable:hover {
  background: rgba(255, 215, 0, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);
  transform: scale(1.02);
}

/* 只读单元格样式 */
.module6_month-cell.readonly {
  background: rgba(128, 128, 128, 0.15);
  color: rgba(255, 255, 255, 0.8);
  cursor: not-allowed;
  border-left: 3px solid rgba(128, 128, 128, 0.5);
}

.module6_month-cell.readonly:hover {
  background: rgba(128, 128, 128, 0.2);
}

/* 匹配状态指示 */
.module6_month-cell.matched {
  border-top: 2px solid rgba(0, 255, 0, 0.4);
}

.module6_month-cell.unmatched {
  border-top: 2px solid rgba(255, 165, 0, 0.4);
}

/* 只读指示器 */
.module6_readonly-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 0.8rem;
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

/* 单元格内容容器 - 垂直居中优化 */
.module6_cell-content {
  width: 100%;
  min-height: 24px;
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  text-align: center;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* 单元格行显示 */
.module6_cell-line {
  margin-bottom: 4px;
  display: block;
}

.module6_cell-line:last-child {
  margin-bottom: 0;
}

.module6_cell-line:empty {
  display: none;
}

/* 长内容显示 */
.module6_cell-long-content {
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 215, 0, 0.5) transparent;
  padding-right: 4px;
}

.module6_cell-long-content::-webkit-scrollbar {
  width: 6px;
}

.module6_cell-long-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.module6_cell-long-content::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 3px;
}

.module6_cell-long-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* 内容截断提示 */
.module6_cell-content.truncated::after {
  content: "...";
  color: rgba(255, 215, 0, 0.7);
  font-weight: bold;
}

/* 空内容占位符 */
.module6_cell-content:empty::before {
  content: "-";
  color: rgba(255, 255, 255, 0.3);
  font-style: italic;
}

/* 输入框和文本域样式 - 优化版本 */
.module6_cell-input {
  width: 100%;
  min-height: 30px;
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 6px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  text-align: center;
  outline: none;
  padding: 8px;
  transition: all 0.3s ease;
}

.module6_cell-input:focus {
  background: rgba(255, 215, 0, 0.2);
  border-color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
  transform: scale(1.02);
}

.module6_cell-textarea {
  width: 100%;
  min-height: 60px;
  max-height: 120px;
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 6px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  outline: none;
  padding: 8px;
  resize: vertical;
  line-height: 1.4;
  transition: all 0.3s ease;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.module6_cell-textarea:focus {
  background: rgba(255, 215, 0, 0.2);
  border-color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
  transform: scale(1.02);
}

/* 文本域滚动条样式 */
.module6_cell-textarea::-webkit-scrollbar {
  width: 6px;
}

.module6_cell-textarea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.module6_cell-textarea::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 3px;
}

.module6_cell-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* 同步状态工具栏 - 作为表格行 */
.module6_sync-toolbar {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  padding: 0;
  margin: 0;
}

.module6_toolbar-content {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.module6_pending-changes,
.module6_sync-errors {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module6_pending-count,
.module6_error-count {
  color: #ffd700;
  font-weight: 600;
  font-size: 0.9rem;
}

.module6_retry-btn,
.module6_clear-btn,
.module6_clear-errors-btn,
.module6_show-errors-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.module6_retry-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.module6_retry-btn:hover {
  background: linear-gradient(45deg, #45a049, #3d8b40);
  transform: translateY(-1px);
}

.module6_clear-btn,
.module6_clear-errors-btn {
  background: linear-gradient(45deg, #f44336, #da190b);
  color: white;
}

.module6_clear-btn:hover,
.module6_clear-errors-btn:hover {
  background: linear-gradient(45deg, #da190b, #c62828);
  transform: translateY(-1px);
}

.module6_show-errors-btn {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

.module6_show-errors-btn:hover {
  background: linear-gradient(45deg, #1976D2, #1565C0);
  transform: translateY(-1px);
}

/* 错误模态框 */
.module6_modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.module6_error-modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.module6_modal-header {
  background: rgba(255, 215, 0, 0.1);
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.module6_modal-header h3 {
  color: #ffd700;
  margin: 0;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 700;
}

.module6_modal-close {
  background: none;
  border: none;
  color: #ffd700;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.module6_modal-close:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: scale(1.1);
}

.module6_modal-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.module6_error-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.module6_error-item {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 8px;
  padding: 12px;
}

.module6_error-field {
  color: #ffd700;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.module6_error-message {
  color: #ff6b6b;
  font-weight: 500;
  margin-bottom: 4px;
}

.module6_error-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  margin-bottom: 4px;
}

.module6_error-value {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  font-style: italic;
}

.module6_modal-footer {
  background: rgba(0, 0, 0, 0.3);
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 215, 0, 0.3);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.module6_retry-all-btn,
.module6_clear-all-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.module6_retry-all-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.module6_retry-all-btn:hover {
  background: linear-gradient(45deg, #45a049, #3d8b40);
  transform: translateY(-1px);
}

.module6_clear-all-btn {
  background: linear-gradient(45deg, #f44336, #da190b);
  color: white;
}

.module6_clear-all-btn:hover {
  background: linear-gradient(45deg, #da190b, #c62828);
  transform: translateY(-1px);
}

/* 合并单元格样式 */
.module6_merged-cell {
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.module6_merged-cell::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .module6_table-controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .module6_month-navigation {
    justify-content: center;
  }
  
  .module6_export-btn {
    align-self: center;
  }
  
  .module6_kpi-table {
    font-size: 0.85rem;
  }
  
  .module6_kpi-table th,
  .module6_kpi-table td {
    padding: 8px 6px;
  }
  
  /* 序号列响应式优化 */
  .module6_sequence-cell {
    min-width: 50px;
    max-width: 60px;
    width: 55px;
    padding: 8px 2px;
    font-size: 0.8rem;
  }
  
  /* 权重列响应式优化 */
  .module6_weight-cell {
    min-width: 50px;
    max-width: 60px;
    width: 55px;
    padding: 8px 2px;
  }
  
  /* 评分列响应式优化 */
  .module6_score-cell {
    min-width: 50px !important;
    max-width: 60px !important;
    width: 55px !important;
    padding: 8px 2px !important;
  }
  
  .module6_month-header {
    min-width: 100px;
    font-size: 0.8rem;
  }
  
  .module6_month-cell {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .module6_table-controls {
    padding: 15px 20px;
  }
  
  .module6_month-display {
    font-size: 1rem;
    min-width: 100px;
  }
  
  .module6_month-nav-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .module6_export-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .module6_kpi-table {
    font-size: 0.8rem;
  }
  
  .module6_kpi-table th,
  .module6_kpi-table td {
    padding: 6px 4px;
  }
  
  /* 序号列移动端优化 */
  .module6_sequence-cell {
    min-width: 45px;
    max-width: 50px;
    width: 48px;
    padding: 6px 2px;
    font-size: 0.75rem;
  }
  
  /* 权重列移动端优化 */
  .module6_weight-cell {
    min-width: 45px;
    max-width: 50px;
    width: 48px;
    padding: 6px 2px;
  }
  
  /* 评分列移动端优化 */
  .module6_score-cell {
    min-width: 45px !important;
    max-width: 50px !important;
    width: 48px !important;
    padding: 6px 2px !important;
  }
  
  .module6_indicator-cell,
  .module6_target-cell {
    padding-left: 8px;
  }
  
  .module6_month-header {
    min-width: 80px;
    font-size: 0.75rem;
  }
  
  .module6_month-cell {
    min-width: 80px;
  }
  
  .module6_month-cell input {
    font-size: 0.8rem;
    padding: 3px;
  }
} 