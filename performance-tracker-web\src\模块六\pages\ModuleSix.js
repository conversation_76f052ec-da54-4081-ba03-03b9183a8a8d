import React, { useState, useEffect } from 'react';
import '../styles/ModuleSix.css';
import '../styles/KPITable.css'; // 确保KPITable样式优先级更高
import KPITable from '../components/KPITable';
import ExportModal from '../components/ExportModal';
import moduleSixService from '../services/moduleSixService';

const ModuleSix = ({ onNavigate }) => {
  const [activeTable, setActiveTable] = useState('金属橡胶件');
  const [currentView, setCurrentView] = useState('keyIndicators'); // keyIndicators 或 keyWork
  const [currentMonthPair, setCurrentMonthPair] = useState([6, 7]); // 默认显示6-7月
  const [data, setData] = useState({
    keyIndicators: [],
    keyWork: []
  });
  const [loading, setLoading] = useState(true);
  const [syncStatus, setSyncStatus] = useState('已同步');
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // 11个表的选择项
  const tableOptions = [
    '金属橡胶件',
    '空簧',
    '系统',
    '客户技术',
    '工艺模具',
    '仿真',
    '特装',
    '技术研究与发展',
    '车端',
    '属地化',
    '车体新材料'
  ];

  useEffect(() => {
    loadData();
  }, [activeTable]);

  const loadData = async () => {
    setLoading(true);
    try {
      const tableData = await moduleSixService.module6_loadTableData(activeTable);
      setData(tableData);
      setSyncStatus('数据加载成功');
    } catch (error) {
      console.error('数据加载失败:', error);
      setSyncStatus('数据加载失败');
    }
    setLoading(false);
  };

  const handleTableChange = (tableName) => {
    setActiveTable(tableName);
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  // 月份切换处理
  const handleMonthChange = (direction) => {
    setCurrentMonthPair(prev => {
      const [first, second] = prev;
      if (direction === 'prev') {
        if (first === 2) return [11, 12]; // 特殊处理：2-3月的前一个是11-12月
        const newFirst = first === 1 ? 12 : first - 1;
        const newSecond = first;
        return [newFirst, newSecond];
      } else {
        if (second === 12) return [2, 3]; // 特殊处理：11-12月的下一个是2-3月
        const newFirst = second;
        const newSecond = second === 12 ? 1 : second + 1;
        return [newFirst, newSecond];
      }
    });
  };

  const handleSyncStatus = (status) => {
    setSyncStatus(status);
  };

  // 处理导出
  const handleExport = async (exportConfig) => {
    setExportLoading(true);
    try {
      await moduleSixService.module6_exportData(activeTable, currentView, exportConfig);
      setSyncStatus('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      setSyncStatus('导出失败');
    }
    setExportLoading(false);
    setShowExportModal(false);
  };

  return (
    <div className="module6_module-six">
      {/* 背景动画 */}
      <div className="module6_module-six-background">
        <div className="module6_particle"></div>
        <div className="module6_particle"></div>
        <div className="module6_particle"></div>
        <div className="module6_grid-lines"></div>
      </div>

      {/* 顶部导航栏 */}
      <div className="module6_module-six-header">
        {/* 左侧：返回首页按钮 */}
        <div className="module6_header-left">
          <button 
            className="module6_back-button"
            onClick={() => onNavigate('home')}
          >
            ← 返回首页
          </button>
        </div>
        
        {/* 中间：标题居中显示 */}
        <div className="module6_header-center">
          <h1 className="module6_module-title">开发中心二级部门工作目标管理责任书</h1>
        </div>
        
        {/* 右侧：状态指示器（重新设计为按钮样式） */}
        <div className="module6_header-right">
          <div className={`module6_status-button ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'syncing'}`}>
            <span className={`module6_status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'syncing'}`}></span>
            {syncStatus}
          </div>
        </div>
      </div>

      {/* 表选择器 */}
      <div className="module6_table-selector">
        <label htmlFor="table-select">选择部门表：</label>
        <select 
          id="table-select"
          value={activeTable}
          onChange={(e) => handleTableChange(e.target.value)}
          className="module6_table-dropdown"
        >
          {tableOptions.map((table) => (
            <option key={table} value={table}>{table}</option>
          ))}
        </select>
        
        <button 
          className="module6_export-btn"
          onClick={() => setShowExportModal(true)}
        >
          导出数据
        </button>
      </div>

      {/* 合并到一行的布局：关键指标 | 月份 | 重点工作 */}
      <div className="module6_unified-header">
        {/* 左侧：关键指标 */}
        <div 
          className={`module6_header-section ${currentView === 'keyIndicators' ? 'active' : ''}`}
          onClick={() => handleViewChange('keyIndicators')}
        >
          <span className="module6_section-icon">🎯</span>
          <span className="module6_section-title">关键指标（40分）</span>
        </div>
        
        {/* 中间：月份选择器（从KPITable移动过来） */}
        <div className="module6_month-selector">
          <button 
            className="module6_month-nav-btn"
            onClick={() => handleMonthChange('prev')}
          >
            ←
          </button>
          <span className="module6_month-display">
            {currentMonthPair[0]}月-{currentMonthPair[1]}月
          </span>
          <button 
            className="module6_month-nav-btn"
            onClick={() => handleMonthChange('next')}
          >
            →
          </button>
        </div>
        
        {/* 右侧：重点工作 */}
        <div 
          className={`module6_header-section ${currentView === 'keyWork' ? 'active' : ''}`}
          onClick={() => handleViewChange('keyWork')}
        >
          <span className="module6_section-icon">🚀</span>
          <span className="module6_section-title">重点工作（60分）</span>
        </div>
      </div>

      {/* 表格内容区域 */}
      {loading ? (
        <div className="module6_loading-container">
          <div className="module6_loading-spinner"></div>
          <div className="module6_loading-text">数据加载中...</div>
        </div>
      ) : (
        <KPITable 
          data={data[currentView]}
          tableName={activeTable}
          viewType={currentView}
          currentMonthPair={currentMonthPair}
          onSyncStatus={handleSyncStatus}
        />
      )}

      {/* 导出模态框 */}
      {showExportModal && (
        <ExportModal
          onClose={() => setShowExportModal(false)}
          onExport={handleExport}
          loading={exportLoading}
          data={data[currentView]}
        />
      )}
    </div>
  );
};

export default ModuleSix; 