{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import'../styles/KPITable.css';import moduleSixService from'../services/moduleSixService';import ExportModal from'./ExportModal';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const KPITable=_ref=>{let{data,tableName,viewType,currentMonthPair,onSyncStatus}=_ref;const[editingCell,setEditingCell]=useState(null);const[showExportModal,setShowExportModal]=useState(false);const[exportLoading,setExportLoading]=useState(false);const[syncErrors,setSyncErrors]=useState([]);const[showRetryModal,setShowRetryModal]=useState(false);const[pendingChangesCount,setPendingChangesCount]=useState(0);// 防抖保存相关\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);const retryTimeoutRef=useRef(null);// 检查待同步更改数量\nuseEffect(()=>{const updatePendingCount=()=>{const count=moduleSixService.module6_getPendingChangesCount();setPendingChangesCount(count);};updatePendingCount();const interval=setInterval(updatePendingCount,5000);// 每5秒检查一次\nreturn()=>clearInterval(interval);},[]);// 防抖保存函数 - 增强错误处理\nconst debouncedSave=useCallback(async(rowIndex,field,value)=>{try{onSyncStatus('同步中...');// 取消之前的保存操作\nif(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}// 创建新的保存操作\nconst controller=new AbortController();pendingSaveRef.current=controller;// 调用服务保存数据\nconst syncResult=await moduleSixService.module6_updateCellData(tableName,viewType,rowIndex,field,value);// 如果没有被取消，更新状态\nif(!controller.signal.aborted){if(syncResult.backendSync){onSyncStatus('同步成功');setTimeout(()=>onSyncStatus('已同步'),1000);}else if(syncResult.localSync){onSyncStatus('已保存到本地');setTimeout(()=>onSyncStatus('等待同步'),2000);// 更新待同步计数\nsetPendingChangesCount(prev=>prev+1);// 设置自动重试\nif(retryTimeoutRef.current){clearTimeout(retryTimeoutRef.current);}retryTimeoutRef.current=setTimeout(()=>{handleRetrySync();},10000);// 10秒后自动重试\n}else{throw new Error('保存失败');}pendingSaveRef.current=null;}}catch(error){if(error.name!=='AbortError'){console.error('保存失败:',error);// 添加到错误列表\nsetSyncErrors(prev=>[...prev,{id:Date.now(),message:error.message||'保存失败',field:\"\".concat(tableName,\".\").concat(viewType,\"[\").concat(rowIndex,\"].\").concat(field),value:value,timestamp:new Date().toISOString()}]);onSyncStatus(\"\\u540C\\u6B65\\u5931\\u8D25: \".concat(error.message));setTimeout(()=>onSyncStatus('同步失败'),3000);}}},[tableName,viewType,onSyncStatus]);// 处理重试同步\nconst handleRetrySync=useCallback(async()=>{try{onSyncStatus('重试同步中...');const result=await moduleSixService.module6_retrySyncPendingChanges();if(result.success){setPendingChangesCount(result.remainingCount);if(result.syncedCount>0){onSyncStatus(\"\\u6210\\u529F\\u540C\\u6B65 \".concat(result.syncedCount,\" \\u9879\\u66F4\\u6539\"));setTimeout(()=>onSyncStatus('已同步'),2000);}else if(result.remainingCount===0){onSyncStatus('所有更改已同步');setTimeout(()=>onSyncStatus('已同步'),2000);}else{onSyncStatus(\"\\u4ECD\\u6709 \".concat(result.remainingCount,\" \\u9879\\u5F85\\u540C\\u6B65\"));setTimeout(()=>onSyncStatus('等待同步'),2000);}}else{throw new Error('重试同步失败');}}catch(error){console.error('重试同步失败:',error);onSyncStatus('重试失败');setTimeout(()=>onSyncStatus('同步失败'),2000);}},[onSyncStatus]);// 清除错误\nconst clearErrors=useCallback(()=>{setSyncErrors([]);},[]);// 清除所有待同步更改\nconst clearPendingChanges=useCallback(()=>{if(window.confirm('确定要清除所有待同步的更改吗？这将丢失未同步的数据。')){moduleSixService.module6_clearPendingChanges();setPendingChangesCount(0);onSyncStatus('已清除待同步更改');setTimeout(()=>onSyncStatus('已同步'),2000);}},[onSyncStatus]);// 处理单元格编辑\nconst handleCellEdit=(rowIndex,field,value)=>{// 清除之前的定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的防抖定时器\nsaveTimeoutRef.current=setTimeout(()=>{debouncedSave(rowIndex,field,value);},500);};// 开始编辑\nconst startEdit=(rowIndex,field)=>{setEditingCell({rowIndex,field});};// 完成编辑\nconst finishEdit=()=>{setEditingCell(null);};// 处理导出\nconst handleExport=async exportConfig=>{setExportLoading(true);try{await moduleSixService.module6_exportData(tableName,viewType,exportConfig);onSyncStatus('导出成功');}catch(error){console.error('导出失败:',error);onSyncStatus('导出失败');}setExportLoading(false);setShowExportModal(false);};// 渲染月份列头\nconst renderMonthHeaders=()=>{return currentMonthPair.map(month=>/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsxs(\"th\",{className:\"module6_month-header\",children:[month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"module6_month-header\",children:[month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"module6_month-header\",children:[month,\"\\u6708\\u8BC4\\u5206\"]})]},month));};// 渲染月份数据 - 优化版本\nconst renderMonthData=(row,rowIndex)=>{return currentMonthPair.map(month=>{var _row,_row3,_row4,_row5,_row6,_row7,_row9,_row0,_row1,_row10,_row11,_row13,_row14,_row15,_row16;return/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsxs(\"td\",{className:\"module6_month-cell \".concat((_row=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])!==null&&_row!==void 0&&_row.isReadOnly?'readonly':'editable',\" \").concat(row._isMatched?'matched':'unmatched'),onClick:()=>{var _row2;return!((_row2=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])!==null&&_row2!==void 0&&_row2.isReadOnly)&&startEdit(rowIndex,\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\"));},title:(_row3=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])!==null&&_row3!==void 0&&_row3.isReadOnly?\"\\u6570\\u636E\\u6765\\u6E90: \".concat(row._matchedSource||'源表'):'可编辑字段',children:[(editingCell===null||editingCell===void 0?void 0:editingCell.rowIndex)===rowIndex&&(editingCell===null||editingCell===void 0?void 0:editingCell.field)===\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")?/*#__PURE__*/_jsx(\"textarea\",{className:\"module6_cell-textarea\",value:((_row4=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])===null||_row4===void 0?void 0:_row4.value)||'',onChange:e=>handleCellEdit(rowIndex,\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\"),e.target.value),onBlur:finishEdit,autoFocus:true,rows:3}):/*#__PURE__*/_jsx(\"div\",{className:\"module6_cell-content\",children:renderCellContent((_row5=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])===null||_row5===void 0?void 0:_row5.value)}),((_row6=row[\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\")])===null||_row6===void 0?void 0:_row6.isReadOnly)&&/*#__PURE__*/_jsx(\"div\",{className:\"module6_readonly-indicator\",title:\"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",children:\"\\uD83D\\uDD12\"})]}),/*#__PURE__*/_jsxs(\"td\",{className:\"module6_month-cell \".concat((_row7=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])!==null&&_row7!==void 0&&_row7.isReadOnly?'readonly':'editable',\" \").concat(row._isMatched?'matched':'unmatched'),onClick:()=>{var _row8;return!((_row8=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])!==null&&_row8!==void 0&&_row8.isReadOnly)&&startEdit(rowIndex,\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"));},title:(_row9=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])!==null&&_row9!==void 0&&_row9.isReadOnly?\"\\u6570\\u636E\\u6765\\u6E90: \".concat(row._matchedSource||'源表'):'可编辑字段',children:[(editingCell===null||editingCell===void 0?void 0:editingCell.rowIndex)===rowIndex&&(editingCell===null||editingCell===void 0?void 0:editingCell.field)===\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")?/*#__PURE__*/_jsx(\"textarea\",{className:\"module6_cell-textarea\",value:((_row0=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])===null||_row0===void 0?void 0:_row0.value)||'',onChange:e=>handleCellEdit(rowIndex,\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),e.target.value),onBlur:finishEdit,autoFocus:true,rows:3}):/*#__PURE__*/_jsx(\"div\",{className:\"module6_cell-content\",children:renderCellContent((_row1=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])===null||_row1===void 0?void 0:_row1.value)}),((_row10=row[\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")])===null||_row10===void 0?void 0:_row10.isReadOnly)&&/*#__PURE__*/_jsx(\"div\",{className:\"module6_readonly-indicator\",title:\"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",children:\"\\uD83D\\uDD12\"})]}),/*#__PURE__*/_jsxs(\"td\",{className:\"module6_month-cell \".concat((_row11=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])!==null&&_row11!==void 0&&_row11.isReadOnly?'readonly':'editable',\" \").concat(row._isMatched?'matched':'unmatched'),onClick:()=>{var _row12;return!((_row12=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])!==null&&_row12!==void 0&&_row12.isReadOnly)&&startEdit(rowIndex,\"\".concat(month,\"\\u6708\\u8BC4\\u5206\"));},title:(_row13=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])!==null&&_row13!==void 0&&_row13.isReadOnly?\"\\u6570\\u636E\\u6765\\u6E90: \".concat(row._matchedSource||'源表'):'可编辑字段',children:[(editingCell===null||editingCell===void 0?void 0:editingCell.rowIndex)===rowIndex&&(editingCell===null||editingCell===void 0?void 0:editingCell.field)===\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")?/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",min:\"0\",max:\"100\",className:\"module6_cell-input\",value:((_row14=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])===null||_row14===void 0?void 0:_row14.value)||'',onChange:e=>handleCellEdit(rowIndex,\"\".concat(month,\"\\u6708\\u8BC4\\u5206\"),e.target.value),onBlur:finishEdit,autoFocus:true}):/*#__PURE__*/_jsx(\"div\",{className:\"module6_cell-content\",children:formatValue((_row15=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])===null||_row15===void 0?void 0:_row15.value,false,false)}),((_row16=row[\"\".concat(month,\"\\u6708\\u8BC4\\u5206\")])===null||_row16===void 0?void 0:_row16.isReadOnly)&&/*#__PURE__*/_jsx(\"div\",{className:\"module6_readonly-indicator\",title:\"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",children:\"\\uD83D\\uDD12\"})]})]},month);});};// 渲染单元格内容 - 支持换行显示和数字格式化\nconst renderCellContent=value=>{if(!value)return'';// 首先格式化数字\nconst formattedValue=formatValue(value,false,false);// 将格式化后的值转换为字符串\nconst stringValue=String(formattedValue);// 如果内容包含换行符，则分行显示\nif(stringValue.includes('\\n')||stringValue.includes('\\r\\n')){return stringValue.split(/\\r\\n|\\n|\\r/).map((line,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"module6_cell-line\",children:line},index));}// 如果内容过长，自动换行\nif(stringValue.length>50){return/*#__PURE__*/_jsx(\"div\",{className:\"module6_cell-long-content\",children:stringValue});}return stringValue;};// 格式化数值显示 - 修复长小数问题\nconst formatValue=function(value){let isPercentage=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;let excludeFromFormatting=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(excludeFromFormatting||value===''||value===null||value===undefined){return value;}// 处理百分比\nif(isPercentage&&typeof value==='number'){return(value*100).toFixed(2)+'%';}// 处理数值类型\nif(typeof value==='number'){// 检查是否为整数或接近整数（避免浮点精度问题）\nif(Number.isInteger(value)||Math.abs(value-Math.round(value))<0.0001){return Math.round(value).toString();}// 小数保留两位小数\nreturn Number(value.toFixed(2)).toString();}// 检查字符串是否为纯数值\nconst stringValue=String(value).trim();// 如果是纯数字字符串（包括科学计数法）\nif(/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(stringValue)){const numValue=parseFloat(stringValue);// 检查是否为有效数字\nif(!isNaN(numValue)&&isFinite(numValue)){// 检查是否为整数或接近整数\nif(Number.isInteger(numValue)||Math.abs(numValue-Math.round(numValue))<0.0001){return Math.round(numValue).toString();}// 小数保留两位小数，并移除末尾的0\nreturn Number(numValue.toFixed(2)).toString();}}// 检查是否包含百分号\nif(stringValue.includes('%')){const numPart=stringValue.replace('%','').trim();if(/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(numPart)){const numValue=parseFloat(numPart);if(!isNaN(numValue)&&isFinite(numValue)){return Number(numValue.toFixed(2)).toString()+'%';}}}// 特殊处理：如果字符串包含数字但不是纯数字（如 \"88.8%=32/36\"）\n// 检查是否以数字开头\nconst numberMatch=stringValue.match(/^(-?\\d+(?:\\.\\d+)?)/);if(numberMatch){const leadingNumber=parseFloat(numberMatch[1]);if(!isNaN(leadingNumber)&&isFinite(leadingNumber)){// 如果是整数或接近整数，格式化为整数\nif(Number.isInteger(leadingNumber)||Math.abs(leadingNumber-Math.round(leadingNumber))<0.0001){return stringValue.replace(numberMatch[1],Math.round(leadingNumber).toString());}// 否则保留两位小数\nreturn stringValue.replace(numberMatch[1],Number(leadingNumber.toFixed(2)).toString());}}// 非数值内容直接返回\nreturn value;};// 合并单元格逻辑\nconst getMergedInfo=()=>{const mergedInfo={};if(!data||data.length===0)return mergedInfo;let currentSequenceSpan=1;let currentIndicatorSpan=1;let sequenceStartIndex=0;let indicatorStartIndex=0;for(let i=1;i<data.length;i++){const currentRow=data[i];const prevRow=data[i-1];// 检查序号是否相同\nif(currentRow.序号===prevRow.序号){currentSequenceSpan++;}else{// 保存前一组的合并信息\nif(currentSequenceSpan>1){for(let j=sequenceStartIndex;j<i;j++){mergedInfo[\"\".concat(j,\"_\\u5E8F\\u53F7\")]={rowSpan:j===sequenceStartIndex?currentSequenceSpan:0,isFirst:j===sequenceStartIndex};}}currentSequenceSpan=1;sequenceStartIndex=i;}// 检查指标是否相同\nif(currentRow.指标===prevRow.指标){currentIndicatorSpan++;}else{// 保存前一组的合并信息\nif(currentIndicatorSpan>1){for(let j=indicatorStartIndex;j<i;j++){mergedInfo[\"\".concat(j,\"_\\u6307\\u6807\")]={rowSpan:j===indicatorStartIndex?currentIndicatorSpan:0,isFirst:j===indicatorStartIndex};}}currentIndicatorSpan=1;indicatorStartIndex=i;}}// 处理最后一组\nif(currentSequenceSpan>1){for(let j=sequenceStartIndex;j<data.length;j++){mergedInfo[\"\".concat(j,\"_\\u5E8F\\u53F7\")]={rowSpan:j===sequenceStartIndex?currentSequenceSpan:0,isFirst:j===sequenceStartIndex};}}if(currentIndicatorSpan>1){for(let j=indicatorStartIndex;j<data.length;j++){mergedInfo[\"\".concat(j,\"_\\u6307\\u6807\")]={rowSpan:j===indicatorStartIndex?currentIndicatorSpan:0,isFirst:j===indicatorStartIndex};}}return mergedInfo;};const mergedInfo=getMergedInfo();// 渲染表头\nconst renderHeaders=()=>{// 关键指标和重点工作都使用相同的表头结构\nreturn/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{children:viewType==='keyWork'?'重点工作项目':'指标'}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u76EE\\u6807\\u503C\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u6743\\u91CD\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u6307\\u6807\\u5206\\u7C7B\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u8DDF\\u8E2A\\u9891\\u6B21\"}),renderMonthHeaders()]});};// 渲染表格行\nconst renderRows=()=>{if(!data||data.length===0){return/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:6+currentMonthPair.length*3,children:\"\\u5F53\\u524D\\u89C6\\u56FE\\u6CA1\\u6709\\u53EF\\u663E\\u793A\\u7684\\u6570\\u636E\\u3002\"})});}return data.map((row,rowIndex)=>{const sequenceMergeInfo=mergedInfo[\"\".concat(rowIndex,\"_\\u5E8F\\u53F7\")];const indicatorMergeInfo=mergedInfo[\"\".concat(rowIndex,\"_\\u6307\\u6807\")];return/*#__PURE__*/_jsxs(\"tr\",{children:[(!sequenceMergeInfo||sequenceMergeInfo.rowSpan>0)&&/*#__PURE__*/_jsx(\"td\",{className:\"module6_sequence-cell\",rowSpan:(sequenceMergeInfo===null||sequenceMergeInfo===void 0?void 0:sequenceMergeInfo.rowSpan)||1,children:row.序号}),(!indicatorMergeInfo||indicatorMergeInfo.rowSpan>0)&&/*#__PURE__*/_jsx(\"td\",{className:\"module6_indicator-cell\",rowSpan:(indicatorMergeInfo===null||indicatorMergeInfo===void 0?void 0:indicatorMergeInfo.rowSpan)||1,children:row.指标}),/*#__PURE__*/_jsx(\"td\",{className:\"module6_target-cell\",children:formatValue(row.目标值,false,true)}),/*#__PURE__*/_jsx(\"td\",{className:\"module6_weight-cell\",children:row.权重}),/*#__PURE__*/_jsx(\"td\",{className:\"module6_category-cell\",children:formatValue(row.指标分类,false,true)}),/*#__PURE__*/_jsx(\"td\",{className:\"module6_frequency-cell\",children:formatValue(row.跟踪频次,false,true)}),renderMonthData(row,rowIndex)]},rowIndex);});};return/*#__PURE__*/_jsxs(\"table\",{className:\"module6_kpi-table\",children:[(pendingChangesCount>0||syncErrors.length>0)&&/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsx(\"tr\",{className:\"module6_sync-toolbar\",children:/*#__PURE__*/_jsx(\"td\",{colSpan:6+currentMonthPair.length*3,children:/*#__PURE__*/_jsxs(\"div\",{className:\"module6_toolbar-content\",children:[pendingChangesCount>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"module6_pending-changes\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"module6_pending-count\",children:[\"\\uD83D\\uDCE4 \",pendingChangesCount,\" \\u9879\\u5F85\\u540C\\u6B65\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_retry-btn\",onClick:handleRetrySync,title:\"\\u91CD\\u8BD5\\u540C\\u6B65\",children:\"\\uD83D\\uDD04 \\u91CD\\u8BD5\"}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_clear-btn\",onClick:clearPendingChanges,title:\"\\u6E05\\u9664\\u5F85\\u540C\\u6B65\\u66F4\\u6539\",children:\"\\uD83D\\uDDD1\\uFE0F \\u6E05\\u9664\"})]}),syncErrors.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"module6_sync-errors\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"module6_error-count\",children:[\"\\u26A0\\uFE0F \",syncErrors.length,\" \\u4E2A\\u9519\\u8BEF\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_clear-errors-btn\",onClick:clearErrors,title:\"\\u6E05\\u9664\\u9519\\u8BEF\",children:\"\\u2716\\uFE0F \\u6E05\\u9664\\u9519\\u8BEF\"}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_show-errors-btn\",onClick:()=>setShowRetryModal(true),title:\"\\u67E5\\u770B\\u9519\\u8BEF\\u8BE6\\u60C5\",children:\"\\uD83D\\uDCCB \\u8BE6\\u60C5\"})]})]})})})}),/*#__PURE__*/_jsx(\"thead\",{children:renderHeaders()}),/*#__PURE__*/_jsx(\"tbody\",{children:renderRows()})]});};export default KPITable;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "moduleSixService", "ExportModal", "jsxs", "_jsxs", "jsx", "_jsx", "KPITable", "_ref", "data", "tableName", "viewType", "currentMonthPair", "onSyncStatus", "editingCell", "setEditingCell", "showExportModal", "setShowExportModal", "exportLoading", "setExportLoading", "syncErrors", "setSyncErrors", "showRetryModal", "setShowRetryModal", "pendingChangesCount", "setPendingChangesCount", "saveTimeoutRef", "pendingSaveRef", "retryTimeoutRef", "updatePendingCount", "count", "module6_getPendingChangesCount", "interval", "setInterval", "clearInterval", "debouncedSave", "rowIndex", "field", "value", "current", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "controller", "AbortController", "syncResult", "module6_updateCellData", "signal", "aborted", "backendSync", "setTimeout", "localSync", "prev", "clearTimeout", "handleRetrySync", "Error", "error", "name", "console", "id", "Date", "now", "message", "concat", "timestamp", "toISOString", "result", "module6_retrySyncPendingChanges", "success", "remainingCount", "syncedCount", "clearErrors", "clearPendingChanges", "window", "confirm", "module6_clearPendingChanges", "handleCellEdit", "startEdit", "finishEdit", "handleExport", "exportConfig", "module6_exportData", "renderMonthHeaders", "map", "month", "Fragment", "children", "className", "renderMonthData", "row", "_row", "_row3", "_row4", "_row5", "_row6", "_row7", "_row9", "_row0", "_row1", "_row10", "_row11", "_row13", "_row14", "_row15", "_row16", "isReadOnly", "_isMatched", "onClick", "_row2", "title", "_matchedSource", "onChange", "e", "target", "onBlur", "autoFocus", "rows", "renderCellContent", "_row8", "_row12", "type", "step", "min", "max", "formatValue", "formattedValue", "stringValue", "String", "includes", "split", "line", "index", "length", "isPercentage", "arguments", "undefined", "excludeFromFormatting", "toFixed", "Number", "isInteger", "Math", "abs", "round", "toString", "trim", "test", "numValue", "parseFloat", "isNaN", "isFinite", "numPart", "replace", "numberMatch", "match", "leadingNumber", "getMergedInfo", "mergedInfo", "currentSequenceSpan", "currentIndicatorSpan", "sequenceStartIndex", "indicatorStartIndex", "i", "currentRow", "prevRow", "序号", "j", "rowSpan", "<PERSON><PERSON><PERSON><PERSON>", "指标", "renderHeaders", "renderRows", "colSpan", "sequenceMergeInfo", "indicatorMergeInfo", "目标值", "权重", "指标分类", "跟踪频次"], "sources": ["E:/处理公司程序/重点工作网页化设计7.14/performance-tracker-web/src/模块六/components/KPITable.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/KPITable.css';\nimport moduleSixService from '../services/moduleSixService';\nimport ExportModal from './ExportModal';\n\nconst KPITable = ({ data, tableName, viewType, currentMonthPair, onSyncStatus }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const [syncErrors, setSyncErrors] = useState([]);\n  const [showRetryModal, setShowRetryModal] = useState(false);\n  const [pendingChangesCount, setPendingChangesCount] = useState(0);\n\n  // 防抖保存相关\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  const retryTimeoutRef = useRef(null);\n\n\n\n  // 检查待同步更改数量\n  useEffect(() => {\n    const updatePendingCount = () => {\n      const count = moduleSixService.module6_getPendingChangesCount();\n      setPendingChangesCount(count);\n    };\n\n    updatePendingCount();\n    const interval = setInterval(updatePendingCount, 5000); // 每5秒检查一次\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // 防抖保存函数 - 增强错误处理\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      onSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用服务保存数据\n      const syncResult = await moduleSixService.module6_updateCellData(tableName, viewType, rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        if (syncResult.backendSync) {\n          onSyncStatus('同步成功');\n          setTimeout(() => onSyncStatus('已同步'), 1000);\n        } else if (syncResult.localSync) {\n          onSyncStatus('已保存到本地');\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n\n          // 更新待同步计数\n          setPendingChangesCount(prev => prev + 1);\n\n          // 设置自动重试\n          if (retryTimeoutRef.current) {\n            clearTimeout(retryTimeoutRef.current);\n          }\n          retryTimeoutRef.current = setTimeout(() => {\n            handleRetrySync();\n          }, 10000); // 10秒后自动重试\n        } else {\n          throw new Error('保存失败');\n        }\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (error.name !== 'AbortError') {\n        console.error('保存失败:', error);\n\n        // 添加到错误列表\n        setSyncErrors(prev => [...prev, {\n          id: Date.now(),\n          message: error.message || '保存失败',\n          field: `${tableName}.${viewType}[${rowIndex}].${field}`,\n          value: value,\n          timestamp: new Date().toISOString()\n        }]);\n\n        onSyncStatus(`同步失败: ${error.message}`);\n        setTimeout(() => onSyncStatus('同步失败'), 3000);\n      }\n    }\n  }, [tableName, viewType, onSyncStatus]);\n\n  // 处理重试同步\n  const handleRetrySync = useCallback(async () => {\n    try {\n      onSyncStatus('重试同步中...');\n      const result = await moduleSixService.module6_retrySyncPendingChanges();\n\n      if (result.success) {\n        setPendingChangesCount(result.remainingCount);\n\n        if (result.syncedCount > 0) {\n          onSyncStatus(`成功同步 ${result.syncedCount} 项更改`);\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else if (result.remainingCount === 0) {\n          onSyncStatus('所有更改已同步');\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else {\n          onSyncStatus(`仍有 ${result.remainingCount} 项待同步`);\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n        }\n      } else {\n        throw new Error('重试同步失败');\n      }\n    } catch (error) {\n      console.error('重试同步失败:', error);\n      onSyncStatus('重试失败');\n      setTimeout(() => onSyncStatus('同步失败'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 清除错误\n  const clearErrors = useCallback(() => {\n    setSyncErrors([]);\n  }, []);\n\n  // 清除所有待同步更改\n  const clearPendingChanges = useCallback(() => {\n    if (window.confirm('确定要清除所有待同步的更改吗？这将丢失未同步的数据。')) {\n      moduleSixService.module6_clearPendingChanges();\n      setPendingChangesCount(0);\n      onSyncStatus('已清除待同步更改');\n      setTimeout(() => onSyncStatus('已同步'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 处理单元格编辑\n  const handleCellEdit = (rowIndex, field, value) => {\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 500);\n  };\n\n  // 开始编辑\n  const startEdit = (rowIndex, field) => {\n    setEditingCell({ rowIndex, field });\n  };\n\n  // 完成编辑\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 处理导出\n  const handleExport = async (exportConfig) => {\n    setExportLoading(true);\n    try {\n      await moduleSixService.module6_exportData(tableName, viewType, exportConfig);\n      onSyncStatus('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      onSyncStatus('导出失败');\n    }\n    setExportLoading(false);\n    setShowExportModal(false);\n  };\n\n  // 渲染月份列头\n  const renderMonthHeaders = () => {\n    return currentMonthPair.map(month => (\n      <React.Fragment key={month}>\n        <th className=\"module6_month-header\">{month}月工作计划</th>\n        <th className=\"module6_month-header\">{month}月完成情况</th>\n        <th className=\"module6_month-header\">{month}月评分</th>\n      </React.Fragment>\n    ));\n  };\n\n  // 渲染月份数据 - 优化版本\n  const renderMonthData = (row, rowIndex) => {\n    return currentMonthPair.map(month => (\n      <React.Fragment key={month}>\n        {/* 工作计划单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月工作计划`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月工作计划`]?.isReadOnly && startEdit(rowIndex, `${month}月工作计划`)}\n          title={row[`${month}月工作计划`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月工作计划` ? (\n            <textarea\n              className=\"module6_cell-textarea\"\n              value={row[`${month}月工作计划`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月工作计划`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n              rows={3}\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {renderCellContent(row[`${month}月工作计划`]?.value)}\n            </div>\n          )}\n          {row[`${month}月工作计划`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n\n        {/* 完成情况单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月完成情况`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月完成情况`]?.isReadOnly && startEdit(rowIndex, `${month}月完成情况`)}\n          title={row[`${month}月完成情况`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月完成情况` ? (\n            <textarea\n              className=\"module6_cell-textarea\"\n              value={row[`${month}月完成情况`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月完成情况`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n              rows={3}\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {renderCellContent(row[`${month}月完成情况`]?.value)}\n            </div>\n          )}\n          {row[`${month}月完成情况`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n\n        {/* 评分单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月评分`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月评分`]?.isReadOnly && startEdit(rowIndex, `${month}月评分`)}\n          title={row[`${month}月评分`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月评分` ? (\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              max=\"100\"\n              className=\"module6_cell-input\"\n              value={row[`${month}月评分`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月评分`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {formatValue(row[`${month}月评分`]?.value, false, false)}\n            </div>\n          )}\n          {row[`${month}月评分`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n      </React.Fragment>\n    ));\n  };\n\n  // 渲染单元格内容 - 支持换行显示和数字格式化\n  const renderCellContent = (value) => {\n    if (!value) return '';\n\n    // 首先格式化数字\n    const formattedValue = formatValue(value, false, false);\n\n    // 将格式化后的值转换为字符串\n    const stringValue = String(formattedValue);\n\n    // 如果内容包含换行符，则分行显示\n    if (stringValue.includes('\\n') || stringValue.includes('\\r\\n')) {\n      return stringValue.split(/\\r\\n|\\n|\\r/).map((line, index) => (\n        <div key={index} className=\"module6_cell-line\">\n          {line}\n        </div>\n      ));\n    }\n\n    // 如果内容过长，自动换行\n    if (stringValue.length > 50) {\n      return (\n        <div className=\"module6_cell-long-content\">\n          {stringValue}\n        </div>\n      );\n    }\n\n    return stringValue;\n  };\n\n  // 格式化数值显示 - 修复长小数问题\n  const formatValue = (value, isPercentage = false, excludeFromFormatting = false) => {\n    if (excludeFromFormatting || value === '' || value === null || value === undefined) {\n      return value;\n    }\n\n    // 处理百分比\n    if (isPercentage && typeof value === 'number') {\n      return (value * 100).toFixed(2) + '%';\n    }\n\n    // 处理数值类型\n    if (typeof value === 'number') {\n      // 检查是否为整数或接近整数（避免浮点精度问题）\n      if (Number.isInteger(value) || Math.abs(value - Math.round(value)) < 0.0001) {\n        return Math.round(value).toString();\n      }\n      // 小数保留两位小数\n      return Number(value.toFixed(2)).toString();\n    }\n\n    // 检查字符串是否为纯数值\n    const stringValue = String(value).trim();\n\n    // 如果是纯数字字符串（包括科学计数法）\n    if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(stringValue)) {\n      const numValue = parseFloat(stringValue);\n\n      // 检查是否为有效数字\n      if (!isNaN(numValue) && isFinite(numValue)) {\n        // 检查是否为整数或接近整数\n        if (Number.isInteger(numValue) || Math.abs(numValue - Math.round(numValue)) < 0.0001) {\n          return Math.round(numValue).toString();\n        }\n        // 小数保留两位小数，并移除末尾的0\n        return Number(numValue.toFixed(2)).toString();\n      }\n    }\n\n    // 检查是否包含百分号\n    if (stringValue.includes('%')) {\n      const numPart = stringValue.replace('%', '').trim();\n      if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(numPart)) {\n        const numValue = parseFloat(numPart);\n        if (!isNaN(numValue) && isFinite(numValue)) {\n          return Number(numValue.toFixed(2)).toString() + '%';\n        }\n      }\n    }\n\n    // 特殊处理：如果字符串包含数字但不是纯数字（如 \"88.8%=32/36\"）\n    // 检查是否以数字开头\n    const numberMatch = stringValue.match(/^(-?\\d+(?:\\.\\d+)?)/);\n    if (numberMatch) {\n      const leadingNumber = parseFloat(numberMatch[1]);\n      if (!isNaN(leadingNumber) && isFinite(leadingNumber)) {\n        // 如果是整数或接近整数，格式化为整数\n        if (Number.isInteger(leadingNumber) || Math.abs(leadingNumber - Math.round(leadingNumber)) < 0.0001) {\n          return stringValue.replace(numberMatch[1], Math.round(leadingNumber).toString());\n        }\n        // 否则保留两位小数\n        return stringValue.replace(numberMatch[1], Number(leadingNumber.toFixed(2)).toString());\n      }\n    }\n\n    // 非数值内容直接返回\n    return value;\n  };\n\n  // 合并单元格逻辑\n  const getMergedInfo = () => {\n    const mergedInfo = {};\n    \n    if (!data || data.length === 0) return mergedInfo;\n    \n    let currentSequenceSpan = 1;\n    let currentIndicatorSpan = 1;\n    let sequenceStartIndex = 0;\n    let indicatorStartIndex = 0;\n    \n    for (let i = 1; i < data.length; i++) {\n      const currentRow = data[i];\n      const prevRow = data[i - 1];\n      \n      // 检查序号是否相同\n      if (currentRow.序号 === prevRow.序号) {\n        currentSequenceSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentSequenceSpan > 1) {\n          for (let j = sequenceStartIndex; j < i; j++) {\n            mergedInfo[`${j}_序号`] = {\n              rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n              isFirst: j === sequenceStartIndex\n            };\n          }\n        }\n        currentSequenceSpan = 1;\n        sequenceStartIndex = i;\n      }\n      \n      // 检查指标是否相同\n      if (currentRow.指标 === prevRow.指标) {\n        currentIndicatorSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentIndicatorSpan > 1) {\n          for (let j = indicatorStartIndex; j < i; j++) {\n            mergedInfo[`${j}_指标`] = {\n              rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n              isFirst: j === indicatorStartIndex\n            };\n          }\n        }\n        currentIndicatorSpan = 1;\n        indicatorStartIndex = i;\n      }\n    }\n    \n    // 处理最后一组\n    if (currentSequenceSpan > 1) {\n      for (let j = sequenceStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_序号`] = {\n          rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n          isFirst: j === sequenceStartIndex\n        };\n      }\n    }\n    \n    if (currentIndicatorSpan > 1) {\n      for (let j = indicatorStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_指标`] = {\n          rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n          isFirst: j === indicatorStartIndex\n        };\n      }\n    }\n    \n    return mergedInfo;\n  };\n\n  const mergedInfo = getMergedInfo();\n\n\n\n  // 渲染表头\n  const renderHeaders = () => {\n    // 关键指标和重点工作都使用相同的表头结构\n    return (\n      <tr>\n        <th>序号</th>\n        <th>{viewType === 'keyWork' ? '重点工作项目' : '指标'}</th>\n        <th>目标值</th>\n        <th>权重</th>\n        <th>指标分类</th>\n        <th>跟踪频次</th>\n        {renderMonthHeaders()}\n      </tr>\n    );\n  };\n\n  // 渲染表格行\n  const renderRows = () => {\n    if (!data || data.length === 0) {\n      return (\n        <tr>\n          <td colSpan={6 + currentMonthPair.length * 3}>\n            当前视图没有可显示的数据。\n          </td>\n        </tr>\n      );\n    }\n\n    return data.map((row, rowIndex) => {\n      const sequenceMergeInfo = mergedInfo[`${rowIndex}_序号`];\n      const indicatorMergeInfo = mergedInfo[`${rowIndex}_指标`];\n      \n      return (\n        <tr key={rowIndex}>\n          {/* 序号列 - 支持合并 */}\n          {(!sequenceMergeInfo || sequenceMergeInfo.rowSpan > 0) && (\n            <td \n              className=\"module6_sequence-cell\"\n              rowSpan={sequenceMergeInfo?.rowSpan || 1}\n            >\n              {row.序号}\n            </td>\n          )}\n          \n          {/* 指标列 - 支持合并 */}\n          {(!indicatorMergeInfo || indicatorMergeInfo.rowSpan > 0) && (\n            <td \n              className=\"module6_indicator-cell\"\n              rowSpan={indicatorMergeInfo?.rowSpan || 1}\n            >\n              {row.指标}\n            </td>\n          )}\n          \n          <td className=\"module6_target-cell\">{formatValue(row.目标值, false, true)}</td>\n          <td className=\"module6_weight-cell\">{row.权重}</td>\n          <td className=\"module6_category-cell\">{formatValue(row.指标分类, false, true)}</td>\n          <td className=\"module6_frequency-cell\">{formatValue(row.跟踪频次, false, true)}</td>\n          {renderMonthData(row, rowIndex)}\n        </tr>\n      );\n    });\n  };\n\n  return (\n    <table className=\"module6_kpi-table\">\n      {/* 同步状态和错误处理工具栏 - 作为表格的一部分 */}\n      {(pendingChangesCount > 0 || syncErrors.length > 0) && (\n        <thead>\n          <tr className=\"module6_sync-toolbar\">\n            <td colSpan={6 + currentMonthPair.length * 3}>\n              <div className=\"module6_toolbar-content\">\n                {pendingChangesCount > 0 && (\n                  <div className=\"module6_pending-changes\">\n                    <span className=\"module6_pending-count\">\n                      📤 {pendingChangesCount} 项待同步\n                    </span>\n                    <button\n                      className=\"module6_retry-btn\"\n                      onClick={handleRetrySync}\n                      title=\"重试同步\"\n                    >\n                      🔄 重试\n                    </button>\n                    <button\n                      className=\"module6_clear-btn\"\n                      onClick={clearPendingChanges}\n                      title=\"清除待同步更改\"\n                    >\n                      🗑️ 清除\n                    </button>\n                  </div>\n                )}\n\n                {syncErrors.length > 0 && (\n                  <div className=\"module6_sync-errors\">\n                    <span className=\"module6_error-count\">\n                      ⚠️ {syncErrors.length} 个错误\n                    </span>\n                    <button\n                      className=\"module6_clear-errors-btn\"\n                      onClick={clearErrors}\n                      title=\"清除错误\"\n                    >\n                      ✖️ 清除错误\n                    </button>\n                    <button\n                      className=\"module6_show-errors-btn\"\n                      onClick={() => setShowRetryModal(true)}\n                      title=\"查看错误详情\"\n                    >\n                      📋 详情\n                    </button>\n                  </div>\n                )}\n              </div>\n            </td>\n          </tr>\n        </thead>\n      )}\n      \n      <thead>\n        {renderHeaders()}\n      </thead>\n      <tbody>\n        {renderRows()}\n      </tbody>\n\n    </table>\n  );\n};\n\nexport default KPITable; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,wBAAwB,CAC/B,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAC3D,MAAO,CAAAC,WAAW,KAAM,eAAe,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAExC,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAmE,IAAlE,CAAEC,IAAI,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,gBAAgB,CAAEC,YAAa,CAAC,CAAAL,IAAA,CAC7E,KAAM,CAACM,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACmB,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACqB,aAAa,CAAEC,gBAAgB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC2B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG5B,QAAQ,CAAC,CAAC,CAAC,CAEjE;AACA,KAAM,CAAA6B,cAAc,CAAG3B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA4B,cAAc,CAAG5B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA6B,eAAe,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAIpC;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,KAAK,CAAG7B,gBAAgB,CAAC8B,8BAA8B,CAAC,CAAC,CAC/DN,sBAAsB,CAACK,KAAK,CAAC,CAC/B,CAAC,CAEDD,kBAAkB,CAAC,CAAC,CACpB,KAAM,CAAAG,QAAQ,CAAGC,WAAW,CAACJ,kBAAkB,CAAE,IAAI,CAAC,CAAE;AAExD,MAAO,IAAMK,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,aAAa,CAAGnC,WAAW,CAAC,MAAOoC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CAClE,GAAI,CACFzB,YAAY,CAAC,QAAQ,CAAC,CAEtB;AACA,GAAIc,cAAc,CAACY,OAAO,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAAd,cAAc,CAACY,OAAO,EAACG,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAG,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxClB,cAAc,CAACY,OAAO,CAAGK,UAAU,CAEnC;AACA,KAAM,CAAAE,UAAU,CAAG,KAAM,CAAA7C,gBAAgB,CAAC8C,sBAAsB,CAACrC,SAAS,CAAEC,QAAQ,CAAEyB,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE7G;AACA,GAAI,CAACM,UAAU,CAACI,MAAM,CAACC,OAAO,CAAE,CAC9B,GAAIH,UAAU,CAACI,WAAW,CAAE,CAC1BrC,YAAY,CAAC,MAAM,CAAC,CACpBsC,UAAU,CAAC,IAAMtC,YAAY,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC7C,CAAC,IAAM,IAAIiC,UAAU,CAACM,SAAS,CAAE,CAC/BvC,YAAY,CAAC,QAAQ,CAAC,CACtBsC,UAAU,CAAC,IAAMtC,YAAY,CAAC,MAAM,CAAC,CAAE,IAAI,CAAC,CAE5C;AACAY,sBAAsB,CAAC4B,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAExC;AACA,GAAIzB,eAAe,CAACW,OAAO,CAAE,CAC3Be,YAAY,CAAC1B,eAAe,CAACW,OAAO,CAAC,CACvC,CACAX,eAAe,CAACW,OAAO,CAAGY,UAAU,CAAC,IAAM,CACzCI,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,KAAK,CAAC,CAAE;AACb,CAAC,IAAM,CACL,KAAM,IAAI,CAAAC,KAAK,CAAC,MAAM,CAAC,CACzB,CACA7B,cAAc,CAACY,OAAO,CAAG,IAAI,CAC/B,CACF,CAAE,MAAOkB,KAAK,CAAE,CACd,GAAIA,KAAK,CAACC,IAAI,GAAK,YAAY,CAAE,CAC/BC,OAAO,CAACF,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAE7B;AACApC,aAAa,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC9BO,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,OAAO,CAAEN,KAAK,CAACM,OAAO,EAAI,MAAM,CAChC1B,KAAK,IAAA2B,MAAA,CAAKtD,SAAS,MAAAsD,MAAA,CAAIrD,QAAQ,MAAAqD,MAAA,CAAI5B,QAAQ,OAAA4B,MAAA,CAAK3B,KAAK,CAAE,CACvDC,KAAK,CAAEA,KAAK,CACZ2B,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CACpC,CAAC,CAAC,CAAC,CAEHrD,YAAY,8BAAAmD,MAAA,CAAUP,KAAK,CAACM,OAAO,CAAE,CAAC,CACtCZ,UAAU,CAAC,IAAMtC,YAAY,CAAC,MAAM,CAAC,CAAE,IAAI,CAAC,CAC9C,CACF,CACF,CAAC,CAAE,CAACH,SAAS,CAAEC,QAAQ,CAAEE,YAAY,CAAC,CAAC,CAEvC;AACA,KAAM,CAAA0C,eAAe,CAAGvD,WAAW,CAAC,SAAY,CAC9C,GAAI,CACFa,YAAY,CAAC,UAAU,CAAC,CACxB,KAAM,CAAAsD,MAAM,CAAG,KAAM,CAAAlE,gBAAgB,CAACmE,+BAA+B,CAAC,CAAC,CAEvE,GAAID,MAAM,CAACE,OAAO,CAAE,CAClB5C,sBAAsB,CAAC0C,MAAM,CAACG,cAAc,CAAC,CAE7C,GAAIH,MAAM,CAACI,WAAW,CAAG,CAAC,CAAE,CAC1B1D,YAAY,6BAAAmD,MAAA,CAASG,MAAM,CAACI,WAAW,uBAAM,CAAC,CAC9CpB,UAAU,CAAC,IAAMtC,YAAY,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC7C,CAAC,IAAM,IAAIsD,MAAM,CAACG,cAAc,GAAK,CAAC,CAAE,CACtCzD,YAAY,CAAC,SAAS,CAAC,CACvBsC,UAAU,CAAC,IAAMtC,YAAY,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC7C,CAAC,IAAM,CACLA,YAAY,iBAAAmD,MAAA,CAAOG,MAAM,CAACG,cAAc,6BAAO,CAAC,CAChDnB,UAAU,CAAC,IAAMtC,YAAY,CAAC,MAAM,CAAC,CAAE,IAAI,CAAC,CAC9C,CACF,CAAC,IAAM,CACL,KAAM,IAAI,CAAA2C,KAAK,CAAC,QAAQ,CAAC,CAC3B,CACF,CAAE,MAAOC,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B5C,YAAY,CAAC,MAAM,CAAC,CACpBsC,UAAU,CAAC,IAAMtC,YAAY,CAAC,MAAM,CAAC,CAAE,IAAI,CAAC,CAC9C,CACF,CAAC,CAAE,CAACA,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAA2D,WAAW,CAAGxE,WAAW,CAAC,IAAM,CACpCqB,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAoD,mBAAmB,CAAGzE,WAAW,CAAC,IAAM,CAC5C,GAAI0E,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAE,CAChD1E,gBAAgB,CAAC2E,2BAA2B,CAAC,CAAC,CAC9CnD,sBAAsB,CAAC,CAAC,CAAC,CACzBZ,YAAY,CAAC,UAAU,CAAC,CACxBsC,UAAU,CAAC,IAAMtC,YAAY,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC7C,CACF,CAAC,CAAE,CAACA,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAgE,cAAc,CAAGA,CAACzC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACjD;AACA,GAAIZ,cAAc,CAACa,OAAO,CAAE,CAC1Be,YAAY,CAAC5B,cAAc,CAACa,OAAO,CAAC,CACtC,CAEA;AACAb,cAAc,CAACa,OAAO,CAAGY,UAAU,CAAC,IAAM,CACxChB,aAAa,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CACvC,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAED;AACA,KAAM,CAAAwC,SAAS,CAAGA,CAAC1C,QAAQ,CAAEC,KAAK,GAAK,CACrCtB,cAAc,CAAC,CAAEqB,QAAQ,CAAEC,KAAM,CAAC,CAAC,CACrC,CAAC,CAED;AACA,KAAM,CAAA0C,UAAU,CAAGA,CAAA,GAAM,CACvBhE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAiE,YAAY,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC3C9D,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAI,CACF,KAAM,CAAAlB,gBAAgB,CAACiF,kBAAkB,CAACxE,SAAS,CAAEC,QAAQ,CAAEsE,YAAY,CAAC,CAC5EpE,YAAY,CAAC,MAAM,CAAC,CACtB,CAAE,MAAO4C,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B5C,YAAY,CAAC,MAAM,CAAC,CACtB,CACAM,gBAAgB,CAAC,KAAK,CAAC,CACvBF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAkE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,MAAO,CAAAvE,gBAAgB,CAACwE,GAAG,CAACC,KAAK,eAC/BjF,KAAA,CAACR,KAAK,CAAC0F,QAAQ,EAAAC,QAAA,eACbnF,KAAA,OAAIoF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,EAAEF,KAAK,CAAC,gCAAK,EAAI,CAAC,cACtDjF,KAAA,OAAIoF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,EAAEF,KAAK,CAAC,gCAAK,EAAI,CAAC,cACtDjF,KAAA,OAAIoF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,EAAEF,KAAK,CAAC,oBAAG,EAAI,CAAC,GAHjCA,KAIL,CACjB,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,eAAe,CAAGA,CAACC,GAAG,CAAEtD,QAAQ,GAAK,CACzC,MAAO,CAAAxB,gBAAgB,CAACwE,GAAG,CAACC,KAAK,OAAAM,IAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA,oBAC/BrG,KAAA,CAACR,KAAK,CAAC0F,QAAQ,EAAAC,QAAA,eAEbnF,KAAA,OACEoF,SAAS,uBAAAxB,MAAA,CAAwB,CAAA2B,IAAA,CAAAD,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAM,IAAA,WAApBA,IAAA,CAAsBe,UAAU,CAAG,UAAU,CAAG,UAAU,MAAA1C,MAAA,CAAI0B,GAAG,CAACiB,UAAU,CAAG,SAAS,CAAG,WAAW,CAAG,CAC1IC,OAAO,CAAEA,CAAA,QAAAC,KAAA,OAAM,GAAAA,KAAA,CAACnB,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAwB,KAAA,WAApBA,KAAA,CAAsBH,UAAU,GAAI5B,SAAS,CAAC1C,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,kCAAO,CAAC,EAAC,CACzFyB,KAAK,CAAE,CAAAlB,KAAA,CAAAF,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAO,KAAA,WAApBA,KAAA,CAAsBc,UAAU,8BAAA1C,MAAA,CAAY0B,GAAG,CAACqB,cAAc,EAAI,IAAI,EAAK,OAAQ,CAAAxB,QAAA,EAEzF,CAAAzE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsB,QAAQ,IAAKA,QAAQ,EAAI,CAAAtB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuB,KAAK,OAAA2B,MAAA,CAAQqB,KAAK,kCAAO,cAC3E/E,IAAA,aACEkF,SAAS,CAAC,uBAAuB,CACjClD,KAAK,CAAE,EAAAuD,KAAA,CAAAH,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAQ,KAAA,iBAApBA,KAAA,CAAsBvD,KAAK,GAAI,EAAG,CACzC0E,QAAQ,CAAGC,CAAC,EAAKpC,cAAc,CAACzC,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,mCAAS4B,CAAC,CAACC,MAAM,CAAC5E,KAAK,CAAE,CAC3E6E,MAAM,CAAEpC,UAAW,CACnBqC,SAAS,MACTC,IAAI,CAAE,CAAE,CACT,CAAC,cAEF/G,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAClC+B,iBAAiB,EAAAxB,KAAA,CAACJ,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAS,KAAA,iBAApBA,KAAA,CAAsBxD,KAAK,CAAC,CAC5C,CACN,CACA,EAAAyD,KAAA,CAAAL,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAU,KAAA,iBAApBA,KAAA,CAAsBW,UAAU,gBAC/BpG,IAAA,QAAKkF,SAAS,CAAC,4BAA4B,CAACsB,KAAK,CAAC,0EAAc,CAAAvB,QAAA,CAAC,cAEjE,CAAK,CACN,EACC,CAAC,cAGLnF,KAAA,OACEoF,SAAS,uBAAAxB,MAAA,CAAwB,CAAAgC,KAAA,CAAAN,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAW,KAAA,WAApBA,KAAA,CAAsBU,UAAU,CAAG,UAAU,CAAG,UAAU,MAAA1C,MAAA,CAAI0B,GAAG,CAACiB,UAAU,CAAG,SAAS,CAAG,WAAW,CAAG,CAC1IC,OAAO,CAAEA,CAAA,QAAAW,KAAA,OAAM,GAAAA,KAAA,CAAC7B,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAkC,KAAA,WAApBA,KAAA,CAAsBb,UAAU,GAAI5B,SAAS,CAAC1C,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,kCAAO,CAAC,EAAC,CACzFyB,KAAK,CAAE,CAAAb,KAAA,CAAAP,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAY,KAAA,WAApBA,KAAA,CAAsBS,UAAU,8BAAA1C,MAAA,CAAY0B,GAAG,CAACqB,cAAc,EAAI,IAAI,EAAK,OAAQ,CAAAxB,QAAA,EAEzF,CAAAzE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsB,QAAQ,IAAKA,QAAQ,EAAI,CAAAtB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuB,KAAK,OAAA2B,MAAA,CAAQqB,KAAK,kCAAO,cAC3E/E,IAAA,aACEkF,SAAS,CAAC,uBAAuB,CACjClD,KAAK,CAAE,EAAA4D,KAAA,CAAAR,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAa,KAAA,iBAApBA,KAAA,CAAsB5D,KAAK,GAAI,EAAG,CACzC0E,QAAQ,CAAGC,CAAC,EAAKpC,cAAc,CAACzC,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,mCAAS4B,CAAC,CAACC,MAAM,CAAC5E,KAAK,CAAE,CAC3E6E,MAAM,CAAEpC,UAAW,CACnBqC,SAAS,MACTC,IAAI,CAAE,CAAE,CACT,CAAC,cAEF/G,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAClC+B,iBAAiB,EAAAnB,KAAA,CAACT,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAc,KAAA,iBAApBA,KAAA,CAAsB7D,KAAK,CAAC,CAC5C,CACN,CACA,EAAA8D,MAAA,CAAAV,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,mCAAQ,UAAAe,MAAA,iBAApBA,MAAA,CAAsBM,UAAU,gBAC/BpG,IAAA,QAAKkF,SAAS,CAAC,4BAA4B,CAACsB,KAAK,CAAC,0EAAc,CAAAvB,QAAA,CAAC,cAEjE,CAAK,CACN,EACC,CAAC,cAGLnF,KAAA,OACEoF,SAAS,uBAAAxB,MAAA,CAAwB,CAAAqC,MAAA,CAAAX,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAgB,MAAA,WAAlBA,MAAA,CAAoBK,UAAU,CAAG,UAAU,CAAG,UAAU,MAAA1C,MAAA,CAAI0B,GAAG,CAACiB,UAAU,CAAG,SAAS,CAAG,WAAW,CAAG,CACxIC,OAAO,CAAEA,CAAA,QAAAY,MAAA,OAAM,GAAAA,MAAA,CAAC9B,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAmC,MAAA,WAAlBA,MAAA,CAAoBd,UAAU,GAAI5B,SAAS,CAAC1C,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,sBAAK,CAAC,EAAC,CACrFyB,KAAK,CAAE,CAAAR,MAAA,CAAAZ,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAiB,MAAA,WAAlBA,MAAA,CAAoBI,UAAU,8BAAA1C,MAAA,CAAY0B,GAAG,CAACqB,cAAc,EAAI,IAAI,EAAK,OAAQ,CAAAxB,QAAA,EAEvF,CAAAzE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsB,QAAQ,IAAKA,QAAQ,EAAI,CAAAtB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuB,KAAK,OAAA2B,MAAA,CAAQqB,KAAK,sBAAK,cACzE/E,IAAA,UACEmH,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTpC,SAAS,CAAC,oBAAoB,CAC9BlD,KAAK,CAAE,EAAAiE,MAAA,CAAAb,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAkB,MAAA,iBAAlBA,MAAA,CAAoBjE,KAAK,GAAI,EAAG,CACvC0E,QAAQ,CAAGC,CAAC,EAAKpC,cAAc,CAACzC,QAAQ,IAAA4B,MAAA,CAAKqB,KAAK,uBAAO4B,CAAC,CAACC,MAAM,CAAC5E,KAAK,CAAE,CACzE6E,MAAM,CAAEpC,UAAW,CACnBqC,SAAS,MACV,CAAC,cAEF9G,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAClCsC,WAAW,EAAArB,MAAA,CAACd,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAmB,MAAA,iBAAlBA,MAAA,CAAoBlE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAClD,CACN,CACA,EAAAmE,MAAA,CAAAf,GAAG,IAAA1B,MAAA,CAAIqB,KAAK,uBAAM,UAAAoB,MAAA,iBAAlBA,MAAA,CAAoBC,UAAU,gBAC7BpG,IAAA,QAAKkF,SAAS,CAAC,4BAA4B,CAACsB,KAAK,CAAC,0EAAc,CAAAvB,QAAA,CAAC,cAEjE,CAAK,CACN,EACC,CAAC,GAnFcF,KAoFL,CAAC,EAClB,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAiC,iBAAiB,CAAIhF,KAAK,EAAK,CACnC,GAAI,CAACA,KAAK,CAAE,MAAO,EAAE,CAErB;AACA,KAAM,CAAAwF,cAAc,CAAGD,WAAW,CAACvF,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAEvD;AACA,KAAM,CAAAyF,WAAW,CAAGC,MAAM,CAACF,cAAc,CAAC,CAE1C;AACA,GAAIC,WAAW,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAIF,WAAW,CAACE,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC9D,MAAO,CAAAF,WAAW,CAACG,KAAK,CAAC,YAAY,CAAC,CAAC9C,GAAG,CAAC,CAAC+C,IAAI,CAAEC,KAAK,gBACrD9H,IAAA,QAAiBkF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAC3C4C,IAAI,EADGC,KAEL,CACN,CAAC,CACJ,CAEA;AACA,GAAIL,WAAW,CAACM,MAAM,CAAG,EAAE,CAAE,CAC3B,mBACE/H,IAAA,QAAKkF,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACvCwC,WAAW,CACT,CAAC,CAEV,CAEA,MAAO,CAAAA,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAF,WAAW,CAAG,QAAAA,CAACvF,KAAK,CAA0D,IAAxD,CAAAgG,YAAY,CAAAC,SAAA,CAAAF,MAAA,IAAAE,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,IAAE,CAAAE,qBAAqB,CAAAF,SAAA,CAAAF,MAAA,IAAAE,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CAC7E,GAAIE,qBAAqB,EAAInG,KAAK,GAAK,EAAE,EAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKkG,SAAS,CAAE,CAClF,MAAO,CAAAlG,KAAK,CACd,CAEA;AACA,GAAIgG,YAAY,EAAI,MAAO,CAAAhG,KAAK,GAAK,QAAQ,CAAE,CAC7C,MAAO,CAACA,KAAK,CAAG,GAAG,EAAEoG,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACvC,CAEA;AACA,GAAI,MAAO,CAAApG,KAAK,GAAK,QAAQ,CAAE,CAC7B;AACA,GAAIqG,MAAM,CAACC,SAAS,CAACtG,KAAK,CAAC,EAAIuG,IAAI,CAACC,GAAG,CAACxG,KAAK,CAAGuG,IAAI,CAACE,KAAK,CAACzG,KAAK,CAAC,CAAC,CAAG,MAAM,CAAE,CAC3E,MAAO,CAAAuG,IAAI,CAACE,KAAK,CAACzG,KAAK,CAAC,CAAC0G,QAAQ,CAAC,CAAC,CACrC,CACA;AACA,MAAO,CAAAL,MAAM,CAACrG,KAAK,CAACoG,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAC5C,CAEA;AACA,KAAM,CAAAjB,WAAW,CAAGC,MAAM,CAAC1F,KAAK,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAExC;AACA,GAAI,gCAAgC,CAACC,IAAI,CAACnB,WAAW,CAAC,CAAE,CACtD,KAAM,CAAAoB,QAAQ,CAAGC,UAAU,CAACrB,WAAW,CAAC,CAExC;AACA,GAAI,CAACsB,KAAK,CAACF,QAAQ,CAAC,EAAIG,QAAQ,CAACH,QAAQ,CAAC,CAAE,CAC1C;AACA,GAAIR,MAAM,CAACC,SAAS,CAACO,QAAQ,CAAC,EAAIN,IAAI,CAACC,GAAG,CAACK,QAAQ,CAAGN,IAAI,CAACE,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAG,MAAM,CAAE,CACpF,MAAO,CAAAN,IAAI,CAACE,KAAK,CAACI,QAAQ,CAAC,CAACH,QAAQ,CAAC,CAAC,CACxC,CACA;AACA,MAAO,CAAAL,MAAM,CAACQ,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAC/C,CACF,CAEA;AACA,GAAIjB,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC7B,KAAM,CAAAsB,OAAO,CAAGxB,WAAW,CAACyB,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC,CACnD,GAAI,gCAAgC,CAACC,IAAI,CAACK,OAAO,CAAC,CAAE,CAClD,KAAM,CAAAJ,QAAQ,CAAGC,UAAU,CAACG,OAAO,CAAC,CACpC,GAAI,CAACF,KAAK,CAACF,QAAQ,CAAC,EAAIG,QAAQ,CAACH,QAAQ,CAAC,CAAE,CAC1C,MAAO,CAAAR,MAAM,CAACQ,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAG,GAAG,CACrD,CACF,CACF,CAEA;AACA;AACA,KAAM,CAAAS,WAAW,CAAG1B,WAAW,CAAC2B,KAAK,CAAC,oBAAoB,CAAC,CAC3D,GAAID,WAAW,CAAE,CACf,KAAM,CAAAE,aAAa,CAAGP,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,CAChD,GAAI,CAACJ,KAAK,CAACM,aAAa,CAAC,EAAIL,QAAQ,CAACK,aAAa,CAAC,CAAE,CACpD;AACA,GAAIhB,MAAM,CAACC,SAAS,CAACe,aAAa,CAAC,EAAId,IAAI,CAACC,GAAG,CAACa,aAAa,CAAGd,IAAI,CAACE,KAAK,CAACY,aAAa,CAAC,CAAC,CAAG,MAAM,CAAE,CACnG,MAAO,CAAA5B,WAAW,CAACyB,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,CAAEZ,IAAI,CAACE,KAAK,CAACY,aAAa,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAC,CAClF,CACA;AACA,MAAO,CAAAjB,WAAW,CAACyB,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,CAAEd,MAAM,CAACgB,aAAa,CAACjB,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC,CACzF,CACF,CAEA;AACA,MAAO,CAAA1G,KAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAsH,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,UAAU,CAAG,CAAC,CAAC,CAErB,GAAI,CAACpJ,IAAI,EAAIA,IAAI,CAAC4H,MAAM,GAAK,CAAC,CAAE,MAAO,CAAAwB,UAAU,CAEjD,GAAI,CAAAC,mBAAmB,CAAG,CAAC,CAC3B,GAAI,CAAAC,oBAAoB,CAAG,CAAC,CAC5B,GAAI,CAAAC,kBAAkB,CAAG,CAAC,CAC1B,GAAI,CAAAC,mBAAmB,CAAG,CAAC,CAE3B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGzJ,IAAI,CAAC4H,MAAM,CAAE6B,CAAC,EAAE,CAAE,CACpC,KAAM,CAAAC,UAAU,CAAG1J,IAAI,CAACyJ,CAAC,CAAC,CAC1B,KAAM,CAAAE,OAAO,CAAG3J,IAAI,CAACyJ,CAAC,CAAG,CAAC,CAAC,CAE3B;AACA,GAAIC,UAAU,CAACE,EAAE,GAAKD,OAAO,CAACC,EAAE,CAAE,CAChCP,mBAAmB,EAAE,CACvB,CAAC,IAAM,CACL;AACA,GAAIA,mBAAmB,CAAG,CAAC,CAAE,CAC3B,IAAK,GAAI,CAAAQ,CAAC,CAAGN,kBAAkB,CAAEM,CAAC,CAAGJ,CAAC,CAAEI,CAAC,EAAE,CAAE,CAC3CT,UAAU,IAAA7F,MAAA,CAAIsG,CAAC,kBAAM,CAAG,CACtBC,OAAO,CAAED,CAAC,GAAKN,kBAAkB,CAAGF,mBAAmB,CAAG,CAAC,CAC3DU,OAAO,CAAEF,CAAC,GAAKN,kBACjB,CAAC,CACH,CACF,CACAF,mBAAmB,CAAG,CAAC,CACvBE,kBAAkB,CAAGE,CAAC,CACxB,CAEA;AACA,GAAIC,UAAU,CAACM,EAAE,GAAKL,OAAO,CAACK,EAAE,CAAE,CAChCV,oBAAoB,EAAE,CACxB,CAAC,IAAM,CACL;AACA,GAAIA,oBAAoB,CAAG,CAAC,CAAE,CAC5B,IAAK,GAAI,CAAAO,CAAC,CAAGL,mBAAmB,CAAEK,CAAC,CAAGJ,CAAC,CAAEI,CAAC,EAAE,CAAE,CAC5CT,UAAU,IAAA7F,MAAA,CAAIsG,CAAC,kBAAM,CAAG,CACtBC,OAAO,CAAED,CAAC,GAAKL,mBAAmB,CAAGF,oBAAoB,CAAG,CAAC,CAC7DS,OAAO,CAAEF,CAAC,GAAKL,mBACjB,CAAC,CACH,CACF,CACAF,oBAAoB,CAAG,CAAC,CACxBE,mBAAmB,CAAGC,CAAC,CACzB,CACF,CAEA;AACA,GAAIJ,mBAAmB,CAAG,CAAC,CAAE,CAC3B,IAAK,GAAI,CAAAQ,CAAC,CAAGN,kBAAkB,CAAEM,CAAC,CAAG7J,IAAI,CAAC4H,MAAM,CAAEiC,CAAC,EAAE,CAAE,CACrDT,UAAU,IAAA7F,MAAA,CAAIsG,CAAC,kBAAM,CAAG,CACtBC,OAAO,CAAED,CAAC,GAAKN,kBAAkB,CAAGF,mBAAmB,CAAG,CAAC,CAC3DU,OAAO,CAAEF,CAAC,GAAKN,kBACjB,CAAC,CACH,CACF,CAEA,GAAID,oBAAoB,CAAG,CAAC,CAAE,CAC5B,IAAK,GAAI,CAAAO,CAAC,CAAGL,mBAAmB,CAAEK,CAAC,CAAG7J,IAAI,CAAC4H,MAAM,CAAEiC,CAAC,EAAE,CAAE,CACtDT,UAAU,IAAA7F,MAAA,CAAIsG,CAAC,kBAAM,CAAG,CACtBC,OAAO,CAAED,CAAC,GAAKL,mBAAmB,CAAGF,oBAAoB,CAAG,CAAC,CAC7DS,OAAO,CAAEF,CAAC,GAAKL,mBACjB,CAAC,CACH,CACF,CAEA,MAAO,CAAAJ,UAAU,CACnB,CAAC,CAED,KAAM,CAAAA,UAAU,CAAGD,aAAa,CAAC,CAAC,CAIlC;AACA,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC1B;AACA,mBACEtK,KAAA,OAAAmF,QAAA,eACEjF,IAAA,OAAAiF,QAAA,CAAI,cAAE,CAAI,CAAC,cACXjF,IAAA,OAAAiF,QAAA,CAAK5E,QAAQ,GAAK,SAAS,CAAG,QAAQ,CAAG,IAAI,CAAK,CAAC,cACnDL,IAAA,OAAAiF,QAAA,CAAI,oBAAG,CAAI,CAAC,cACZjF,IAAA,OAAAiF,QAAA,CAAI,cAAE,CAAI,CAAC,cACXjF,IAAA,OAAAiF,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbjF,IAAA,OAAAiF,QAAA,CAAI,0BAAI,CAAI,CAAC,CACZJ,kBAAkB,CAAC,CAAC,EACnB,CAAC,CAET,CAAC,CAED;AACA,KAAM,CAAAwF,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI,CAAClK,IAAI,EAAIA,IAAI,CAAC4H,MAAM,GAAK,CAAC,CAAE,CAC9B,mBACE/H,IAAA,OAAAiF,QAAA,cACEjF,IAAA,OAAIsK,OAAO,CAAE,CAAC,CAAGhK,gBAAgB,CAACyH,MAAM,CAAG,CAAE,CAAA9C,QAAA,CAAC,gFAE9C,CAAI,CAAC,CACH,CAAC,CAET,CAEA,MAAO,CAAA9E,IAAI,CAAC2E,GAAG,CAAC,CAACM,GAAG,CAAEtD,QAAQ,GAAK,CACjC,KAAM,CAAAyI,iBAAiB,CAAGhB,UAAU,IAAA7F,MAAA,CAAI5B,QAAQ,kBAAM,CACtD,KAAM,CAAA0I,kBAAkB,CAAGjB,UAAU,IAAA7F,MAAA,CAAI5B,QAAQ,kBAAM,CAEvD,mBACEhC,KAAA,OAAAmF,QAAA,EAEG,CAAC,CAACsF,iBAAiB,EAAIA,iBAAiB,CAACN,OAAO,CAAG,CAAC,gBACnDjK,IAAA,OACEkF,SAAS,CAAC,uBAAuB,CACjC+E,OAAO,CAAE,CAAAM,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEN,OAAO,GAAI,CAAE,CAAAhF,QAAA,CAExCG,GAAG,CAAC2E,EAAE,CACL,CACL,CAGA,CAAC,CAACS,kBAAkB,EAAIA,kBAAkB,CAACP,OAAO,CAAG,CAAC,gBACrDjK,IAAA,OACEkF,SAAS,CAAC,wBAAwB,CAClC+E,OAAO,CAAE,CAAAO,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAEP,OAAO,GAAI,CAAE,CAAAhF,QAAA,CAEzCG,GAAG,CAAC+E,EAAE,CACL,CACL,cAEDnK,IAAA,OAAIkF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEsC,WAAW,CAACnC,GAAG,CAACqF,GAAG,CAAE,KAAK,CAAE,IAAI,CAAC,CAAK,CAAC,cAC5EzK,IAAA,OAAIkF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEG,GAAG,CAACsF,EAAE,CAAK,CAAC,cACjD1K,IAAA,OAAIkF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAEsC,WAAW,CAACnC,GAAG,CAACuF,IAAI,CAAE,KAAK,CAAE,IAAI,CAAC,CAAK,CAAC,cAC/E3K,IAAA,OAAIkF,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAEsC,WAAW,CAACnC,GAAG,CAACwF,IAAI,CAAE,KAAK,CAAE,IAAI,CAAC,CAAK,CAAC,CAC/EzF,eAAe,CAACC,GAAG,CAAEtD,QAAQ,CAAC,GAzBxBA,QA0BL,CAAC,CAET,CAAC,CAAC,CACJ,CAAC,CAED,mBACEhC,KAAA,UAAOoF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,EAEjC,CAAC/D,mBAAmB,CAAG,CAAC,EAAIJ,UAAU,CAACiH,MAAM,CAAG,CAAC,gBAChD/H,IAAA,UAAAiF,QAAA,cACEjF,IAAA,OAAIkF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cAClCjF,IAAA,OAAIsK,OAAO,CAAE,CAAC,CAAGhK,gBAAgB,CAACyH,MAAM,CAAG,CAAE,CAAA9C,QAAA,cAC3CnF,KAAA,QAAKoF,SAAS,CAAC,yBAAyB,CAAAD,QAAA,EACrC/D,mBAAmB,CAAG,CAAC,eACtBpB,KAAA,QAAKoF,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACtCnF,KAAA,SAAMoF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,eACnC,CAAC/D,mBAAmB,CAAC,2BAC1B,EAAM,CAAC,cACPlB,IAAA,WACEkF,SAAS,CAAC,mBAAmB,CAC7BoB,OAAO,CAAErD,eAAgB,CACzBuD,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CACb,2BAED,CAAQ,CAAC,cACTjF,IAAA,WACEkF,SAAS,CAAC,mBAAmB,CAC7BoB,OAAO,CAAEnC,mBAAoB,CAC7BqC,KAAK,CAAC,4CAAS,CAAAvB,QAAA,CAChB,iCAED,CAAQ,CAAC,EACN,CACN,CAEAnE,UAAU,CAACiH,MAAM,CAAG,CAAC,eACpBjI,KAAA,QAAKoF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClCnF,KAAA,SAAMoF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,EAAC,eACjC,CAACnE,UAAU,CAACiH,MAAM,CAAC,qBACxB,EAAM,CAAC,cACP/H,IAAA,WACEkF,SAAS,CAAC,0BAA0B,CACpCoB,OAAO,CAAEpC,WAAY,CACrBsC,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CACb,uCAED,CAAQ,CAAC,cACTjF,IAAA,WACEkF,SAAS,CAAC,yBAAyB,CACnCoB,OAAO,CAAEA,CAAA,GAAMrF,iBAAiB,CAAC,IAAI,CAAE,CACvCuF,KAAK,CAAC,sCAAQ,CAAAvB,QAAA,CACf,2BAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CACJ,CAAC,CACH,CAAC,CACA,CACR,cAEDjF,IAAA,UAAAiF,QAAA,CACGmF,aAAa,CAAC,CAAC,CACX,CAAC,cACRpK,IAAA,UAAAiF,QAAA,CACGoF,UAAU,CAAC,CAAC,CACR,CAAC,EAEH,CAAC,CAEZ,CAAC,CAED,cAAe,CAAApK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}