{"ast": null, "code": "import React,{useState,useEffect}from'react';import'../styles/HomePage.css';import GlobalFileSelector from'../../components/GlobalFileSelector';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomePage=_ref=>{let{onNavigate}=_ref;const[currentTime,setCurrentTime]=useState(new Date());const[showFileSelector,setShowFileSelector]=useState(false);const[currentFileName,setCurrentFileName]=useState('附件1：2025年开发中心重点工作跟踪表-0414.xlsx');useEffect(()=>{const timer=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timer);},[]);const navigationItems=[{id:'work-target',title:'工作目标管理责任书',subtitle:'Work Target Management',icon:'🎯',description:'关键指标·质量指标·重点工作管理',color:'#20ff4d'},{id:'work-tracking',title:'重点工作跟踪-填写表',subtitle:'Work Tracking Form',icon:'📊',description:'月度工作计划与完成情况跟踪',color:'#00d4aa'},{id:'world-class',title:'对标世界一流举措-提报版',subtitle:'World-Class Benchmarking',icon:'🌍',description:'世界一流标准对标与举措实施',color:'#4dd0ff'},{id:'monthly-kpi',title:'月度重点KPI',subtitle:'Monthly Key Performance Indicators',icon:'📈',description:'KPI指标监控与分析评估',color:'#ff6b4d'},{id:'project-one',title:'1号项目责任状',subtitle:'Project One Responsibility',icon:'🚀',description:'重点项目推进与责任落实',color:'#ff4dff'},{id:'module-six',title:'开发中心二级部门工作目标管理责任书',subtitle:'Department Target Management',icon:'🏢',description:'各部门目标管理与绩效考核',color:'#ffff4d'}];const handleNavigation=itemId=>{onNavigate(itemId);};const handleFileChanged=fileName=>{setCurrentFileName(fileName);// 可以在这里添加其他处理逻辑，比如刷新数据等\n};return/*#__PURE__*/_jsxs(\"div\",{className:\"homepage\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"background-animation\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid-lines\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-display\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"current-time\",children:currentTime.toLocaleString('zh-CN',{year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit',second:'2-digit'})}),/*#__PURE__*/_jsx(\"div\",{className:\"system-status\",children:\"\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"}),/*#__PURE__*/_jsx(\"button\",{className:\"file-selector-btn\",onClick:()=>setShowFileSelector(true),title:\"\\u66F4\\u6362Excel\\u6587\\u4EF6\",children:\"\\uD83D\\uDCC1 \\u66F4\\u6362\\u6587\\u4EF6\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"main-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"title-container\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"\\u667A\\u80FD\\u9A71\\u52A8\\xB7\\u8D28\\u6548\\u53CC\\u5347\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"sub-title\",children:\"2025\\u5F00\\u53D1\\u4E2D\\u5FC3\\u91CD\\u70B9\\u9879\\u76EE\\u6708\\u5EA6\\u7EE9\\u6548\\u8DDF\\u8E2A\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(\"div\",{className:\"title-divider\"}),/*#__PURE__*/_jsx(\"p\",{className:\"system-description\",children:\"\\u542B\\u4E16\\u754C\\u4E00\\u6D41\\u5BF9\\u6807\\u4F53\\u7CFB \\xB7 \\u667A\\u80FD\\u5316\\u6570\\u636E\\u7BA1\\u7406\\u5E73\\u53F0\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"navigation-grid\",children:navigationItems.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"nav-card\",style:{'--delay':\"\".concat(index*0.1,\"s\"),'--color':item.color},onClick:()=>handleNavigation(item.id),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-icon\",children:item.icon}),/*#__PURE__*/_jsx(\"div\",{className:\"card-number\",children:String(index+1).padStart(2,'0')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-content\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"card-title\",children:item.title}),/*#__PURE__*/_jsx(\"p\",{className:\"card-subtitle\",children:item.subtitle}),/*#__PURE__*/_jsx(\"p\",{className:\"card-description\",children:item.description})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-footer\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"enter-text\",children:\"\\u8FDB\\u5165\\u6A21\\u5757\"}),/*#__PURE__*/_jsx(\"span\",{className:\"arrow\",children:\"\\u2192\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"})]},item.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-bar\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"\\u6570\\u636E\\u540C\\u6B65\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value online\",children:\"\\u5728\\u7EBF\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"Excel\\u6587\\u4EF6:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value\",children:currentFileName})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"\\u6A21\\u5757\\u6570\\u91CF:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value\",children:navigationItems.length})]})]}),/*#__PURE__*/_jsx(GlobalFileSelector,{isVisible:showFileSelector,onClose:()=>setShowFileSelector(false),onFileChanged:handleFileChanged})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "GlobalFileSelector", "jsx", "_jsx", "jsxs", "_jsxs", "HomePage", "_ref", "onNavigate", "currentTime", "setCurrentTime", "Date", "showFileSelector", "setShowFileSelector", "currentFileName", "setCurrentFileName", "timer", "setInterval", "clearInterval", "navigationItems", "id", "title", "subtitle", "icon", "description", "color", "handleNavigation", "itemId", "handleFileChanged", "fileName", "className", "children", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "onClick", "map", "item", "index", "style", "concat", "String", "padStart", "length", "isVisible", "onClose", "onFileChanged"], "sources": ["E:/处理公司程序/重点工作网页化设计7.14/performance-tracker-web/src/模块一/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/HomePage.css';\r\nimport GlobalFileSelector from '../../components/GlobalFileSelector';\r\n\r\nconst HomePage = ({ onNavigate }) => {\r\n  const [currentTime, setCurrentTime] = useState(new Date());\r\n  const [showFileSelector, setShowFileSelector] = useState(false);\r\n  const [currentFileName, setCurrentFileName] = useState('附件1：2025年开发中心重点工作跟踪表-0414.xlsx');\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setCurrentTime(new Date());\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, []);\r\n\r\n  const navigationItems = [\r\n    {\r\n      id: 'work-target',\r\n      title: '工作目标管理责任书',\r\n      subtitle: 'Work Target Management',\r\n      icon: '🎯',\r\n      description: '关键指标·质量指标·重点工作管理',\r\n      color: '#20ff4d'\r\n    },\r\n    {\r\n      id: 'work-tracking',\r\n      title: '重点工作跟踪-填写表',\r\n      subtitle: 'Work Tracking Form',\r\n      icon: '📊',\r\n      description: '月度工作计划与完成情况跟踪',\r\n      color: '#00d4aa'\r\n    },\r\n    {\r\n      id: 'world-class',\r\n      title: '对标世界一流举措-提报版',\r\n      subtitle: 'World-Class Benchmarking',\r\n      icon: '🌍',\r\n      description: '世界一流标准对标与举措实施',\r\n      color: '#4dd0ff'\r\n    },\r\n    {\r\n      id: 'monthly-kpi',\r\n      title: '月度重点KPI',\r\n      subtitle: 'Monthly Key Performance Indicators',\r\n      icon: '📈',\r\n      description: 'KPI指标监控与分析评估',\r\n      color: '#ff6b4d'\r\n    },\r\n    {\r\n      id: 'project-one',\r\n      title: '1号项目责任状',\r\n      subtitle: 'Project One Responsibility',\r\n      icon: '🚀',\r\n      description: '重点项目推进与责任落实',\r\n      color: '#ff4dff'\r\n    },\r\n    {\r\n      id: 'module-six',\r\n      title: '开发中心二级部门工作目标管理责任书',\r\n      subtitle: 'Department Target Management',\r\n      icon: '🏢',\r\n      description: '各部门目标管理与绩效考核',\r\n      color: '#ffff4d'\r\n    }\r\n  ];\r\n\r\n  const handleNavigation = (itemId) => {\r\n    onNavigate(itemId);\r\n  };\r\n\r\n  const handleFileChanged = (fileName) => {\r\n    setCurrentFileName(fileName);\r\n    // 可以在这里添加其他处理逻辑，比如刷新数据等\r\n  };\r\n\r\n  return (\r\n    <div className=\"homepage\">\r\n      {/* 背景动画 */}\r\n      <div className=\"background-animation\">\r\n        <div className=\"particle\"></div>\r\n        <div className=\"particle\"></div>\r\n        <div className=\"particle\"></div>\r\n        <div className=\"grid-lines\"></div>\r\n      </div>\r\n\r\n      {/* 顶部时间显示 */}\r\n      <div className=\"time-display\">\r\n        <div className=\"current-time\">\r\n          {currentTime.toLocaleString('zh-CN', {\r\n            year: 'numeric',\r\n            month: '2-digit',\r\n            day: '2-digit',\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            second: '2-digit'\r\n          })}\r\n        </div>\r\n        <div className=\"system-status\">系统运行正常</div>\r\n        <button \r\n          className=\"file-selector-btn\"\r\n          onClick={() => setShowFileSelector(true)}\r\n          title=\"更换Excel文件\"\r\n        >\r\n          📁 更换文件\r\n        </button>\r\n      </div>\r\n\r\n      {/* 主标题区域 */}\r\n      <div className=\"main-header\">\r\n        <div className=\"title-container\">\r\n          <h1 className=\"main-title\">\r\n            智能驱动·质效双升\r\n          </h1>\r\n          <h2 className=\"sub-title\">\r\n            2025开发中心重点项目月度绩效跟踪报告\r\n          </h2>\r\n          <div className=\"title-divider\"></div>\r\n          <p className=\"system-description\">\r\n            含世界一流对标体系 · 智能化数据管理平台\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 导航网格 */}\r\n      <div className=\"navigation-grid\">\r\n        {navigationItems.map((item, index) => (\r\n          <div\r\n            key={item.id}\r\n            className=\"nav-card\"\r\n            style={{ '--delay': `${index * 0.1}s`, '--color': item.color }}\r\n            onClick={() => handleNavigation(item.id)}\r\n          >\r\n            <div className=\"card-header\">\r\n              <div className=\"card-icon\">{item.icon}</div>\r\n              <div className=\"card-number\">{String(index + 1).padStart(2, '0')}</div>\r\n            </div>\r\n            <div className=\"card-content\">\r\n              <h3 className=\"card-title\">{item.title}</h3>\r\n              <p className=\"card-subtitle\">{item.subtitle}</p>\r\n              <p className=\"card-description\">{item.description}</p>\r\n            </div>\r\n            <div className=\"card-footer\">\r\n              <span className=\"enter-text\">进入模块</span>\r\n              <span className=\"arrow\">→</span>\r\n            </div>\r\n            <div className=\"card-glow\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 底部状态栏 */}\r\n      <div className=\"status-bar\">\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">数据同步状态:</span>\r\n          <span className=\"status-value online\">在线</span>\r\n        </div>\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">Excel文件:</span>\r\n          <span className=\"status-value\">{currentFileName}</span>\r\n        </div>\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">模块数量:</span>\r\n          <span className=\"status-value\">{navigationItems.length}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 文件选择器 */}\r\n      <GlobalFileSelector\r\n        isVisible={showFileSelector}\r\n        onClose={() => setShowFileSelector(false)}\r\n        onFileChanged={handleFileChanged}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,wBAAwB,CAC/B,MAAO,CAAAC,kBAAkB,KAAM,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErE,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAC9B,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGX,QAAQ,CAAC,GAAI,CAAAY,IAAI,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACe,eAAe,CAAEC,kBAAkB,CAAC,CAAGhB,QAAQ,CAAC,gCAAgC,CAAC,CAExFC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgB,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9BP,cAAc,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMO,aAAa,CAACF,KAAK,CAAC,CACnC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,eAAe,CAAG,CACtB,CACEC,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,WAAW,CAClBC,QAAQ,CAAE,wBAAwB,CAClCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,kBAAkB,CAC/BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,eAAe,CACnBC,KAAK,CAAE,YAAY,CACnBC,QAAQ,CAAE,oBAAoB,CAC9BC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,eAAe,CAC5BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,cAAc,CACrBC,QAAQ,CAAE,0BAA0B,CACpCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,eAAe,CAC5BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,oCAAoC,CAC9CC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,cAAc,CAC3BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,4BAA4B,CACtCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,aAAa,CAC1BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,YAAY,CAChBC,KAAK,CAAE,mBAAmB,CAC1BC,QAAQ,CAAE,8BAA8B,CACxCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,cAAc,CAC3BC,KAAK,CAAE,SACT,CAAC,CACF,CAED,KAAM,CAAAC,gBAAgB,CAAIC,MAAM,EAAK,CACnCnB,UAAU,CAACmB,MAAM,CAAC,CACpB,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,QAAQ,EAAK,CACtCd,kBAAkB,CAACc,QAAQ,CAAC,CAC5B;AACF,CAAC,CAED,mBACExB,KAAA,QAAKyB,SAAS,CAAC,UAAU,CAAAC,QAAA,eAEvB1B,KAAA,QAAKyB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5B,IAAA,QAAK2B,SAAS,CAAC,UAAU,CAAM,CAAC,cAChC3B,IAAA,QAAK2B,SAAS,CAAC,UAAU,CAAM,CAAC,cAChC3B,IAAA,QAAK2B,SAAS,CAAC,UAAU,CAAM,CAAC,cAChC3B,IAAA,QAAK2B,SAAS,CAAC,YAAY,CAAM,CAAC,EAC/B,CAAC,cAGNzB,KAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5B,IAAA,QAAK2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BtB,WAAW,CAACuB,cAAc,CAAC,OAAO,CAAE,CACnCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SACV,CAAC,CAAC,CACC,CAAC,cACNnC,IAAA,QAAK2B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sCAAM,CAAK,CAAC,cAC3C5B,IAAA,WACE2B,SAAS,CAAC,mBAAmB,CAC7BS,OAAO,CAAEA,CAAA,GAAM1B,mBAAmB,CAAC,IAAI,CAAE,CACzCQ,KAAK,CAAC,+BAAW,CAAAU,QAAA,CAClB,uCAED,CAAQ,CAAC,EACN,CAAC,cAGN5B,IAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B1B,KAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B5B,IAAA,OAAI2B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,sDAE3B,CAAI,CAAC,cACL5B,IAAA,OAAI2B,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,sGAE1B,CAAI,CAAC,cACL5B,IAAA,QAAK2B,SAAS,CAAC,eAAe,CAAM,CAAC,cACrC3B,IAAA,MAAG2B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,oHAElC,CAAG,CAAC,EACD,CAAC,CACH,CAAC,cAGN5B,IAAA,QAAK2B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BZ,eAAe,CAACqB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC/BrC,KAAA,QAEEyB,SAAS,CAAC,UAAU,CACpBa,KAAK,CAAE,CAAE,SAAS,IAAAC,MAAA,CAAKF,KAAK,CAAG,GAAG,KAAG,CAAE,SAAS,CAAED,IAAI,CAAChB,KAAM,CAAE,CAC/Dc,OAAO,CAAEA,CAAA,GAAMb,gBAAgB,CAACe,IAAI,CAACrB,EAAE,CAAE,CAAAW,QAAA,eAEzC1B,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,QAAK2B,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEU,IAAI,CAAClB,IAAI,CAAM,CAAC,cAC5CpB,IAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEc,MAAM,CAACH,KAAK,CAAG,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAM,CAAC,EACpE,CAAC,cACNzC,KAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5B,IAAA,OAAI2B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEU,IAAI,CAACpB,KAAK,CAAK,CAAC,cAC5ClB,IAAA,MAAG2B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEU,IAAI,CAACnB,QAAQ,CAAI,CAAC,cAChDnB,IAAA,MAAG2B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEU,IAAI,CAACjB,WAAW,CAAI,CAAC,EACnD,CAAC,cACNnB,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,SAAM2B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxC5B,IAAA,SAAM2B,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,EAC7B,CAAC,cACN5B,IAAA,QAAK2B,SAAS,CAAC,WAAW,CAAM,CAAC,GAlB5BW,IAAI,CAACrB,EAmBP,CACN,CAAC,CACC,CAAC,cAGNf,KAAA,QAAKyB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1B,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,uCAAO,CAAM,CAAC,cAC7C5B,IAAA,SAAM2B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EAC5C,CAAC,cACN1B,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,oBAAQ,CAAM,CAAC,cAC9C5B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEjB,eAAe,CAAO,CAAC,EACpD,CAAC,cACNT,KAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC3C5B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEZ,eAAe,CAAC4B,MAAM,CAAO,CAAC,EAC3D,CAAC,EACH,CAAC,cAGN5C,IAAA,CAACF,kBAAkB,EACjB+C,SAAS,CAAEpC,gBAAiB,CAC5BqC,OAAO,CAAEA,CAAA,GAAMpC,mBAAmB,CAAC,KAAK,CAAE,CAC1CqC,aAAa,CAAEtB,iBAAkB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}