{"ast": null, "code": "var _jsxFileName = \"E:\\\\\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F\\\\\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA17.14\\\\performance-tracker-web\\\\src\\\\\\u6A21\\u5757\\u516D\\\\components\\\\KPITable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/KPITable.css';\nimport moduleSixService from '../services/moduleSixService';\nimport ExportModal from './ExportModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KPITable = ({\n  data,\n  tableName,\n  viewType,\n  currentMonthPair,\n  onSyncStatus\n}) => {\n  _s();\n  const [editingCell, setEditingCell] = useState(null);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const [syncErrors, setSyncErrors] = useState([]);\n  const [showRetryModal, setShowRetryModal] = useState(false);\n  const [pendingChangesCount, setPendingChangesCount] = useState(0);\n\n  // 防抖保存相关\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  const retryTimeoutRef = useRef(null);\n\n  // 检查待同步更改数量\n  useEffect(() => {\n    const updatePendingCount = () => {\n      const count = moduleSixService.module6_getPendingChangesCount();\n      setPendingChangesCount(count);\n    };\n    updatePendingCount();\n    const interval = setInterval(updatePendingCount, 5000); // 每5秒检查一次\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // 防抖保存函数 - 增强错误处理\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      onSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        var _pendingSaveRef$curre, _pendingSaveRef$curre2;\n        (_pendingSaveRef$curre = (_pendingSaveRef$curre2 = pendingSaveRef.current).abort) === null || _pendingSaveRef$curre === void 0 ? void 0 : _pendingSaveRef$curre.call(_pendingSaveRef$curre2);\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用服务保存数据\n      const syncResult = await moduleSixService.module6_updateCellData(tableName, viewType, rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        if (syncResult.backendSync) {\n          onSyncStatus('同步成功');\n          setTimeout(() => onSyncStatus('已同步'), 1000);\n        } else if (syncResult.localSync) {\n          onSyncStatus('已保存到本地');\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n\n          // 更新待同步计数\n          setPendingChangesCount(prev => prev + 1);\n\n          // 设置自动重试\n          if (retryTimeoutRef.current) {\n            clearTimeout(retryTimeoutRef.current);\n          }\n          retryTimeoutRef.current = setTimeout(() => {\n            handleRetrySync();\n          }, 10000); // 10秒后自动重试\n        } else {\n          throw new Error('保存失败');\n        }\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (error.name !== 'AbortError') {\n        console.error('保存失败:', error);\n\n        // 添加到错误列表\n        setSyncErrors(prev => [...prev, {\n          id: Date.now(),\n          message: error.message || '保存失败',\n          field: `${tableName}.${viewType}[${rowIndex}].${field}`,\n          value: value,\n          timestamp: new Date().toISOString()\n        }]);\n        onSyncStatus(`同步失败: ${error.message}`);\n        setTimeout(() => onSyncStatus('同步失败'), 3000);\n      }\n    }\n  }, [tableName, viewType, onSyncStatus]);\n\n  // 处理重试同步\n  const handleRetrySync = useCallback(async () => {\n    try {\n      onSyncStatus('重试同步中...');\n      const result = await moduleSixService.module6_retrySyncPendingChanges();\n      if (result.success) {\n        setPendingChangesCount(result.remainingCount);\n        if (result.syncedCount > 0) {\n          onSyncStatus(`成功同步 ${result.syncedCount} 项更改`);\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else if (result.remainingCount === 0) {\n          onSyncStatus('所有更改已同步');\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else {\n          onSyncStatus(`仍有 ${result.remainingCount} 项待同步`);\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n        }\n      } else {\n        throw new Error('重试同步失败');\n      }\n    } catch (error) {\n      console.error('重试同步失败:', error);\n      onSyncStatus('重试失败');\n      setTimeout(() => onSyncStatus('同步失败'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 清除错误\n  const clearErrors = useCallback(() => {\n    setSyncErrors([]);\n  }, []);\n\n  // 清除所有待同步更改\n  const clearPendingChanges = useCallback(() => {\n    if (window.confirm('确定要清除所有待同步的更改吗？这将丢失未同步的数据。')) {\n      moduleSixService.module6_clearPendingChanges();\n      setPendingChangesCount(0);\n      onSyncStatus('已清除待同步更改');\n      setTimeout(() => onSyncStatus('已同步'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 处理单元格编辑\n  const handleCellEdit = (rowIndex, field, value) => {\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 500);\n  };\n\n  // 开始编辑\n  const startEdit = (rowIndex, field) => {\n    setEditingCell({\n      rowIndex,\n      field\n    });\n  };\n\n  // 完成编辑\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 处理导出\n  const handleExport = async exportConfig => {\n    setExportLoading(true);\n    try {\n      await moduleSixService.module6_exportData(tableName, viewType, exportConfig);\n      onSyncStatus('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      onSyncStatus('导出失败');\n    }\n    setExportLoading(false);\n    setShowExportModal(false);\n  };\n\n  // 渲染月份列头\n  const renderMonthHeaders = () => {\n    return currentMonthPair.map(month => /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n        className: \"module6_month-header\",\n        children: [month, \"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        className: \"module6_month-header\",\n        children: [month, \"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        className: \"module6_month-header\",\n        children: [month, \"\\u6708\\u8BC4\\u5206\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, month, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this));\n  };\n\n  // 渲染月份数据 - 优化版本\n  const renderMonthData = (row, rowIndex) => {\n    return currentMonthPair.map(month => {\n      var _row, _row3, _row4, _row5, _row6, _row7, _row9, _row0, _row1, _row10, _row11, _row13, _row14, _row15, _row16;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: `module6_month-cell ${(_row = row[`${month}月工作计划`]) !== null && _row !== void 0 && _row.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`,\n          onClick: () => {\n            var _row2;\n            return !((_row2 = row[`${month}月工作计划`]) !== null && _row2 !== void 0 && _row2.isReadOnly) && startEdit(rowIndex, `${month}月工作计划`);\n          },\n          title: (_row3 = row[`${month}月工作计划`]) !== null && _row3 !== void 0 && _row3.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段',\n          children: [(editingCell === null || editingCell === void 0 ? void 0 : editingCell.rowIndex) === rowIndex && (editingCell === null || editingCell === void 0 ? void 0 : editingCell.field) === `${month}月工作计划` ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"module6_cell-textarea\",\n            value: ((_row4 = row[`${month}月工作计划`]) === null || _row4 === void 0 ? void 0 : _row4.value) || '',\n            onChange: e => handleCellEdit(rowIndex, `${month}月工作计划`, e.target.value),\n            onBlur: finishEdit,\n            autoFocus: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_cell-content\",\n            children: renderCellContent((_row5 = row[`${month}月工作计划`]) === null || _row5 === void 0 ? void 0 : _row5.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), ((_row6 = row[`${month}月工作计划`]) === null || _row6 === void 0 ? void 0 : _row6.isReadOnly) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_readonly-indicator\",\n            title: \"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `module6_month-cell ${(_row7 = row[`${month}月完成情况`]) !== null && _row7 !== void 0 && _row7.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`,\n          onClick: () => {\n            var _row8;\n            return !((_row8 = row[`${month}月完成情况`]) !== null && _row8 !== void 0 && _row8.isReadOnly) && startEdit(rowIndex, `${month}月完成情况`);\n          },\n          title: (_row9 = row[`${month}月完成情况`]) !== null && _row9 !== void 0 && _row9.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段',\n          children: [(editingCell === null || editingCell === void 0 ? void 0 : editingCell.rowIndex) === rowIndex && (editingCell === null || editingCell === void 0 ? void 0 : editingCell.field) === `${month}月完成情况` ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"module6_cell-textarea\",\n            value: ((_row0 = row[`${month}月完成情况`]) === null || _row0 === void 0 ? void 0 : _row0.value) || '',\n            onChange: e => handleCellEdit(rowIndex, `${month}月完成情况`, e.target.value),\n            onBlur: finishEdit,\n            autoFocus: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_cell-content\",\n            children: renderCellContent((_row1 = row[`${month}月完成情况`]) === null || _row1 === void 0 ? void 0 : _row1.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), ((_row10 = row[`${month}月完成情况`]) === null || _row10 === void 0 ? void 0 : _row10.isReadOnly) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_readonly-indicator\",\n            title: \"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `module6_month-cell ${(_row11 = row[`${month}月评分`]) !== null && _row11 !== void 0 && _row11.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`,\n          onClick: () => {\n            var _row12;\n            return !((_row12 = row[`${month}月评分`]) !== null && _row12 !== void 0 && _row12.isReadOnly) && startEdit(rowIndex, `${month}月评分`);\n          },\n          title: (_row13 = row[`${month}月评分`]) !== null && _row13 !== void 0 && _row13.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段',\n          children: [(editingCell === null || editingCell === void 0 ? void 0 : editingCell.rowIndex) === rowIndex && (editingCell === null || editingCell === void 0 ? void 0 : editingCell.field) === `${month}月评分` ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            step: \"0.01\",\n            min: \"0\",\n            max: \"100\",\n            className: \"module6_cell-input\",\n            value: ((_row14 = row[`${month}月评分`]) === null || _row14 === void 0 ? void 0 : _row14.value) || '',\n            onChange: e => handleCellEdit(rowIndex, `${month}月评分`, e.target.value),\n            onBlur: finishEdit,\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_cell-content\",\n            children: formatValue((_row15 = row[`${month}月评分`]) === null || _row15 === void 0 ? void 0 : _row15.value, false, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), ((_row16 = row[`${month}月评分`]) === null || _row16 === void 0 ? void 0 : _row16.isReadOnly) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_readonly-indicator\",\n            title: \"\\u6B64\\u6570\\u636E\\u6765\\u81EA\\u6E90\\u8868\\uFF0C\\u4E0D\\u53EF\\u7F16\\u8F91\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 9\n        }, this)]\n      }, month, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 7\n      }, this);\n    });\n  };\n\n  // 渲染单元格内容 - 支持换行显示和数字格式化\n  const renderCellContent = value => {\n    if (!value) return '';\n\n    // 首先格式化数字\n    const formattedValue = formatValue(value, false, false);\n\n    // 将格式化后的值转换为字符串\n    const stringValue = String(formattedValue);\n\n    // 如果内容包含换行符，则分行显示\n    if (stringValue.includes('\\n') || stringValue.includes('\\r\\n')) {\n      return stringValue.split(/\\r\\n|\\n|\\r/).map((line, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"module6_cell-line\",\n        children: line\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this));\n    }\n\n    // 如果内容过长，自动换行\n    if (stringValue.length > 50) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"module6_cell-long-content\",\n        children: stringValue\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this);\n    }\n    return stringValue;\n  };\n\n  // 格式化数值显示 - 修复长小数问题\n  const formatValue = (value, isPercentage = false, excludeFromFormatting = false) => {\n    if (excludeFromFormatting || value === '' || value === null || value === undefined) {\n      return value;\n    }\n\n    // 处理百分比\n    if (isPercentage && typeof value === 'number') {\n      return (value * 100).toFixed(2) + '%';\n    }\n\n    // 处理数值类型\n    if (typeof value === 'number') {\n      // 检查是否为整数或接近整数（避免浮点精度问题）\n      if (Number.isInteger(value) || Math.abs(value - Math.round(value)) < 0.0001) {\n        return Math.round(value).toString();\n      }\n      // 小数保留两位小数\n      return Number(value.toFixed(2)).toString();\n    }\n\n    // 检查字符串是否为纯数值\n    const stringValue = String(value).trim();\n\n    // 如果是纯数字字符串（包括科学计数法）\n    if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(stringValue)) {\n      const numValue = parseFloat(stringValue);\n\n      // 检查是否为有效数字\n      if (!isNaN(numValue) && isFinite(numValue)) {\n        // 检查是否为整数或接近整数\n        if (Number.isInteger(numValue) || Math.abs(numValue - Math.round(numValue)) < 0.0001) {\n          return Math.round(numValue).toString();\n        }\n        // 小数保留两位小数，并移除末尾的0\n        return Number(numValue.toFixed(2)).toString();\n      }\n    }\n\n    // 检查是否包含百分号\n    if (stringValue.includes('%')) {\n      const numPart = stringValue.replace('%', '').trim();\n      if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(numPart)) {\n        const numValue = parseFloat(numPart);\n        if (!isNaN(numValue) && isFinite(numValue)) {\n          return Number(numValue.toFixed(2)).toString() + '%';\n        }\n      }\n    }\n\n    // 特殊处理：如果字符串包含数字但不是纯数字（如 \"88.8%=32/36\"）\n    // 检查是否以数字开头\n    const numberMatch = stringValue.match(/^(-?\\d+(?:\\.\\d+)?)/);\n    if (numberMatch) {\n      const leadingNumber = parseFloat(numberMatch[1]);\n      if (!isNaN(leadingNumber) && isFinite(leadingNumber)) {\n        // 如果是整数或接近整数，格式化为整数\n        if (Number.isInteger(leadingNumber) || Math.abs(leadingNumber - Math.round(leadingNumber)) < 0.0001) {\n          return stringValue.replace(numberMatch[1], Math.round(leadingNumber).toString());\n        }\n        // 否则保留两位小数\n        return stringValue.replace(numberMatch[1], Number(leadingNumber.toFixed(2)).toString());\n      }\n    }\n\n    // 非数值内容直接返回\n    return value;\n  };\n\n  // 合并单元格逻辑\n  const getMergedInfo = () => {\n    const mergedInfo = {};\n    if (!data || data.length === 0) return mergedInfo;\n    let currentSequenceSpan = 1;\n    let currentIndicatorSpan = 1;\n    let sequenceStartIndex = 0;\n    let indicatorStartIndex = 0;\n    for (let i = 1; i < data.length; i++) {\n      const currentRow = data[i];\n      const prevRow = data[i - 1];\n\n      // 检查序号是否相同\n      if (currentRow.序号 === prevRow.序号) {\n        currentSequenceSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentSequenceSpan > 1) {\n          for (let j = sequenceStartIndex; j < i; j++) {\n            mergedInfo[`${j}_序号`] = {\n              rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n              isFirst: j === sequenceStartIndex\n            };\n          }\n        }\n        currentSequenceSpan = 1;\n        sequenceStartIndex = i;\n      }\n\n      // 检查指标是否相同\n      if (currentRow.指标 === prevRow.指标) {\n        currentIndicatorSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentIndicatorSpan > 1) {\n          for (let j = indicatorStartIndex; j < i; j++) {\n            mergedInfo[`${j}_指标`] = {\n              rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n              isFirst: j === indicatorStartIndex\n            };\n          }\n        }\n        currentIndicatorSpan = 1;\n        indicatorStartIndex = i;\n      }\n    }\n\n    // 处理最后一组\n    if (currentSequenceSpan > 1) {\n      for (let j = sequenceStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_序号`] = {\n          rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n          isFirst: j === sequenceStartIndex\n        };\n      }\n    }\n    if (currentIndicatorSpan > 1) {\n      for (let j = indicatorStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_指标`] = {\n          rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n          isFirst: j === indicatorStartIndex\n        };\n      }\n    }\n    return mergedInfo;\n  };\n  const mergedInfo = getMergedInfo();\n\n  // 渲染表头\n  const renderHeaders = () => {\n    // 关键指标和重点工作都使用相同的表头结构\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"\\u5E8F\\u53F7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: viewType === 'keyWork' ? '重点工作项目' : '指标'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"\\u76EE\\u6807\\u503C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"\\u6743\\u91CD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"\\u6307\\u6807\\u5206\\u7C7B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"\\u8DDF\\u8E2A\\u9891\\u6B21\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), renderMonthHeaders()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染表格行\n  const renderRows = () => {\n    if (!data || data.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: /*#__PURE__*/_jsxDEV(\"td\", {\n          colSpan: 6 + currentMonthPair.length * 3,\n          children: \"\\u5F53\\u524D\\u89C6\\u56FE\\u6CA1\\u6709\\u53EF\\u663E\\u793A\\u7684\\u6570\\u636E\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this);\n    }\n    return data.map((row, rowIndex) => {\n      const sequenceMergeInfo = mergedInfo[`${rowIndex}_序号`];\n      const indicatorMergeInfo = mergedInfo[`${rowIndex}_指标`];\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: [(!sequenceMergeInfo || sequenceMergeInfo.rowSpan > 0) && /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_sequence-cell\",\n          rowSpan: (sequenceMergeInfo === null || sequenceMergeInfo === void 0 ? void 0 : sequenceMergeInfo.rowSpan) || 1,\n          children: row.序号\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this), (!indicatorMergeInfo || indicatorMergeInfo.rowSpan > 0) && /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_indicator-cell\",\n          rowSpan: (indicatorMergeInfo === null || indicatorMergeInfo === void 0 ? void 0 : indicatorMergeInfo.rowSpan) || 1,\n          children: row.指标\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_target-cell\",\n          children: formatValue(row.目标值, false, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_weight-cell\",\n          children: row.权重\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_category-cell\",\n          children: formatValue(row.指标分类, false, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"module6_frequency-cell\",\n          children: formatValue(row.跟踪频次, false, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), renderMonthData(row, rowIndex)]\n      }, rowIndex, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"table\", {\n    className: \"module6_kpi-table\",\n    children: [(pendingChangesCount > 0 || syncErrors.length > 0) && /*#__PURE__*/_jsxDEV(\"thead\", {\n      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"module6_sync-toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"td\", {\n          colSpan: 6 + currentMonthPair.length * 3,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"module6_toolbar-content\",\n            children: [pendingChangesCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"module6_pending-changes\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"module6_pending-count\",\n                children: [\"\\uD83D\\uDCE4 \", pendingChangesCount, \" \\u9879\\u5F85\\u540C\\u6B65\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"module6_retry-btn\",\n                onClick: handleRetrySync,\n                title: \"\\u91CD\\u8BD5\\u540C\\u6B65\",\n                children: \"\\uD83D\\uDD04 \\u91CD\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"module6_clear-btn\",\n                onClick: clearPendingChanges,\n                title: \"\\u6E05\\u9664\\u5F85\\u540C\\u6B65\\u66F4\\u6539\",\n                children: \"\\uD83D\\uDDD1\\uFE0F \\u6E05\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 19\n            }, this), syncErrors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"module6_sync-errors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"module6_error-count\",\n                children: [\"\\u26A0\\uFE0F \", syncErrors.length, \" \\u4E2A\\u9519\\u8BEF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"module6_clear-errors-btn\",\n                onClick: clearErrors,\n                title: \"\\u6E05\\u9664\\u9519\\u8BEF\",\n                children: \"\\u2716\\uFE0F \\u6E05\\u9664\\u9519\\u8BEF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"module6_show-errors-btn\",\n                onClick: () => setShowRetryModal(true),\n                title: \"\\u67E5\\u770B\\u9519\\u8BEF\\u8BE6\\u60C5\",\n                children: \"\\uD83D\\uDCCB \\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"thead\", {\n      children: renderHeaders()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n      children: renderRows()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 518,\n    columnNumber: 5\n  }, this);\n};\n_s(KPITable, \"cDd8Upa4bBHKorfrzbC2yKPLIUk=\");\n_c = KPITable;\nexport default KPITable;\nvar _c;\n$RefreshReg$(_c, \"KPITable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "moduleSixService", "ExportModal", "jsxDEV", "_jsxDEV", "KPITable", "data", "tableName", "viewType", "currentMonthPair", "onSyncStatus", "_s", "editingCell", "setEditingCell", "showExportModal", "setShowExportModal", "exportLoading", "setExportLoading", "syncErrors", "setSyncErrors", "showRetryModal", "setShowRetryModal", "pendingChangesCount", "setPendingChangesCount", "saveTimeoutRef", "pendingSaveRef", "retryTimeoutRef", "updatePendingCount", "count", "module6_getPendingChangesCount", "interval", "setInterval", "clearInterval", "debouncedSave", "rowIndex", "field", "value", "current", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "controller", "AbortController", "syncResult", "module6_updateCellData", "signal", "aborted", "backendSync", "setTimeout", "localSync", "prev", "clearTimeout", "handleRetrySync", "Error", "error", "name", "console", "id", "Date", "now", "message", "timestamp", "toISOString", "result", "module6_retrySyncPendingChanges", "success", "remainingCount", "syncedCount", "clearErrors", "clearPendingChanges", "window", "confirm", "module6_clearPendingChanges", "handleCellEdit", "startEdit", "finishEdit", "handleExport", "exportConfig", "module6_exportData", "renderMonthHeaders", "map", "month", "Fragment", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMonthData", "row", "_row", "_row3", "_row4", "_row5", "_row6", "_row7", "_row9", "_row0", "_row1", "_row10", "_row11", "_row13", "_row14", "_row15", "_row16", "isReadOnly", "_isMatched", "onClick", "_row2", "title", "_matchedSource", "onChange", "e", "target", "onBlur", "autoFocus", "rows", "renderCellContent", "_row8", "_row12", "type", "step", "min", "max", "formatValue", "formattedValue", "stringValue", "String", "includes", "split", "line", "index", "length", "isPercentage", "excludeFromFormatting", "undefined", "toFixed", "Number", "isInteger", "Math", "abs", "round", "toString", "trim", "test", "numValue", "parseFloat", "isNaN", "isFinite", "numPart", "replace", "numberMatch", "match", "leadingNumber", "getMergedInfo", "mergedInfo", "currentSequenceSpan", "currentIndicatorSpan", "sequenceStartIndex", "indicatorStartIndex", "i", "currentRow", "prevRow", "序号", "j", "rowSpan", "<PERSON><PERSON><PERSON><PERSON>", "指标", "renderHeaders", "renderRows", "colSpan", "sequenceMergeInfo", "indicatorMergeInfo", "目标值", "权重", "指标分类", "跟踪频次", "_c", "$RefreshReg$"], "sources": ["E:/处理公司程序/重点工作网页化设计7.14/performance-tracker-web/src/模块六/components/KPITable.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/KPITable.css';\nimport moduleSixService from '../services/moduleSixService';\nimport ExportModal from './ExportModal';\n\nconst KPITable = ({ data, tableName, viewType, currentMonthPair, onSyncStatus }) => {\n  const [editingCell, setEditingCell] = useState(null);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const [syncErrors, setSyncErrors] = useState([]);\n  const [showRetryModal, setShowRetryModal] = useState(false);\n  const [pendingChangesCount, setPendingChangesCount] = useState(0);\n\n  // 防抖保存相关\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  const retryTimeoutRef = useRef(null);\n\n\n\n  // 检查待同步更改数量\n  useEffect(() => {\n    const updatePendingCount = () => {\n      const count = moduleSixService.module6_getPendingChangesCount();\n      setPendingChangesCount(count);\n    };\n\n    updatePendingCount();\n    const interval = setInterval(updatePendingCount, 5000); // 每5秒检查一次\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // 防抖保存函数 - 增强错误处理\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      onSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用服务保存数据\n      const syncResult = await moduleSixService.module6_updateCellData(tableName, viewType, rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        if (syncResult.backendSync) {\n          onSyncStatus('同步成功');\n          setTimeout(() => onSyncStatus('已同步'), 1000);\n        } else if (syncResult.localSync) {\n          onSyncStatus('已保存到本地');\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n\n          // 更新待同步计数\n          setPendingChangesCount(prev => prev + 1);\n\n          // 设置自动重试\n          if (retryTimeoutRef.current) {\n            clearTimeout(retryTimeoutRef.current);\n          }\n          retryTimeoutRef.current = setTimeout(() => {\n            handleRetrySync();\n          }, 10000); // 10秒后自动重试\n        } else {\n          throw new Error('保存失败');\n        }\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (error.name !== 'AbortError') {\n        console.error('保存失败:', error);\n\n        // 添加到错误列表\n        setSyncErrors(prev => [...prev, {\n          id: Date.now(),\n          message: error.message || '保存失败',\n          field: `${tableName}.${viewType}[${rowIndex}].${field}`,\n          value: value,\n          timestamp: new Date().toISOString()\n        }]);\n\n        onSyncStatus(`同步失败: ${error.message}`);\n        setTimeout(() => onSyncStatus('同步失败'), 3000);\n      }\n    }\n  }, [tableName, viewType, onSyncStatus]);\n\n  // 处理重试同步\n  const handleRetrySync = useCallback(async () => {\n    try {\n      onSyncStatus('重试同步中...');\n      const result = await moduleSixService.module6_retrySyncPendingChanges();\n\n      if (result.success) {\n        setPendingChangesCount(result.remainingCount);\n\n        if (result.syncedCount > 0) {\n          onSyncStatus(`成功同步 ${result.syncedCount} 项更改`);\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else if (result.remainingCount === 0) {\n          onSyncStatus('所有更改已同步');\n          setTimeout(() => onSyncStatus('已同步'), 2000);\n        } else {\n          onSyncStatus(`仍有 ${result.remainingCount} 项待同步`);\n          setTimeout(() => onSyncStatus('等待同步'), 2000);\n        }\n      } else {\n        throw new Error('重试同步失败');\n      }\n    } catch (error) {\n      console.error('重试同步失败:', error);\n      onSyncStatus('重试失败');\n      setTimeout(() => onSyncStatus('同步失败'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 清除错误\n  const clearErrors = useCallback(() => {\n    setSyncErrors([]);\n  }, []);\n\n  // 清除所有待同步更改\n  const clearPendingChanges = useCallback(() => {\n    if (window.confirm('确定要清除所有待同步的更改吗？这将丢失未同步的数据。')) {\n      moduleSixService.module6_clearPendingChanges();\n      setPendingChangesCount(0);\n      onSyncStatus('已清除待同步更改');\n      setTimeout(() => onSyncStatus('已同步'), 2000);\n    }\n  }, [onSyncStatus]);\n\n  // 处理单元格编辑\n  const handleCellEdit = (rowIndex, field, value) => {\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 500);\n  };\n\n  // 开始编辑\n  const startEdit = (rowIndex, field) => {\n    setEditingCell({ rowIndex, field });\n  };\n\n  // 完成编辑\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 处理导出\n  const handleExport = async (exportConfig) => {\n    setExportLoading(true);\n    try {\n      await moduleSixService.module6_exportData(tableName, viewType, exportConfig);\n      onSyncStatus('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      onSyncStatus('导出失败');\n    }\n    setExportLoading(false);\n    setShowExportModal(false);\n  };\n\n  // 渲染月份列头\n  const renderMonthHeaders = () => {\n    return currentMonthPair.map(month => (\n      <React.Fragment key={month}>\n        <th className=\"module6_month-header\">{month}月工作计划</th>\n        <th className=\"module6_month-header\">{month}月完成情况</th>\n        <th className=\"module6_month-header\">{month}月评分</th>\n      </React.Fragment>\n    ));\n  };\n\n  // 渲染月份数据 - 优化版本\n  const renderMonthData = (row, rowIndex) => {\n    return currentMonthPair.map(month => (\n      <React.Fragment key={month}>\n        {/* 工作计划单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月工作计划`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月工作计划`]?.isReadOnly && startEdit(rowIndex, `${month}月工作计划`)}\n          title={row[`${month}月工作计划`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月工作计划` ? (\n            <textarea\n              className=\"module6_cell-textarea\"\n              value={row[`${month}月工作计划`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月工作计划`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n              rows={3}\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {renderCellContent(row[`${month}月工作计划`]?.value)}\n            </div>\n          )}\n          {row[`${month}月工作计划`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n\n        {/* 完成情况单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月完成情况`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月完成情况`]?.isReadOnly && startEdit(rowIndex, `${month}月完成情况`)}\n          title={row[`${month}月完成情况`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月完成情况` ? (\n            <textarea\n              className=\"module6_cell-textarea\"\n              value={row[`${month}月完成情况`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月完成情况`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n              rows={3}\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {renderCellContent(row[`${month}月完成情况`]?.value)}\n            </div>\n          )}\n          {row[`${month}月完成情况`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n\n        {/* 评分单元格 */}\n        <td\n          className={`module6_month-cell ${row[`${month}月评分`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}\n          onClick={() => !row[`${month}月评分`]?.isReadOnly && startEdit(rowIndex, `${month}月评分`)}\n          title={row[`${month}月评分`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}\n        >\n          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月评分` ? (\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              max=\"100\"\n              className=\"module6_cell-input\"\n              value={row[`${month}月评分`]?.value || ''}\n              onChange={(e) => handleCellEdit(rowIndex, `${month}月评分`, e.target.value)}\n              onBlur={finishEdit}\n              autoFocus\n            />\n          ) : (\n            <div className=\"module6_cell-content\">\n              {formatValue(row[`${month}月评分`]?.value, false, false)}\n            </div>\n          )}\n          {row[`${month}月评分`]?.isReadOnly && (\n            <div className=\"module6_readonly-indicator\" title=\"此数据来自源表，不可编辑\">\n              🔒\n            </div>\n          )}\n        </td>\n      </React.Fragment>\n    ));\n  };\n\n  // 渲染单元格内容 - 支持换行显示和数字格式化\n  const renderCellContent = (value) => {\n    if (!value) return '';\n\n    // 首先格式化数字\n    const formattedValue = formatValue(value, false, false);\n\n    // 将格式化后的值转换为字符串\n    const stringValue = String(formattedValue);\n\n    // 如果内容包含换行符，则分行显示\n    if (stringValue.includes('\\n') || stringValue.includes('\\r\\n')) {\n      return stringValue.split(/\\r\\n|\\n|\\r/).map((line, index) => (\n        <div key={index} className=\"module6_cell-line\">\n          {line}\n        </div>\n      ));\n    }\n\n    // 如果内容过长，自动换行\n    if (stringValue.length > 50) {\n      return (\n        <div className=\"module6_cell-long-content\">\n          {stringValue}\n        </div>\n      );\n    }\n\n    return stringValue;\n  };\n\n  // 格式化数值显示 - 修复长小数问题\n  const formatValue = (value, isPercentage = false, excludeFromFormatting = false) => {\n    if (excludeFromFormatting || value === '' || value === null || value === undefined) {\n      return value;\n    }\n\n    // 处理百分比\n    if (isPercentage && typeof value === 'number') {\n      return (value * 100).toFixed(2) + '%';\n    }\n\n    // 处理数值类型\n    if (typeof value === 'number') {\n      // 检查是否为整数或接近整数（避免浮点精度问题）\n      if (Number.isInteger(value) || Math.abs(value - Math.round(value)) < 0.0001) {\n        return Math.round(value).toString();\n      }\n      // 小数保留两位小数\n      return Number(value.toFixed(2)).toString();\n    }\n\n    // 检查字符串是否为纯数值\n    const stringValue = String(value).trim();\n\n    // 如果是纯数字字符串（包括科学计数法）\n    if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(stringValue)) {\n      const numValue = parseFloat(stringValue);\n\n      // 检查是否为有效数字\n      if (!isNaN(numValue) && isFinite(numValue)) {\n        // 检查是否为整数或接近整数\n        if (Number.isInteger(numValue) || Math.abs(numValue - Math.round(numValue)) < 0.0001) {\n          return Math.round(numValue).toString();\n        }\n        // 小数保留两位小数，并移除末尾的0\n        return Number(numValue.toFixed(2)).toString();\n      }\n    }\n\n    // 检查是否包含百分号\n    if (stringValue.includes('%')) {\n      const numPart = stringValue.replace('%', '').trim();\n      if (/^-?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$/.test(numPart)) {\n        const numValue = parseFloat(numPart);\n        if (!isNaN(numValue) && isFinite(numValue)) {\n          return Number(numValue.toFixed(2)).toString() + '%';\n        }\n      }\n    }\n\n    // 特殊处理：如果字符串包含数字但不是纯数字（如 \"88.8%=32/36\"）\n    // 检查是否以数字开头\n    const numberMatch = stringValue.match(/^(-?\\d+(?:\\.\\d+)?)/);\n    if (numberMatch) {\n      const leadingNumber = parseFloat(numberMatch[1]);\n      if (!isNaN(leadingNumber) && isFinite(leadingNumber)) {\n        // 如果是整数或接近整数，格式化为整数\n        if (Number.isInteger(leadingNumber) || Math.abs(leadingNumber - Math.round(leadingNumber)) < 0.0001) {\n          return stringValue.replace(numberMatch[1], Math.round(leadingNumber).toString());\n        }\n        // 否则保留两位小数\n        return stringValue.replace(numberMatch[1], Number(leadingNumber.toFixed(2)).toString());\n      }\n    }\n\n    // 非数值内容直接返回\n    return value;\n  };\n\n  // 合并单元格逻辑\n  const getMergedInfo = () => {\n    const mergedInfo = {};\n    \n    if (!data || data.length === 0) return mergedInfo;\n    \n    let currentSequenceSpan = 1;\n    let currentIndicatorSpan = 1;\n    let sequenceStartIndex = 0;\n    let indicatorStartIndex = 0;\n    \n    for (let i = 1; i < data.length; i++) {\n      const currentRow = data[i];\n      const prevRow = data[i - 1];\n      \n      // 检查序号是否相同\n      if (currentRow.序号 === prevRow.序号) {\n        currentSequenceSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentSequenceSpan > 1) {\n          for (let j = sequenceStartIndex; j < i; j++) {\n            mergedInfo[`${j}_序号`] = {\n              rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n              isFirst: j === sequenceStartIndex\n            };\n          }\n        }\n        currentSequenceSpan = 1;\n        sequenceStartIndex = i;\n      }\n      \n      // 检查指标是否相同\n      if (currentRow.指标 === prevRow.指标) {\n        currentIndicatorSpan++;\n      } else {\n        // 保存前一组的合并信息\n        if (currentIndicatorSpan > 1) {\n          for (let j = indicatorStartIndex; j < i; j++) {\n            mergedInfo[`${j}_指标`] = {\n              rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n              isFirst: j === indicatorStartIndex\n            };\n          }\n        }\n        currentIndicatorSpan = 1;\n        indicatorStartIndex = i;\n      }\n    }\n    \n    // 处理最后一组\n    if (currentSequenceSpan > 1) {\n      for (let j = sequenceStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_序号`] = {\n          rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,\n          isFirst: j === sequenceStartIndex\n        };\n      }\n    }\n    \n    if (currentIndicatorSpan > 1) {\n      for (let j = indicatorStartIndex; j < data.length; j++) {\n        mergedInfo[`${j}_指标`] = {\n          rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,\n          isFirst: j === indicatorStartIndex\n        };\n      }\n    }\n    \n    return mergedInfo;\n  };\n\n  const mergedInfo = getMergedInfo();\n\n\n\n  // 渲染表头\n  const renderHeaders = () => {\n    // 关键指标和重点工作都使用相同的表头结构\n    return (\n      <tr>\n        <th>序号</th>\n        <th>{viewType === 'keyWork' ? '重点工作项目' : '指标'}</th>\n        <th>目标值</th>\n        <th>权重</th>\n        <th>指标分类</th>\n        <th>跟踪频次</th>\n        {renderMonthHeaders()}\n      </tr>\n    );\n  };\n\n  // 渲染表格行\n  const renderRows = () => {\n    if (!data || data.length === 0) {\n      return (\n        <tr>\n          <td colSpan={6 + currentMonthPair.length * 3}>\n            当前视图没有可显示的数据。\n          </td>\n        </tr>\n      );\n    }\n\n    return data.map((row, rowIndex) => {\n      const sequenceMergeInfo = mergedInfo[`${rowIndex}_序号`];\n      const indicatorMergeInfo = mergedInfo[`${rowIndex}_指标`];\n      \n      return (\n        <tr key={rowIndex}>\n          {/* 序号列 - 支持合并 */}\n          {(!sequenceMergeInfo || sequenceMergeInfo.rowSpan > 0) && (\n            <td \n              className=\"module6_sequence-cell\"\n              rowSpan={sequenceMergeInfo?.rowSpan || 1}\n            >\n              {row.序号}\n            </td>\n          )}\n          \n          {/* 指标列 - 支持合并 */}\n          {(!indicatorMergeInfo || indicatorMergeInfo.rowSpan > 0) && (\n            <td \n              className=\"module6_indicator-cell\"\n              rowSpan={indicatorMergeInfo?.rowSpan || 1}\n            >\n              {row.指标}\n            </td>\n          )}\n          \n          <td className=\"module6_target-cell\">{formatValue(row.目标值, false, true)}</td>\n          <td className=\"module6_weight-cell\">{row.权重}</td>\n          <td className=\"module6_category-cell\">{formatValue(row.指标分类, false, true)}</td>\n          <td className=\"module6_frequency-cell\">{formatValue(row.跟踪频次, false, true)}</td>\n          {renderMonthData(row, rowIndex)}\n        </tr>\n      );\n    });\n  };\n\n  return (\n    <table className=\"module6_kpi-table\">\n      {/* 同步状态和错误处理工具栏 - 作为表格的一部分 */}\n      {(pendingChangesCount > 0 || syncErrors.length > 0) && (\n        <thead>\n          <tr className=\"module6_sync-toolbar\">\n            <td colSpan={6 + currentMonthPair.length * 3}>\n              <div className=\"module6_toolbar-content\">\n                {pendingChangesCount > 0 && (\n                  <div className=\"module6_pending-changes\">\n                    <span className=\"module6_pending-count\">\n                      📤 {pendingChangesCount} 项待同步\n                    </span>\n                    <button\n                      className=\"module6_retry-btn\"\n                      onClick={handleRetrySync}\n                      title=\"重试同步\"\n                    >\n                      🔄 重试\n                    </button>\n                    <button\n                      className=\"module6_clear-btn\"\n                      onClick={clearPendingChanges}\n                      title=\"清除待同步更改\"\n                    >\n                      🗑️ 清除\n                    </button>\n                  </div>\n                )}\n\n                {syncErrors.length > 0 && (\n                  <div className=\"module6_sync-errors\">\n                    <span className=\"module6_error-count\">\n                      ⚠️ {syncErrors.length} 个错误\n                    </span>\n                    <button\n                      className=\"module6_clear-errors-btn\"\n                      onClick={clearErrors}\n                      title=\"清除错误\"\n                    >\n                      ✖️ 清除错误\n                    </button>\n                    <button\n                      className=\"module6_show-errors-btn\"\n                      onClick={() => setShowRetryModal(true)}\n                      title=\"查看错误详情\"\n                    >\n                      📋 详情\n                    </button>\n                  </div>\n                )}\n              </div>\n            </td>\n          </tr>\n        </thead>\n      )}\n      \n      <thead>\n        {renderHeaders()}\n      </thead>\n      <tbody>\n        {renderRows()}\n      </tbody>\n\n    </table>\n  );\n};\n\nexport default KPITable; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS;EAAEC,QAAQ;EAAEC,gBAAgB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAM2B,cAAc,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM0B,cAAc,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2B,eAAe,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAIpC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM6B,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,MAAMC,KAAK,GAAG3B,gBAAgB,CAAC4B,8BAA8B,CAAC,CAAC;MAC/DN,sBAAsB,CAACK,KAAK,CAAC;IAC/B,CAAC;IAEDD,kBAAkB,CAAC,CAAC;IACpB,MAAMG,QAAQ,GAAGC,WAAW,CAACJ,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;;IAExD,OAAO,MAAMK,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,aAAa,GAAGjC,WAAW,CAAC,OAAOkC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAClE,IAAI;MACF1B,YAAY,CAAC,QAAQ,CAAC;;MAEtB;MACA,IAAIe,cAAc,CAACY,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC1B,CAAAD,qBAAA,IAAAC,sBAAA,GAAAd,cAAc,CAACY,OAAO,EAACG,KAAK,cAAAF,qBAAA,uBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC;MAClC;;MAEA;MACA,MAAMG,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxClB,cAAc,CAACY,OAAO,GAAGK,UAAU;;MAEnC;MACA,MAAME,UAAU,GAAG,MAAM3C,gBAAgB,CAAC4C,sBAAsB,CAACtC,SAAS,EAAEC,QAAQ,EAAE0B,QAAQ,EAAEC,KAAK,EAAEC,KAAK,CAAC;;MAE7G;MACA,IAAI,CAACM,UAAU,CAACI,MAAM,CAACC,OAAO,EAAE;QAC9B,IAAIH,UAAU,CAACI,WAAW,EAAE;UAC1BtC,YAAY,CAAC,MAAM,CAAC;UACpBuC,UAAU,CAAC,MAAMvC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;QAC7C,CAAC,MAAM,IAAIkC,UAAU,CAACM,SAAS,EAAE;UAC/BxC,YAAY,CAAC,QAAQ,CAAC;UACtBuC,UAAU,CAAC,MAAMvC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;;UAE5C;UACAa,sBAAsB,CAAC4B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;;UAExC;UACA,IAAIzB,eAAe,CAACW,OAAO,EAAE;YAC3Be,YAAY,CAAC1B,eAAe,CAACW,OAAO,CAAC;UACvC;UACAX,eAAe,CAACW,OAAO,GAAGY,UAAU,CAAC,MAAM;YACzCI,eAAe,CAAC,CAAC;UACnB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACb,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,MAAM,CAAC;QACzB;QACA7B,cAAc,CAACY,OAAO,GAAG,IAAI;MAC/B;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACd,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;QAC/BC,OAAO,CAACF,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;;QAE7B;QACApC,aAAa,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC9BO,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,OAAO,EAAEN,KAAK,CAACM,OAAO,IAAI,MAAM;UAChC1B,KAAK,EAAE,GAAG5B,SAAS,IAAIC,QAAQ,IAAI0B,QAAQ,KAAKC,KAAK,EAAE;UACvDC,KAAK,EAAEA,KAAK;UACZ0B,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC;QACpC,CAAC,CAAC,CAAC;QAEHrD,YAAY,CAAC,SAAS6C,KAAK,CAACM,OAAO,EAAE,CAAC;QACtCZ,UAAU,CAAC,MAAMvC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,CAACH,SAAS,EAAEC,QAAQ,EAAEE,YAAY,CAAC,CAAC;;EAEvC;EACA,MAAM2C,eAAe,GAAGrD,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFU,YAAY,CAAC,UAAU,CAAC;MACxB,MAAMsD,MAAM,GAAG,MAAM/D,gBAAgB,CAACgE,+BAA+B,CAAC,CAAC;MAEvE,IAAID,MAAM,CAACE,OAAO,EAAE;QAClB3C,sBAAsB,CAACyC,MAAM,CAACG,cAAc,CAAC;QAE7C,IAAIH,MAAM,CAACI,WAAW,GAAG,CAAC,EAAE;UAC1B1D,YAAY,CAAC,QAAQsD,MAAM,CAACI,WAAW,MAAM,CAAC;UAC9CnB,UAAU,CAAC,MAAMvC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;QAC7C,CAAC,MAAM,IAAIsD,MAAM,CAACG,cAAc,KAAK,CAAC,EAAE;UACtCzD,YAAY,CAAC,SAAS,CAAC;UACvBuC,UAAU,CAAC,MAAMvC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;QAC7C,CAAC,MAAM;UACLA,YAAY,CAAC,MAAMsD,MAAM,CAACG,cAAc,OAAO,CAAC;UAChDlB,UAAU,CAAC,MAAMvC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;QAC9C;MACF,CAAC,MAAM;QACL,MAAM,IAAI4C,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,YAAY,CAAC,MAAM,CAAC;MACpBuC,UAAU,CAAC,MAAMvC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;IAC9C;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM2D,WAAW,GAAGrE,WAAW,CAAC,MAAM;IACpCmB,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmD,mBAAmB,GAAGtE,WAAW,CAAC,MAAM;IAC5C,IAAIuE,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAAE;MAChDvE,gBAAgB,CAACwE,2BAA2B,CAAC,CAAC;MAC9ClD,sBAAsB,CAAC,CAAC,CAAC;MACzBb,YAAY,CAAC,UAAU,CAAC;MACxBuC,UAAU,CAAC,MAAMvC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC7C;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgE,cAAc,GAAGA,CAACxC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACjD;IACA,IAAIZ,cAAc,CAACa,OAAO,EAAE;MAC1Be,YAAY,CAAC5B,cAAc,CAACa,OAAO,CAAC;IACtC;;IAEA;IACAb,cAAc,CAACa,OAAO,GAAGY,UAAU,CAAC,MAAM;MACxChB,aAAa,CAACC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,CAAC;IACvC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMuC,SAAS,GAAGA,CAACzC,QAAQ,EAAEC,KAAK,KAAK;IACrCtB,cAAc,CAAC;MAAEqB,QAAQ;MAAEC;IAAM,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMyC,UAAU,GAAGA,CAAA,KAAM;IACvB/D,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMgE,YAAY,GAAG,MAAOC,YAAY,IAAK;IAC3C7D,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMhB,gBAAgB,CAAC8E,kBAAkB,CAACxE,SAAS,EAAEC,QAAQ,EAAEsE,YAAY,CAAC;MAC5EpE,YAAY,CAAC,MAAM,CAAC;IACtB,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B7C,YAAY,CAAC,MAAM,CAAC;IACtB;IACAO,gBAAgB,CAAC,KAAK,CAAC;IACvBF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMiE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOvE,gBAAgB,CAACwE,GAAG,CAACC,KAAK,iBAC/B9E,OAAA,CAACR,KAAK,CAACuF,QAAQ;MAAAC,QAAA,gBACbhF,OAAA;QAAIiF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAAEF,KAAK,EAAC,gCAAK;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtDrF,OAAA;QAAIiF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAAEF,KAAK,EAAC,gCAAK;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtDrF,OAAA;QAAIiF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,GAAEF,KAAK,EAAC,oBAAG;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA,GAHjCP,KAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIV,CACjB,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,GAAG,EAAEzD,QAAQ,KAAK;IACzC,OAAOzB,gBAAgB,CAACwE,GAAG,CAACC,KAAK;MAAA,IAAAU,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA;MAAA,oBAC/BtG,OAAA,CAACR,KAAK,CAACuF,QAAQ;QAAAC,QAAA,gBAEbhF,OAAA;UACEiF,SAAS,EAAE,sBAAsB,CAAAO,IAAA,GAAAD,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAU,IAAA,eAApBA,IAAA,CAAsBe,UAAU,GAAG,UAAU,GAAG,UAAU,IAAIhB,GAAG,CAACiB,UAAU,GAAG,SAAS,GAAG,WAAW,EAAG;UAC1IC,OAAO,EAAEA,CAAA;YAAA,IAAAC,KAAA;YAAA,OAAM,GAAAA,KAAA,GAACnB,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAA4B,KAAA,eAApBA,KAAA,CAAsBH,UAAU,KAAIhC,SAAS,CAACzC,QAAQ,EAAE,GAAGgD,KAAK,OAAO,CAAC;UAAA,CAAC;UACzF6B,KAAK,EAAE,CAAAlB,KAAA,GAAAF,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAW,KAAA,eAApBA,KAAA,CAAsBc,UAAU,GAAG,SAAShB,GAAG,CAACqB,cAAc,IAAI,IAAI,EAAE,GAAG,OAAQ;UAAA5B,QAAA,GAEzF,CAAAxE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,QAAQ,MAAKA,QAAQ,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,KAAK,MAAK,GAAG+C,KAAK,OAAO,gBAC3E9E,OAAA;YACEiF,SAAS,EAAC,uBAAuB;YACjCjD,KAAK,EAAE,EAAA0D,KAAA,GAAAH,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAY,KAAA,uBAApBA,KAAA,CAAsB1D,KAAK,KAAI,EAAG;YACzC6E,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACxC,QAAQ,EAAE,GAAGgD,KAAK,OAAO,EAAEgC,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;YAC3EgF,MAAM,EAAExC,UAAW;YACnByC,SAAS;YACTC,IAAI,EAAE;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,gBAEFrF,OAAA;YAAKiF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAClCmC,iBAAiB,EAAAxB,KAAA,GAACJ,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAa,KAAA,uBAApBA,KAAA,CAAsB3D,KAAK;UAAC;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA,EAAAO,KAAA,GAAAL,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAc,KAAA,uBAApBA,KAAA,CAAsBW,UAAU,kBAC/BvG,OAAA;YAAKiF,SAAS,EAAC,4BAA4B;YAAC0B,KAAK,EAAC,0EAAc;YAAA3B,QAAA,EAAC;UAEjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGLrF,OAAA;UACEiF,SAAS,EAAE,sBAAsB,CAAAY,KAAA,GAAAN,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAe,KAAA,eAApBA,KAAA,CAAsBU,UAAU,GAAG,UAAU,GAAG,UAAU,IAAIhB,GAAG,CAACiB,UAAU,GAAG,SAAS,GAAG,WAAW,EAAG;UAC1IC,OAAO,EAAEA,CAAA;YAAA,IAAAW,KAAA;YAAA,OAAM,GAAAA,KAAA,GAAC7B,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAsC,KAAA,eAApBA,KAAA,CAAsBb,UAAU,KAAIhC,SAAS,CAACzC,QAAQ,EAAE,GAAGgD,KAAK,OAAO,CAAC;UAAA,CAAC;UACzF6B,KAAK,EAAE,CAAAb,KAAA,GAAAP,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAgB,KAAA,eAApBA,KAAA,CAAsBS,UAAU,GAAG,SAAShB,GAAG,CAACqB,cAAc,IAAI,IAAI,EAAE,GAAG,OAAQ;UAAA5B,QAAA,GAEzF,CAAAxE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,QAAQ,MAAKA,QAAQ,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,KAAK,MAAK,GAAG+C,KAAK,OAAO,gBAC3E9E,OAAA;YACEiF,SAAS,EAAC,uBAAuB;YACjCjD,KAAK,EAAE,EAAA+D,KAAA,GAAAR,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAiB,KAAA,uBAApBA,KAAA,CAAsB/D,KAAK,KAAI,EAAG;YACzC6E,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACxC,QAAQ,EAAE,GAAGgD,KAAK,OAAO,EAAEgC,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;YAC3EgF,MAAM,EAAExC,UAAW;YACnByC,SAAS;YACTC,IAAI,EAAE;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,gBAEFrF,OAAA;YAAKiF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAClCmC,iBAAiB,EAAAnB,KAAA,GAACT,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAkB,KAAA,uBAApBA,KAAA,CAAsBhE,KAAK;UAAC;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA,EAAAY,MAAA,GAAAV,GAAG,CAAC,GAAGT,KAAK,OAAO,CAAC,cAAAmB,MAAA,uBAApBA,MAAA,CAAsBM,UAAU,kBAC/BvG,OAAA;YAAKiF,SAAS,EAAC,4BAA4B;YAAC0B,KAAK,EAAC,0EAAc;YAAA3B,QAAA,EAAC;UAEjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGLrF,OAAA;UACEiF,SAAS,EAAE,sBAAsB,CAAAiB,MAAA,GAAAX,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAoB,MAAA,eAAlBA,MAAA,CAAoBK,UAAU,GAAG,UAAU,GAAG,UAAU,IAAIhB,GAAG,CAACiB,UAAU,GAAG,SAAS,GAAG,WAAW,EAAG;UACxIC,OAAO,EAAEA,CAAA;YAAA,IAAAY,MAAA;YAAA,OAAM,GAAAA,MAAA,GAAC9B,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAuC,MAAA,eAAlBA,MAAA,CAAoBd,UAAU,KAAIhC,SAAS,CAACzC,QAAQ,EAAE,GAAGgD,KAAK,KAAK,CAAC;UAAA,CAAC;UACrF6B,KAAK,EAAE,CAAAR,MAAA,GAAAZ,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAqB,MAAA,eAAlBA,MAAA,CAAoBI,UAAU,GAAG,SAAShB,GAAG,CAACqB,cAAc,IAAI,IAAI,EAAE,GAAG,OAAQ;UAAA5B,QAAA,GAEvF,CAAAxE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,QAAQ,MAAKA,QAAQ,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,KAAK,MAAK,GAAG+C,KAAK,KAAK,gBACzE9E,OAAA;YACEsH,IAAI,EAAC,QAAQ;YACbC,IAAI,EAAC,MAAM;YACXC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,KAAK;YACTxC,SAAS,EAAC,oBAAoB;YAC9BjD,KAAK,EAAE,EAAAoE,MAAA,GAAAb,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAsB,MAAA,uBAAlBA,MAAA,CAAoBpE,KAAK,KAAI,EAAG;YACvC6E,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACxC,QAAQ,EAAE,GAAGgD,KAAK,KAAK,EAAEgC,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAE;YACzEgF,MAAM,EAAExC,UAAW;YACnByC,SAAS;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEFrF,OAAA;YAAKiF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAClC0C,WAAW,EAAArB,MAAA,GAACd,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAuB,MAAA,uBAAlBA,MAAA,CAAoBrE,KAAK,EAAE,KAAK,EAAE,KAAK;UAAC;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN,EACA,EAAAiB,MAAA,GAAAf,GAAG,CAAC,GAAGT,KAAK,KAAK,CAAC,cAAAwB,MAAA,uBAAlBA,MAAA,CAAoBC,UAAU,kBAC7BvG,OAAA;YAAKiF,SAAS,EAAC,4BAA4B;YAAC0B,KAAK,EAAC,0EAAc;YAAA3B,QAAA,EAAC;UAEjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAnFcP,KAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoFV,CAAC;IAAA,CAClB,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAInF,KAAK,IAAK;IACnC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;;IAErB;IACA,MAAM2F,cAAc,GAAGD,WAAW,CAAC1F,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;IAEvD;IACA,MAAM4F,WAAW,GAAGC,MAAM,CAACF,cAAc,CAAC;;IAE1C;IACA,IAAIC,WAAW,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,WAAW,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC9D,OAAOF,WAAW,CAACG,KAAK,CAAC,YAAY,CAAC,CAAClD,GAAG,CAAC,CAACmD,IAAI,EAAEC,KAAK,kBACrDjI,OAAA;QAAiBiF,SAAS,EAAC,mBAAmB;QAAAD,QAAA,EAC3CgD;MAAI,GADGC,KAAK;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN,CAAC;IACJ;;IAEA;IACA,IAAIuC,WAAW,CAACM,MAAM,GAAG,EAAE,EAAE;MAC3B,oBACElI,OAAA;QAAKiF,SAAS,EAAC,2BAA2B;QAAAD,QAAA,EACvC4C;MAAW;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAEV;IAEA,OAAOuC,WAAW;EACpB,CAAC;;EAED;EACA,MAAMF,WAAW,GAAGA,CAAC1F,KAAK,EAAEmG,YAAY,GAAG,KAAK,EAAEC,qBAAqB,GAAG,KAAK,KAAK;IAClF,IAAIA,qBAAqB,IAAIpG,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKqG,SAAS,EAAE;MAClF,OAAOrG,KAAK;IACd;;IAEA;IACA,IAAImG,YAAY,IAAI,OAAOnG,KAAK,KAAK,QAAQ,EAAE;MAC7C,OAAO,CAACA,KAAK,GAAG,GAAG,EAAEsG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACvC;;IAEA;IACA,IAAI,OAAOtG,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA,IAAIuG,MAAM,CAACC,SAAS,CAACxG,KAAK,CAAC,IAAIyG,IAAI,CAACC,GAAG,CAAC1G,KAAK,GAAGyG,IAAI,CAACE,KAAK,CAAC3G,KAAK,CAAC,CAAC,GAAG,MAAM,EAAE;QAC3E,OAAOyG,IAAI,CAACE,KAAK,CAAC3G,KAAK,CAAC,CAAC4G,QAAQ,CAAC,CAAC;MACrC;MACA;MACA,OAAOL,MAAM,CAACvG,KAAK,CAACsG,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;IAC5C;;IAEA;IACA,MAAMhB,WAAW,GAAGC,MAAM,CAAC7F,KAAK,CAAC,CAAC6G,IAAI,CAAC,CAAC;;IAExC;IACA,IAAI,gCAAgC,CAACC,IAAI,CAAClB,WAAW,CAAC,EAAE;MACtD,MAAMmB,QAAQ,GAAGC,UAAU,CAACpB,WAAW,CAAC;;MAExC;MACA,IAAI,CAACqB,KAAK,CAACF,QAAQ,CAAC,IAAIG,QAAQ,CAACH,QAAQ,CAAC,EAAE;QAC1C;QACA,IAAIR,MAAM,CAACC,SAAS,CAACO,QAAQ,CAAC,IAAIN,IAAI,CAACC,GAAG,CAACK,QAAQ,GAAGN,IAAI,CAACE,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,MAAM,EAAE;UACpF,OAAON,IAAI,CAACE,KAAK,CAACI,QAAQ,CAAC,CAACH,QAAQ,CAAC,CAAC;QACxC;QACA;QACA,OAAOL,MAAM,CAACQ,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAIhB,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7B,MAAMqB,OAAO,GAAGvB,WAAW,CAACwB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC;MACnD,IAAI,gCAAgC,CAACC,IAAI,CAACK,OAAO,CAAC,EAAE;QAClD,MAAMJ,QAAQ,GAAGC,UAAU,CAACG,OAAO,CAAC;QACpC,IAAI,CAACF,KAAK,CAACF,QAAQ,CAAC,IAAIG,QAAQ,CAACH,QAAQ,CAAC,EAAE;UAC1C,OAAOR,MAAM,CAACQ,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,GAAG,GAAG;QACrD;MACF;IACF;;IAEA;IACA;IACA,MAAMS,WAAW,GAAGzB,WAAW,CAAC0B,KAAK,CAAC,oBAAoB,CAAC;IAC3D,IAAID,WAAW,EAAE;MACf,MAAME,aAAa,GAAGP,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC;MAChD,IAAI,CAACJ,KAAK,CAACM,aAAa,CAAC,IAAIL,QAAQ,CAACK,aAAa,CAAC,EAAE;QACpD;QACA,IAAIhB,MAAM,CAACC,SAAS,CAACe,aAAa,CAAC,IAAId,IAAI,CAACC,GAAG,CAACa,aAAa,GAAGd,IAAI,CAACE,KAAK,CAACY,aAAa,CAAC,CAAC,GAAG,MAAM,EAAE;UACnG,OAAO3B,WAAW,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACE,KAAK,CAACY,aAAa,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAC;QAClF;QACA;QACA,OAAOhB,WAAW,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEd,MAAM,CAACgB,aAAa,CAACjB,OAAO,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC;MACzF;IACF;;IAEA;IACA,OAAO5G,KAAK;EACd,CAAC;;EAED;EACA,MAAMwH,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,UAAU,GAAG,CAAC,CAAC;IAErB,IAAI,CAACvJ,IAAI,IAAIA,IAAI,CAACgI,MAAM,KAAK,CAAC,EAAE,OAAOuB,UAAU;IAEjD,IAAIC,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,mBAAmB,GAAG,CAAC;IAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5J,IAAI,CAACgI,MAAM,EAAE4B,CAAC,EAAE,EAAE;MACpC,MAAMC,UAAU,GAAG7J,IAAI,CAAC4J,CAAC,CAAC;MAC1B,MAAME,OAAO,GAAG9J,IAAI,CAAC4J,CAAC,GAAG,CAAC,CAAC;;MAE3B;MACA,IAAIC,UAAU,CAACE,EAAE,KAAKD,OAAO,CAACC,EAAE,EAAE;QAChCP,mBAAmB,EAAE;MACvB,CAAC,MAAM;QACL;QACA,IAAIA,mBAAmB,GAAG,CAAC,EAAE;UAC3B,KAAK,IAAIQ,CAAC,GAAGN,kBAAkB,EAAEM,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;YAC3CT,UAAU,CAAC,GAAGS,CAAC,KAAK,CAAC,GAAG;cACtBC,OAAO,EAAED,CAAC,KAAKN,kBAAkB,GAAGF,mBAAmB,GAAG,CAAC;cAC3DU,OAAO,EAAEF,CAAC,KAAKN;YACjB,CAAC;UACH;QACF;QACAF,mBAAmB,GAAG,CAAC;QACvBE,kBAAkB,GAAGE,CAAC;MACxB;;MAEA;MACA,IAAIC,UAAU,CAACM,EAAE,KAAKL,OAAO,CAACK,EAAE,EAAE;QAChCV,oBAAoB,EAAE;MACxB,CAAC,MAAM;QACL;QACA,IAAIA,oBAAoB,GAAG,CAAC,EAAE;UAC5B,KAAK,IAAIO,CAAC,GAAGL,mBAAmB,EAAEK,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;YAC5CT,UAAU,CAAC,GAAGS,CAAC,KAAK,CAAC,GAAG;cACtBC,OAAO,EAAED,CAAC,KAAKL,mBAAmB,GAAGF,oBAAoB,GAAG,CAAC;cAC7DS,OAAO,EAAEF,CAAC,KAAKL;YACjB,CAAC;UACH;QACF;QACAF,oBAAoB,GAAG,CAAC;QACxBE,mBAAmB,GAAGC,CAAC;MACzB;IACF;;IAEA;IACA,IAAIJ,mBAAmB,GAAG,CAAC,EAAE;MAC3B,KAAK,IAAIQ,CAAC,GAAGN,kBAAkB,EAAEM,CAAC,GAAGhK,IAAI,CAACgI,MAAM,EAAEgC,CAAC,EAAE,EAAE;QACrDT,UAAU,CAAC,GAAGS,CAAC,KAAK,CAAC,GAAG;UACtBC,OAAO,EAAED,CAAC,KAAKN,kBAAkB,GAAGF,mBAAmB,GAAG,CAAC;UAC3DU,OAAO,EAAEF,CAAC,KAAKN;QACjB,CAAC;MACH;IACF;IAEA,IAAID,oBAAoB,GAAG,CAAC,EAAE;MAC5B,KAAK,IAAIO,CAAC,GAAGL,mBAAmB,EAAEK,CAAC,GAAGhK,IAAI,CAACgI,MAAM,EAAEgC,CAAC,EAAE,EAAE;QACtDT,UAAU,CAAC,GAAGS,CAAC,KAAK,CAAC,GAAG;UACtBC,OAAO,EAAED,CAAC,KAAKL,mBAAmB,GAAGF,oBAAoB,GAAG,CAAC;UAC7DS,OAAO,EAAEF,CAAC,KAAKL;QACjB,CAAC;MACH;IACF;IAEA,OAAOJ,UAAU;EACnB,CAAC;EAED,MAAMA,UAAU,GAAGD,aAAa,CAAC,CAAC;;EAIlC;EACA,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,oBACEtK,OAAA;MAAAgF,QAAA,gBACEhF,OAAA;QAAAgF,QAAA,EAAI;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACXrF,OAAA;QAAAgF,QAAA,EAAK5E,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG;MAAI;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnDrF,OAAA;QAAAgF,QAAA,EAAI;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACZrF,OAAA;QAAAgF,QAAA,EAAI;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACXrF,OAAA;QAAAgF,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACbrF,OAAA;QAAAgF,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACZT,kBAAkB,CAAC,CAAC;IAAA;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAET,CAAC;;EAED;EACA,MAAMkF,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACrK,IAAI,IAAIA,IAAI,CAACgI,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACElI,OAAA;QAAAgF,QAAA,eACEhF,OAAA;UAAIwK,OAAO,EAAE,CAAC,GAAGnK,gBAAgB,CAAC6H,MAAM,GAAG,CAAE;UAAAlD,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAET;IAEA,OAAOnF,IAAI,CAAC2E,GAAG,CAAC,CAACU,GAAG,EAAEzD,QAAQ,KAAK;MACjC,MAAM2I,iBAAiB,GAAGhB,UAAU,CAAC,GAAG3H,QAAQ,KAAK,CAAC;MACtD,MAAM4I,kBAAkB,GAAGjB,UAAU,CAAC,GAAG3H,QAAQ,KAAK,CAAC;MAEvD,oBACE9B,OAAA;QAAAgF,QAAA,GAEG,CAAC,CAACyF,iBAAiB,IAAIA,iBAAiB,CAACN,OAAO,GAAG,CAAC,kBACnDnK,OAAA;UACEiF,SAAS,EAAC,uBAAuB;UACjCkF,OAAO,EAAE,CAAAM,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEN,OAAO,KAAI,CAAE;UAAAnF,QAAA,EAExCO,GAAG,CAAC0E;QAAE;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACL,EAGA,CAAC,CAACqF,kBAAkB,IAAIA,kBAAkB,CAACP,OAAO,GAAG,CAAC,kBACrDnK,OAAA;UACEiF,SAAS,EAAC,wBAAwB;UAClCkF,OAAO,EAAE,CAAAO,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEP,OAAO,KAAI,CAAE;UAAAnF,QAAA,EAEzCO,GAAG,CAAC8E;QAAE;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACL,eAEDrF,OAAA;UAAIiF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,EAAE0C,WAAW,CAACnC,GAAG,CAACoF,GAAG,EAAE,KAAK,EAAE,IAAI;QAAC;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5ErF,OAAA;UAAIiF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,EAAEO,GAAG,CAACqF;QAAE;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDrF,OAAA;UAAIiF,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAE0C,WAAW,CAACnC,GAAG,CAACsF,IAAI,EAAE,KAAK,EAAE,IAAI;QAAC;UAAA3F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/ErF,OAAA;UAAIiF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,EAAE0C,WAAW,CAACnC,GAAG,CAACuF,IAAI,EAAE,KAAK,EAAE,IAAI;QAAC;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/EC,eAAe,CAACC,GAAG,EAAEzD,QAAQ,CAAC;MAAA,GAzBxBA,QAAQ;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0Bb,CAAC;IAET,CAAC,CAAC;EACJ,CAAC;EAED,oBACErF,OAAA;IAAOiF,SAAS,EAAC,mBAAmB;IAAAD,QAAA,GAEjC,CAAC9D,mBAAmB,GAAG,CAAC,IAAIJ,UAAU,CAACoH,MAAM,GAAG,CAAC,kBAChDlI,OAAA;MAAAgF,QAAA,eACEhF,OAAA;QAAIiF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eAClChF,OAAA;UAAIwK,OAAO,EAAE,CAAC,GAAGnK,gBAAgB,CAAC6H,MAAM,GAAG,CAAE;UAAAlD,QAAA,eAC3ChF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAD,QAAA,GACrC9D,mBAAmB,GAAG,CAAC,iBACtBlB,OAAA;cAAKiF,SAAS,EAAC,yBAAyB;cAAAD,QAAA,gBACtChF,OAAA;gBAAMiF,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GAAC,eACnC,EAAC9D,mBAAmB,EAAC,2BAC1B;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrF,OAAA;gBACEiF,SAAS,EAAC,mBAAmB;gBAC7BwB,OAAO,EAAExD,eAAgB;gBACzB0D,KAAK,EAAC,0BAAM;gBAAA3B,QAAA,EACb;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrF,OAAA;gBACEiF,SAAS,EAAC,mBAAmB;gBAC7BwB,OAAO,EAAEvC,mBAAoB;gBAC7ByC,KAAK,EAAC,4CAAS;gBAAA3B,QAAA,EAChB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEAvE,UAAU,CAACoH,MAAM,GAAG,CAAC,iBACpBlI,OAAA;cAAKiF,SAAS,EAAC,qBAAqB;cAAAD,QAAA,gBAClChF,OAAA;gBAAMiF,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,GAAC,eACjC,EAAClE,UAAU,CAACoH,MAAM,EAAC,qBACxB;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrF,OAAA;gBACEiF,SAAS,EAAC,0BAA0B;gBACpCwB,OAAO,EAAExC,WAAY;gBACrB0C,KAAK,EAAC,0BAAM;gBAAA3B,QAAA,EACb;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrF,OAAA;gBACEiF,SAAS,EAAC,yBAAyB;gBACnCwB,OAAO,EAAEA,CAAA,KAAMxF,iBAAiB,CAAC,IAAI,CAAE;gBACvC0F,KAAK,EAAC,sCAAQ;gBAAA3B,QAAA,EACf;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAEDrF,OAAA;MAAAgF,QAAA,EACGsF,aAAa,CAAC;IAAC;MAAApF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACRrF,OAAA;MAAAgF,QAAA,EACGuF,UAAU,CAAC;IAAC;MAAArF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEZ,CAAC;AAAC9E,EAAA,CAjkBIN,QAAQ;AAAA8K,EAAA,GAAR9K,QAAQ;AAmkBd,eAAeA,QAAQ;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}