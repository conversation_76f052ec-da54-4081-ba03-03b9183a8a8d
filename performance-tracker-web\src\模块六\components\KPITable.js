import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../styles/KPITable.css';
import moduleSixService from '../services/moduleSixService';
import ExportModal from './ExportModal';

const KPITable = ({ data, tableName, viewType, currentMonthPair, onSyncStatus }) => {
  const [editingCell, setEditingCell] = useState(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [syncErrors, setSyncErrors] = useState([]);
  const [showRetryModal, setShowRetryModal] = useState(false);
  const [pendingChangesCount, setPendingChangesCount] = useState(0);

  // 防抖保存相关
  const saveTimeoutRef = useRef(null);
  const pendingSaveRef = useRef(null);
  const retryTimeoutRef = useRef(null);



  // 检查待同步更改数量
  useEffect(() => {
    const updatePendingCount = () => {
      const count = moduleSixService.module6_getPendingChangesCount();
      setPendingChangesCount(count);
    };

    updatePendingCount();
    const interval = setInterval(updatePendingCount, 5000); // 每5秒检查一次

    return () => clearInterval(interval);
  }, []);

  // 防抖保存函数 - 增强错误处理
  const debouncedSave = useCallback(async (rowIndex, field, value) => {
    try {
      onSyncStatus('同步中...');

      // 取消之前的保存操作
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }

      // 创建新的保存操作
      const controller = new AbortController();
      pendingSaveRef.current = controller;

      // 调用服务保存数据
      const syncResult = await moduleSixService.module6_updateCellData(tableName, viewType, rowIndex, field, value);

      // 如果没有被取消，更新状态
      if (!controller.signal.aborted) {
        if (syncResult.backendSync) {
          onSyncStatus('同步成功');
          setTimeout(() => onSyncStatus('已同步'), 1000);
        } else if (syncResult.localSync) {
          onSyncStatus('已保存到本地');
          setTimeout(() => onSyncStatus('等待同步'), 2000);

          // 更新待同步计数
          setPendingChangesCount(prev => prev + 1);

          // 设置自动重试
          if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
          }
          retryTimeoutRef.current = setTimeout(() => {
            handleRetrySync();
          }, 10000); // 10秒后自动重试
        } else {
          throw new Error('保存失败');
        }
        pendingSaveRef.current = null;
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('保存失败:', error);

        // 添加到错误列表
        setSyncErrors(prev => [...prev, {
          id: Date.now(),
          message: error.message || '保存失败',
          field: `${tableName}.${viewType}[${rowIndex}].${field}`,
          value: value,
          timestamp: new Date().toISOString()
        }]);

        onSyncStatus(`同步失败: ${error.message}`);
        setTimeout(() => onSyncStatus('同步失败'), 3000);
      }
    }
  }, [tableName, viewType, onSyncStatus]);

  // 处理重试同步
  const handleRetrySync = useCallback(async () => {
    try {
      onSyncStatus('重试同步中...');
      const result = await moduleSixService.module6_retrySyncPendingChanges();

      if (result.success) {
        setPendingChangesCount(result.remainingCount);

        if (result.syncedCount > 0) {
          onSyncStatus(`成功同步 ${result.syncedCount} 项更改`);
          setTimeout(() => onSyncStatus('已同步'), 2000);
        } else if (result.remainingCount === 0) {
          onSyncStatus('所有更改已同步');
          setTimeout(() => onSyncStatus('已同步'), 2000);
        } else {
          onSyncStatus(`仍有 ${result.remainingCount} 项待同步`);
          setTimeout(() => onSyncStatus('等待同步'), 2000);
        }
      } else {
        throw new Error('重试同步失败');
      }
    } catch (error) {
      console.error('重试同步失败:', error);
      onSyncStatus('重试失败');
      setTimeout(() => onSyncStatus('同步失败'), 2000);
    }
  }, [onSyncStatus]);

  // 清除错误
  const clearErrors = useCallback(() => {
    setSyncErrors([]);
  }, []);

  // 清除所有待同步更改
  const clearPendingChanges = useCallback(() => {
    if (window.confirm('确定要清除所有待同步的更改吗？这将丢失未同步的数据。')) {
      moduleSixService.module6_clearPendingChanges();
      setPendingChangesCount(0);
      onSyncStatus('已清除待同步更改');
      setTimeout(() => onSyncStatus('已同步'), 2000);
    }
  }, [onSyncStatus]);

  // 处理单元格编辑
  const handleCellEdit = (rowIndex, field, value) => {
    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的防抖定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(rowIndex, field, value);
    }, 500);
  };

  // 开始编辑
  const startEdit = (rowIndex, field) => {
    setEditingCell({ rowIndex, field });
  };

  // 完成编辑
  const finishEdit = () => {
    setEditingCell(null);
  };

  // 处理导出
  const handleExport = async (exportConfig) => {
    setExportLoading(true);
    try {
      await moduleSixService.module6_exportData(tableName, viewType, exportConfig);
      onSyncStatus('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      onSyncStatus('导出失败');
    }
    setExportLoading(false);
    setShowExportModal(false);
  };

  // 渲染月份列头
  const renderMonthHeaders = () => {
    return currentMonthPair.map(month => (
      <React.Fragment key={month}>
        <th className="module6_month-header">{month}月工作计划</th>
        <th className="module6_month-header">{month}月完成情况</th>
        <th className="module6_month-header">{month}月评分</th>
      </React.Fragment>
    ));
  };

  // 渲染月份数据 - 优化版本
  const renderMonthData = (row, rowIndex) => {
    return currentMonthPair.map(month => (
      <React.Fragment key={month}>
        {/* 工作计划单元格 */}
        <td
          className={`module6_month-cell ${row[`${month}月工作计划`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}
          onClick={() => !row[`${month}月工作计划`]?.isReadOnly && startEdit(rowIndex, `${month}月工作计划`)}
          title={row[`${month}月工作计划`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}
        >
          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月工作计划` ? (
            <textarea
              className="module6_cell-textarea"
              value={row[`${month}月工作计划`]?.value || ''}
              onChange={(e) => handleCellEdit(rowIndex, `${month}月工作计划`, e.target.value)}
              onBlur={finishEdit}
              autoFocus
              rows={3}
            />
          ) : (
            <div className="module6_cell-content">
              {renderCellContent(row[`${month}月工作计划`]?.value)}
            </div>
          )}
          {row[`${month}月工作计划`]?.isReadOnly && (
            <div className="module6_readonly-indicator" title="此数据来自源表，不可编辑">
              🔒
            </div>
          )}
        </td>

        {/* 完成情况单元格 */}
        <td
          className={`module6_month-cell ${row[`${month}月完成情况`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}
          onClick={() => !row[`${month}月完成情况`]?.isReadOnly && startEdit(rowIndex, `${month}月完成情况`)}
          title={row[`${month}月完成情况`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}
        >
          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月完成情况` ? (
            <textarea
              className="module6_cell-textarea"
              value={row[`${month}月完成情况`]?.value || ''}
              onChange={(e) => handleCellEdit(rowIndex, `${month}月完成情况`, e.target.value)}
              onBlur={finishEdit}
              autoFocus
              rows={3}
            />
          ) : (
            <div className="module6_cell-content">
              {renderCellContent(row[`${month}月完成情况`]?.value)}
            </div>
          )}
          {row[`${month}月完成情况`]?.isReadOnly && (
            <div className="module6_readonly-indicator" title="此数据来自源表，不可编辑">
              🔒
            </div>
          )}
        </td>

        {/* 评分单元格 */}
        <td
          className={`module6_month-cell ${row[`${month}月评分`]?.isReadOnly ? 'readonly' : 'editable'} ${row._isMatched ? 'matched' : 'unmatched'}`}
          onClick={() => !row[`${month}月评分`]?.isReadOnly && startEdit(rowIndex, `${month}月评分`)}
          title={row[`${month}月评分`]?.isReadOnly ? `数据来源: ${row._matchedSource || '源表'}` : '可编辑字段'}
        >
          {editingCell?.rowIndex === rowIndex && editingCell?.field === `${month}月评分` ? (
            <input
              type="number"
              step="0.01"
              min="0"
              max="100"
              className="module6_cell-input"
              value={row[`${month}月评分`]?.value || ''}
              onChange={(e) => handleCellEdit(rowIndex, `${month}月评分`, e.target.value)}
              onBlur={finishEdit}
              autoFocus
            />
          ) : (
            <div className="module6_cell-content">
              {formatValue(row[`${month}月评分`]?.value, false, false)}
            </div>
          )}
          {row[`${month}月评分`]?.isReadOnly && (
            <div className="module6_readonly-indicator" title="此数据来自源表，不可编辑">
              🔒
            </div>
          )}
        </td>
      </React.Fragment>
    ));
  };

  // 渲染单元格内容 - 支持换行显示和数字格式化
  const renderCellContent = (value) => {
    if (!value) return '';

    // 首先格式化数字
    const formattedValue = formatValue(value, false, false);

    // 将格式化后的值转换为字符串
    const stringValue = String(formattedValue);

    // 如果内容包含换行符，则分行显示
    if (stringValue.includes('\n') || stringValue.includes('\r\n')) {
      return stringValue.split(/\r\n|\n|\r/).map((line, index) => (
        <div key={index} className="module6_cell-line">
          {line}
        </div>
      ));
    }

    // 如果内容过长，自动换行
    if (stringValue.length > 50) {
      return (
        <div className="module6_cell-long-content">
          {stringValue}
        </div>
      );
    }

    return stringValue;
  };

  // 格式化数值显示 - 修复长小数问题
  const formatValue = (value, isPercentage = false, excludeFromFormatting = false) => {
    if (excludeFromFormatting || value === '' || value === null || value === undefined) {
      return value;
    }

    // 处理百分比
    if (isPercentage && typeof value === 'number') {
      return (value * 100).toFixed(2) + '%';
    }

    // 处理数值类型
    if (typeof value === 'number') {
      // 检查是否为整数或接近整数（避免浮点精度问题）
      if (Number.isInteger(value) || Math.abs(value - Math.round(value)) < 0.0001) {
        return Math.round(value).toString();
      }
      // 小数保留两位小数
      return Number(value.toFixed(2)).toString();
    }

    // 检查字符串是否为纯数值
    const stringValue = String(value).trim();

    // 如果是纯数字字符串（包括科学计数法）
    if (/^-?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(stringValue)) {
      const numValue = parseFloat(stringValue);

      // 检查是否为有效数字
      if (!isNaN(numValue) && isFinite(numValue)) {
        // 检查是否为整数或接近整数
        if (Number.isInteger(numValue) || Math.abs(numValue - Math.round(numValue)) < 0.0001) {
          return Math.round(numValue).toString();
        }
        // 小数保留两位小数，并移除末尾的0
        return Number(numValue.toFixed(2)).toString();
      }
    }

    // 检查是否包含百分号
    if (stringValue.includes('%')) {
      const numPart = stringValue.replace('%', '').trim();
      if (/^-?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(numPart)) {
        const numValue = parseFloat(numPart);
        if (!isNaN(numValue) && isFinite(numValue)) {
          return Number(numValue.toFixed(2)).toString() + '%';
        }
      }
    }

    // 特殊处理：如果字符串包含数字但不是纯数字（如 "88.8%=32/36"）
    // 检查是否以数字开头
    const numberMatch = stringValue.match(/^(-?\d+(?:\.\d+)?)/);
    if (numberMatch) {
      const leadingNumber = parseFloat(numberMatch[1]);
      if (!isNaN(leadingNumber) && isFinite(leadingNumber)) {
        // 如果是整数或接近整数，格式化为整数
        if (Number.isInteger(leadingNumber) || Math.abs(leadingNumber - Math.round(leadingNumber)) < 0.0001) {
          return stringValue.replace(numberMatch[1], Math.round(leadingNumber).toString());
        }
        // 否则保留两位小数
        return stringValue.replace(numberMatch[1], Number(leadingNumber.toFixed(2)).toString());
      }
    }

    // 非数值内容直接返回
    return value;
  };

  // 合并单元格逻辑
  const getMergedInfo = () => {
    const mergedInfo = {};
    
    if (!data || data.length === 0) return mergedInfo;
    
    let currentSequenceSpan = 1;
    let currentIndicatorSpan = 1;
    let sequenceStartIndex = 0;
    let indicatorStartIndex = 0;
    
    for (let i = 1; i < data.length; i++) {
      const currentRow = data[i];
      const prevRow = data[i - 1];
      
      // 检查序号是否相同
      if (currentRow.序号 === prevRow.序号) {
        currentSequenceSpan++;
      } else {
        // 保存前一组的合并信息
        if (currentSequenceSpan > 1) {
          for (let j = sequenceStartIndex; j < i; j++) {
            mergedInfo[`${j}_序号`] = {
              rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,
              isFirst: j === sequenceStartIndex
            };
          }
        }
        currentSequenceSpan = 1;
        sequenceStartIndex = i;
      }
      
      // 检查指标是否相同
      if (currentRow.指标 === prevRow.指标) {
        currentIndicatorSpan++;
      } else {
        // 保存前一组的合并信息
        if (currentIndicatorSpan > 1) {
          for (let j = indicatorStartIndex; j < i; j++) {
            mergedInfo[`${j}_指标`] = {
              rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,
              isFirst: j === indicatorStartIndex
            };
          }
        }
        currentIndicatorSpan = 1;
        indicatorStartIndex = i;
      }
    }
    
    // 处理最后一组
    if (currentSequenceSpan > 1) {
      for (let j = sequenceStartIndex; j < data.length; j++) {
        mergedInfo[`${j}_序号`] = {
          rowSpan: j === sequenceStartIndex ? currentSequenceSpan : 0,
          isFirst: j === sequenceStartIndex
        };
      }
    }
    
    if (currentIndicatorSpan > 1) {
      for (let j = indicatorStartIndex; j < data.length; j++) {
        mergedInfo[`${j}_指标`] = {
          rowSpan: j === indicatorStartIndex ? currentIndicatorSpan : 0,
          isFirst: j === indicatorStartIndex
        };
      }
    }
    
    return mergedInfo;
  };

  const mergedInfo = getMergedInfo();



  // 渲染表头
  const renderHeaders = () => {
    // 关键指标和重点工作都使用相同的表头结构
    return (
      <tr>
        <th>序号</th>
        <th>{viewType === 'keyWork' ? '重点工作项目' : '指标'}</th>
        <th>目标值</th>
        <th>权重</th>
        <th>指标分类</th>
        <th>跟踪频次</th>
        {renderMonthHeaders()}
      </tr>
    );
  };

  // 渲染表格行
  const renderRows = () => {
    if (!data || data.length === 0) {
      return (
        <tr>
          <td colSpan={6 + currentMonthPair.length * 3}>
            当前视图没有可显示的数据。
          </td>
        </tr>
      );
    }

    return data.map((row, rowIndex) => {
      const sequenceMergeInfo = mergedInfo[`${rowIndex}_序号`];
      const indicatorMergeInfo = mergedInfo[`${rowIndex}_指标`];
      
      return (
        <tr key={rowIndex}>
          {/* 序号列 - 支持合并 */}
          {(!sequenceMergeInfo || sequenceMergeInfo.rowSpan > 0) && (
            <td 
              className="module6_sequence-cell"
              rowSpan={sequenceMergeInfo?.rowSpan || 1}
            >
              {row.序号}
            </td>
          )}
          
          {/* 指标列 - 支持合并 */}
          {(!indicatorMergeInfo || indicatorMergeInfo.rowSpan > 0) && (
            <td 
              className="module6_indicator-cell"
              rowSpan={indicatorMergeInfo?.rowSpan || 1}
            >
              {row.指标}
            </td>
          )}
          
          <td className="module6_target-cell">{formatValue(row.目标值, false, true)}</td>
          <td className="module6_weight-cell">{row.权重}</td>
          <td className="module6_category-cell">{formatValue(row.指标分类, false, true)}</td>
          <td className="module6_frequency-cell">{formatValue(row.跟踪频次, false, true)}</td>
          {renderMonthData(row, rowIndex)}
        </tr>
      );
    });
  };

  return (
    <table className="module6_kpi-table">
      {/* 同步状态和错误处理工具栏 - 作为表格的一部分 */}
      {(pendingChangesCount > 0 || syncErrors.length > 0) && (
        <thead>
          <tr className="module6_sync-toolbar">
            <td colSpan={6 + currentMonthPair.length * 3}>
              <div className="module6_toolbar-content">
                {pendingChangesCount > 0 && (
                  <div className="module6_pending-changes">
                    <span className="module6_pending-count">
                      📤 {pendingChangesCount} 项待同步
                    </span>
                    <button
                      className="module6_retry-btn"
                      onClick={handleRetrySync}
                      title="重试同步"
                    >
                      🔄 重试
                    </button>
                    <button
                      className="module6_clear-btn"
                      onClick={clearPendingChanges}
                      title="清除待同步更改"
                    >
                      🗑️ 清除
                    </button>
                  </div>
                )}

                {syncErrors.length > 0 && (
                  <div className="module6_sync-errors">
                    <span className="module6_error-count">
                      ⚠️ {syncErrors.length} 个错误
                    </span>
                    <button
                      className="module6_clear-errors-btn"
                      onClick={clearErrors}
                      title="清除错误"
                    >
                      ✖️ 清除错误
                    </button>
                    <button
                      className="module6_show-errors-btn"
                      onClick={() => setShowRetryModal(true)}
                      title="查看错误详情"
                    >
                      📋 详情
                    </button>
                  </div>
                )}
              </div>
            </td>
          </tr>
        </thead>
      )}
      
      <thead>
        {renderHeaders()}
      </thead>
      <tbody>
        {renderRows()}
      </tbody>

      {showExportModal && (
        <ExportModal
          onClose={() => setShowExportModal(false)}
          onExport={handleExport}
          loading={exportLoading}
          data={data}
        />
      )}

      {/* 错误详情模态框 */}
      {showRetryModal && (
        <div className="module6_modal-overlay">
          <div className="module6_error-modal">
            <div className="module6_modal-header">
              <h3>同步错误详情</h3>
              <button
                className="module6_modal-close"
                onClick={() => setShowRetryModal(false)}
              >
                ✖️
              </button>
            </div>
            <div className="module6_modal-content">
              {syncErrors.length === 0 ? (
                <p>没有错误记录</p>
              ) : (
                <div className="module6_error-list">
                  {syncErrors.map(error => (
                    <div key={error.id} className="module6_error-item">
                      <div className="module6_error-field">{error.field}</div>
                      <div className="module6_error-message">{error.message}</div>
                      <div className="module6_error-time">
                        {new Date(error.timestamp).toLocaleString()}
                      </div>
                      <div className="module6_error-value">
                        值: {error.value}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="module6_modal-footer">
              <button
                className="module6_retry-all-btn"
                onClick={() => {
                  handleRetrySync();
                  setShowRetryModal(false);
                }}
              >
                🔄 重试所有
              </button>
              <button
                className="module6_clear-all-btn"
                onClick={() => {
                  clearErrors();
                  clearPendingChanges();
                  setShowRetryModal(false);
                }}
              >
                🗑️ 清除所有
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default KPITable; 