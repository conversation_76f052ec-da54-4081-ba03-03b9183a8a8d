/* 模块六主页面样式 - 高科技风格 + 金黄色主题 */
.module6_module-six {
  min-height: 100vh;
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  position: relative;
  overflow-x: auto;
}

/* 背景动画 */
.module6_module-six-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.module6_module-six-background .module6_particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ffd700;
  border-radius: 50%;
  animation: module6_float 6s ease-in-out infinite;
}

.module6_module-six-background .module6_particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.module6_module-six-background .module6_particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.module6_module-six-background .module6_particle:nth-child(3) {
  top: 80%;
  left: 30%;
  animation-delay: 4s;
}

.module6_module-six-background .module6_grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: module6_gridMove 20s linear infinite;
}

/* 顶部导航栏 */
.module6_module-six-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 2px solid rgba(255, 215, 0, 0.5);
  backdrop-filter: blur(15px);
  position: relative;
  z-index: 10;
}

.module6_header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.module6_header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 2;
}

.module6_header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.module6_back-button {
  padding: 10px 20px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border: none;
  border-radius: 8px;
  color: #000;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.module6_module-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.4rem;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
  margin: 0;
}

.module6_header-right {
  display: flex;
  align-items: center;
}

/* 状态按钮样式（类似返回首页按钮） */
.module6_status-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border: none;
  border-radius: 8px;
  color: #000;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: default;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  min-width: 140px;
  justify-content: center;
}

.module6_status-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

/* 不同状态的颜色变体 - 统一使用黄色主题 */
.module6_status-button.success {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_status-button.success:hover {
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.module6_status-button.error {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_status-button.error:hover {
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.module6_status-button.syncing {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.module6_status-button.syncing:hover {
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

/* 保留原有的状态指示器样式 */
.module6_sync-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffd700;
  font-size: 0.9rem;
  font-weight: 500;
}

.module6_status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: module6_pulse 2s ease-in-out infinite;
}

.module6_status-indicator.success {
  background: #000;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
}

.module6_status-indicator.error {
  background: #000;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
}

.module6_status-indicator.syncing {
  background: #000;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
}

/* 表选择器 */
.module6_table-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  position: relative;
  z-index: 10;
}

.module6_table-selector label {
  font-size: 1.1rem;
  color: #ffd700;
  font-weight: 600;
}

.module6_table-dropdown {
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 215, 0, 0.5);
  border-radius: 8px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.module6_table-dropdown:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.module6_table-dropdown option {
  background: #1a1a2e;
  color: #ffffff;
}



/* 合并到一行的统一头部布局 */
.module6_unified-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  gap: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 左侧和右侧的指标/工作选择器 */
.module6_header-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 25px;
  background: transparent;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 180px;
  justify-content: center;
}

.module6_header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.module6_header-section:hover::before {
  left: 100%;
}

.module6_header-section.active {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  color: #ffd700;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.6);
}

.module6_header-section:hover {
  color: #ffd700;
  transform: translateY(-2px);
  border-color: rgba(255, 215, 0, 0.5);
}

.module6_section-icon {
  font-size: 1.3rem;
  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
}

.module6_section-title {
  font-weight: 700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  white-space: nowrap;
}

/* 月份选择器 */
.module6_month-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  background: transparent;
  border-radius: 8px;
  padding: 12px 20px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
}

.module6_month-selector .module6_month-nav-btn {
  width: 35px;
  height: 35px;
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 6px;
  color: #ffd700;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module6_month-selector .module6_month-nav-btn:hover {
  background: rgba(255, 215, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.7);
  transform: scale(1.1);
}

.module6_month-selector .module6_month-display {
  font-family: 'Orbitron', monospace;
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  min-width: 100px;
  text-align: center;
}





/* 加载状态 */
.module6_loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
}

.module6_loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 215, 0, 0.3);
  border-top: 3px solid #ffd700;
  border-radius: 50%;
  animation: module6_spin 1s linear infinite;
}

.module6_loading-text {
  color: #ffd700;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 动画 */
@keyframes module6_float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes module6_gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes module6_pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes module6_spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表格样式优化 - 移除重复定义，使用KPITable.css中的样式 */

/* 减少行间距 */
.module6_kpi-table tbody tr {
  height: 40px;
}

.module6_kpi-table tbody tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.02);
}

.module6_kpi-table tbody tr:hover {
  background: rgba(255, 215, 0, 0.05);
}

/* 特殊列样式 - 保持与KPITable.css一致 */
.module6_sequence-cell,
.module6_indicator-cell {
  background: rgba(255, 215, 0, 0.05);
  font-weight: 600;
}

/* 序号列特定样式覆盖 - 精确调整宽度 */
.module6_sequence-cell {
  min-width: 60px !important;
  max-width: 80px !important;
  width: 70px !important;
  padding: 12px 4px !important;
}

.module6_month-cell {
  /* 优化后的月份列宽度 */
  min-width: 200px;
}

.module6_month-cell.editable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.module6_month-cell.editable:hover {
  background: rgba(255, 215, 0, 0.1);
}

.module6_month-cell.readonly {
  background: rgba(128, 128, 128, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

/* 滚动条样式 */
.module6_table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.module6_table-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.module6_table-container::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 4px;
}

.module6_table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .module6_module-title {
    font-size: 2rem;
  }
  
  .module6_unified-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .module6_header-section {
    min-width: 160px;
    padding: 12px 20px;
  }
}

@media (max-width: 768px) {
  .module6_module-six-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }
  
  .module6_header-left {
    flex-direction: column;
    gap: 10px;
  }
  
  .module6_module-title {
    font-size: 1.6rem;
    text-align: center;
  }
  
  .module6_table-selector {
    flex-direction: column;
    gap: 10px;
  }
  
  .module6_table-dropdown {
    min-width: 150px;
  }
  
  .module6_module-six-content {
    padding: 20px 15px;
  }
} 