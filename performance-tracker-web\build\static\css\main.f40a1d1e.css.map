{"version": 3, "file": "static/css/main.f40a1d1e.css", "mappings": "sHACA,EAGE,qBAAsB,CADtB,SAEF,CAEA,OALE,QAeF,CAVA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,gFAAqF,CACrF,UAAc,CALd,4JACgF,CAKhF,gBAAiB,CACjB,iBACF,CAEA,KACE,0CACF,CAGA,oBACE,SACF,CAEA,0BACE,oBACF,CAEA,0BACE,iDAAoD,CACpD,iBACF,CAEA,gCACE,iDACF,CAQA,mBACE,GAAO,SAAU,CAAE,2BAA8B,CACjD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,gBACE,MAAW,6BAA6C,CACxD,IAAM,6BAA6C,CACrD,CCtDA,KAEE,gBAAiB,CACjB,iBAAkB,CAFlB,iBAGF,CAGA,gBAQE,kBAAmB,CAFnB,gFAAqF,CACrF,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YACF,CAEA,iBAEE,4BAA6B,CAD7B,iBAEF,CAEA,yBAIE,aAAc,CAHd,8BAAkC,CAClC,cAAe,CACf,eAAgB,CAOhB,kBAAmB,CADnB,mBAAqB,CAJrB,oEAMF,CAEA,6BAGE,aAAc,CAFd,+BAAmC,CACnC,gBAAiB,CAEjB,kBAAmB,CACnB,kBAAmB,CACnB,UACF,CAEA,kBAGE,oBAAoC,CACpC,iBAAkB,CAFlB,UAAW,CAIX,kBAAmB,CADnB,eAAgB,CAEhB,iBAAkB,CANlB,WAOF,CAEA,cAIE,6CAA8C,CAF9C,yDAA6D,CAC7D,yBAA0B,CAE1B,iBAAkB,CAJlB,WAKF,CAEA,uBACE,GAEE,0BAA2B,CAD3B,OAEF,CACA,IAEE,0BAA2B,CAD3B,SAEF,CACA,GAEE,uBAAyB,CADzB,UAEF,CACF,CAEA,gBAKE,uCAAwC,CAFxC,UAAc,CAFd,+BAAmC,CACnC,gBAAiB,CAEjB,UAEF,CAGA,yBACE,yBACE,cAAe,CACf,kBACF,CAEA,6BACE,cAAe,CACf,kBACF,CAEA,kBACE,WACF,CACF,CAEA,yBACE,yBACE,gBAAiB,CACjB,kBACF,CAEA,6BACE,eAAiB,CACjB,kBACF,CAEA,kBACE,WACF,CACF,CCvHA,UAIE,YAAa,CACb,qBAAsB,CAJtB,gBAAiB,CAEjB,eAAgB,CADhB,iBAIF,CAGA,sBAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,UAME,mCAAoC,CAFpC,kBAAmB,CACnB,iBAAkB,CAElB,6BAA2C,CAJ3C,UAAW,CAFX,iBAAkB,CAClB,SAMF,CAEA,sBAGE,kBAAmB,CACnB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,uBAGE,mBAAoB,CACpB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,uBAGE,oBAAqB,CACrB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,iBACE,GAAiD,SAAU,CAAtD,qCAAwD,CAC7D,IAAM,SAAY,CAClB,IAAM,SAAY,CAClB,GAAuD,SAAU,CAA1D,6CAA4D,CACrE,CAEA,YAUE,sCAAuC,CAJvC,oGAEsE,CACtE,yBAA0B,CAJ1B,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAOF,CAEA,oBACE,GAAK,sBAA4B,CACjC,GAAO,8BAAkC,CAC3C,CAGA,cACE,iBAAkB,CAElB,UAAW,CAEX,gBAAiB,CAHjB,QAAS,CAET,UAEF,CAEA,cAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,eAAgB,CAChB,8BACF,CAEA,eAGE,aAAc,CACd,cAAe,CACf,UACF,CAGA,kCARE,+BAAmC,CACnC,eAoBF,CAbA,mBACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAQlB,0BAAwC,CAVxC,aAAc,CAOd,cAAe,CAFf,eAAgB,CAIhB,eAAgB,CANhB,gBAAiB,CAKjB,uBAGF,CAEA,yBAEE,+BAA6C,CAD7C,0BAEF,CAGA,aAEE,sBAAuB,CAEvB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,iBAEE,aAAc,CADd,gBAEF,CAEA,sBAUE,4BAA6B,CAN7B,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAMhB,kBAAmB,CADnB,kBAAmB,CAHnB,iDAMF,CAEA,WAOE,qCAAuC,CAHvC,aAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAGhB,kBAAmB,CADnB,kBAGF,CAEA,eAKE,qCAAuC,CAFvC,qDAAqE,CADrE,UAAW,CAEX,gBAAiB,CAHjB,WAKF,CAEA,oBAKE,qCAAuC,CAFvC,WAA+B,CAF/B,+BAAmC,CACnC,gBAAiB,CAEjB,kBAEF,CAGA,iBAGE,aAAS,CAFT,YAAa,CAQb,QAAO,CANP,QAAS,CADT,wDAA2D,CAI3D,aAAc,CADd,gBAAiB,CADjB,mBAAoB,CAIpB,iBAAkB,CADlB,SAGF,CAEA,UAUE,gDAAkD,CADlD,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAEnB,cAAe,CAGf,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CADlB,uBAKF,CAEA,gBAEE,yBAA0B,CAC1B,mDAEiC,CAJjC,2BAKF,CAEA,2BACE,SACF,CAEA,WAME,mEAA4E,CAD5E,QAAS,CAFT,MAAO,CAOP,qBAAsB,CAHtB,SAAU,CAEV,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,2BAGF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,WAEE,sBAAuB,CADvB,gBAAiB,CAEjB,0BACF,CAEA,2BACE,mBACF,CAEA,aAIE,kBAAmB,CAHnB,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,UACF,CAEA,cAEE,kBAAmB,CADnB,eAEF,CAEA,YAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,iBAEF,CAEA,2BARE,+BAgBF,CARA,eAGE,kBAAmB,CADnB,cAAe,CAIf,kBAAmB,CAFnB,kBAAmB,CAGnB,UAAY,CAFZ,wBAGF,CAEA,kBAGE,eAA+B,CAF/B,+BAAmC,CACnC,gBAAkB,CAElB,eACF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,SAAU,CACV,0BAA2B,CAC3B,uBACF,CAEA,6BACE,SAAU,CACV,uBACF,CAEA,YAGE,kBAAmB,CAFnB,+BAAmC,CACnC,cAAe,CAEf,eACF,CAEA,OAGE,4CAA6C,CAD7C,kBAAmB,CADnB,gBAGF,CAEA,sBACE,MAAW,uBAA0B,CACrC,IAAM,yBAA4B,CACpC,CAGA,YAKE,oBAA8B,CAC9B,8BAA4C,CAL5C,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,YAAa,CAIb,iBAAkB,CADlB,SAEF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,cAGE,WAA+B,CAF/B,+BAAmC,CACnC,eAEF,CAEA,cAGE,aAAc,CAFd,8BAAkC,CAClC,eAAiB,CAEjB,eACF,CAEA,qBACE,uCACF,CAGA,0BACE,iBAEE,QAAS,CADT,wDAA2D,CAE3D,mBACF,CACF,CAEA,yBACE,YACE,gBAAiB,CACjB,kBACF,CAEA,WACE,gBACF,CAEA,iBAEE,QAAS,CADT,yBAA0B,CAE1B,mBACF,CAEA,UACE,YACF,CAEA,YACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,cAGE,YAAa,CAFb,eAAgB,CAChB,iBAEF,CACF,CAEA,yBACE,YACE,cAAe,CACf,kBACF,CAEA,WACE,gBACF,CAEA,aACE,sBACF,CAEA,iBACE,mBACF,CAEA,UACE,YACF,CACF,CCzZA,uBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,qBAUE,mCAAqC,CATrC,kDAA6D,CAC7D,0BAA0C,CAC1C,kBAAmB,CAMnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAJhB,SAAU,CACV,SAMF,CAEA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,sBAEE,kBAAmB,CAInB,oBAAqC,CADrC,iCAAiD,CAJjD,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAEA,yBAEE,UAAc,CACd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAJhB,QAKF,CAEA,WAWE,kBAAmB,CARnB,UAAc,CAOd,YAAa,CANb,cAAe,CAKf,WAAY,CAGZ,sBAAuB,CANvB,WAAY,CAEZ,UAMF,CAEA,iBACE,oBAEF,CAEA,uBAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAEA,mBACE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,qBAEE,aAAc,CACd,gBAAkB,CAFlB,QAGF,CAEA,kBACE,kBACF,CAEA,aAOE,oBAAqC,CANrC,2BAA2C,CAC3C,kBAAmB,CAGnB,cAAe,CAFf,iBAAkB,CAClB,iBAAkB,CAElB,uBAEF,CAEA,mBAEE,oBAAmC,CADnC,oBAAqB,CAErB,0BACF,CAEA,aACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,eAEE,UAAc,CACd,+BAAmC,CAFnC,YAGF,CAEA,aAEE,qBAA0C,CAD1C,gBAAkB,CAElB,yBACF,CAEA,oBACE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,sBAEE,aAAc,CACd,eAAiB,CAFjB,YAGF,CAEA,eAEE,iBAAkB,CAElB,eAAiB,CACjB,eAAgB,CAFhB,kBAAmB,CAFnB,iBAKF,CAEA,uBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,qBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,oBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,uBAKE,oBAAqC,CADrC,8BAA8C,CAH9C,YAAa,CACb,QAAS,CACT,iBAGF,CAEA,wBAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAPf,QAAO,CAIP,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,YACE,oBAAoC,CAEpC,sBAA0C,CAD1C,UAEF,CAEA,iCACE,gBAAoC,CACpC,0BACF,CAEA,YACE,iDAAoD,CACpD,aACF,CAEA,iCAEE,+BAA6C,CAD7C,0BAEF,CAEA,0CAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAGA,0CACE,SACF,CAEA,gDACE,oBAAoC,CACpC,iBACF,CAEA,gDACE,oBAAkC,CAClC,iBACF,CAEA,sDACE,oBACF,CAGA,yBACE,qBAEE,WAAY,CADZ,SAEF,CAEA,sBACE,iBACF,CAEA,uBACE,YACF,CAEA,uBAEE,qBAAsB,CADtB,iBAEF,CAEA,aACE,iBACF,CAEA,aACE,gBACF,CACF,CC5QA,aAEE,gFAAqF,CACrF,UAAc,CACd,+BAAmC,CAHnC,gBAIF,CAGA,aASE,kCAA2B,CAA3B,0BAA2B,CAF3B,gBAA8B,CAC9B,iCAA+C,CAF/C,iBAIF,CAEA,aAEE,iDAAoD,CAEpD,iBAAkB,CAClB,UAAW,CACX,+BAAmC,CACnC,eAAiB,CANjB,gBAUF,CAEA,mBAEE,+BACF,CAEA,yBAUE,kBAAmB,CANnB,aAAc,CAKd,YAAa,CAHb,QAAO,CALP,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAQhB,sBAAuB,CAJvB,aAAc,CAMd,eAAgB,CARhB,iBAAkB,CASlB,sBAAuB,CANvB,8BAA4C,CAI5C,kBAGF,CAEA,aAIE,aAAc,CACd,eAAiB,CAFjB,OAGF,CAEA,YAKE,uCAAwC,CAFxC,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAGA,eAKE,gBAA8B,CAJ9B,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,iBAEF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,OACF,CAEA,iBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,8BACF,CAEA,gBAEE,WAA+B,CAD/B,eAEF,CAGA,cAGE,aAAc,CADd,gBAAiB,CADjB,sBAGF,CAGA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aAWE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CAEnB,cAAe,CAKf,YAAa,CAEb,QAAS,CAJT,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CADlB,uBAOF,CAEA,uCAIE,oBAAqC,CADrC,iCAAkC,CAElC,6DAE+B,CAL/B,0BAMF,CAEA,UAEE,sBAAuB,CADvB,cAAe,CAEf,0BACF,CAEA,2DAEE,mBACF,CAEA,aACE,QAAO,CACP,eACF,CAEA,WAQE,kBAAmB,CAJnB,UAAc,CAGd,YAAa,CANb,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAMhB,sBAAuB,CAJvB,iBAAkB,CAClB,iBAIF,CAEA,WACE,eAAiB,CAEjB,UACF,CAEA,sBAJE,0BAQF,CAJA,WACE,cAAe,CAEf,6BACF,CAGA,eAKE,6BAA+B,CAJ/B,oBAAqC,CAGrC,0BAAwC,CAFxC,kBAAmB,CACnB,YAGF,CAEA,cAME,iCAAiD,CAJjD,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,+BANE,kBAAmB,CAFnB,YAiBF,CATA,iBAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAMjB,sBAAuB,CAJvB,QAAS,CACT,iBAIF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,WAEE,aAAc,CADd,eAAiB,CAEjB,UACF,CAEA,aAEE,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAGd,cAAe,CAFf,+BAAmC,CACnC,eAAiB,CANjB,gBAAiB,CAQjB,uBACF,CAEA,mBACE,oBACF,CAGA,eASE,kBAAmB,CAJnB,oBAAkC,CAElC,0BAAwC,CADxC,iBAAkB,CALlB,YAAa,CAOb,eAAiB,CANjB,QAAS,CAQT,6BAA8B,CAP9B,kBAAmB,CACnB,iBAOF,CAEA,oBACE,aAAc,CACd,eACF,CAGA,wBAIE,oBAAmC,CAGnC,0BAAwC,CADxC,iBAAkB,CALlB,sBAAyB,CAEzB,wCAA6C,CAK7C,0BAA6B,CAN7B,yBAA2B,CAG3B,gBAIF,CAGA,sBAGE,0BAA0C,CAD1C,iBAAkB,CADlB,eAGF,CAEA,YAGE,gBAA8B,CAD9B,wBAAyB,CAEzB,eAAiB,CAEjB,eAAgB,CADhB,kBAAmB,CAJnB,UAMF,CAGA,wBAA0B,QAAW,CACrC,2BAA6B,SAAY,CACzC,wBAA0B,SAAY,CACtC,wBAA0B,QAAW,CACrC,0BAA4B,SAAY,CAExC,uDAA+B,SAAY,CAE3C,eACE,sDAAmF,CAOnF,+BAAgC,CANhC,UAAc,CAGd,8BAAkC,CAClC,gBAAkB,CAClB,eAAgB,CAJhB,iBAAkB,CAMlB,uBAAgB,CAAhB,eAAgB,CALhB,iBAAkB,CASlB,+BAAyC,CAHzC,KAAM,CAEN,qBAAsB,CADtB,UAGF,CAEA,UACE,uBACF,CAEA,gBACE,oBAAmC,CAEnC,8BAA4C,CAD5C,0BAEF,CAEA,wBACE,oBACF,CAOA,YACE,8BAEF,CAEA,kBACE,8BACF,CAEA,+BACE,sDAAoF,CAIpF,gCAA8C,CAD9C,eAAgB,CAEhB,iBAAkB,CAJlB,iBAAkB,CAClB,qBAIF,CAEA,qCAOE,kDAAwD,CAFxD,QAAS,CAJT,UAAW,CAEX,MAAO,CADP,iBAAkB,CAElB,KAAM,CAEN,SAEF,CAEA,WAOE,oBAAqB,CALrB,iCAAkD,CAClD,gCAAiD,CAKjD,UAAc,CACd,eAAgB,CARhB,gBAAiB,CAIjB,iBAAkB,CADlB,uBAAyB,CAEzB,qBAKF,CAMA,0BACE,oBAAmC,CACnC,sBAAoC,CACpC,kCACF,CAEA,cAKE,oBAAqB,CAFrB,UAAc,CAMd,eAAgB,CALhB,eAAgB,CAEhB,wBAAyB,CAIzB,+BAAyC,CAHzC,kBAAmB,CALnB,UASF,CAEA,eAIE,sBAA6B,CAH7B,iBAAkB,CAClB,eAAgB,CAChB,uBAEF,CAEA,qBACE,oBAAkC,CAClC,sBACF,CAEA,YAGE,gBAA8B,CAC9B,wBAAyB,CACzB,iBAAkB,CAIlB,gBAAkB,CAIlB,eAAgB,CADhB,eAAgB,CAFhB,YAAa,CAJb,eASF,CAEA,kBAEE,oBAAiC,CADjC,6BAEF,CAGA,iBAGE,WAA+B,CAD/B,iBAAkB,CADlB,iBAGF,CAEA,oBAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,kBACF,CAEA,mBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAGA,SAEE,YAEF,CAGA,qBAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,gBAEF,CAEA,iBAIE,0BAA6B,CAA7B,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAGA,0BACE,cACE,yBACF,CAEA,cACE,sBACF,CAGA,wBAA0B,WAAc,CACxC,0BAA4B,WAAc,CAC5C,CAEA,yBACE,eACE,cAAe,CACf,QACF,CAEA,aACE,qBAAsB,CACtB,QAAS,CACT,kBAAmB,CACnB,iBACF,CAEA,YACE,gBAAiB,CACjB,QACF,CAEA,cAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,YACE,eACF,CAEA,8BAEE,eACF,CAGA,wBAA0B,WAAc,CACxC,0BAA4B,WAAc,CAC5C,CAcA,iBACE,MAAW,UAAc,CACzB,IAAM,SAAY,CACpB,CAQA,gBACE,8BAA8C,CAC9C,sCACF,CAEA,sBACE,8BACF,CAEA,gBACE,oBAAkC,CAGlC,oCAAkD,CADlD,UAAc,CADd,eAGF,CAEA,uBACE,gEAAgG,CAChG,0CACF,CAEA,6BACE,2DACF,CAGA,cAcE,kBAAmB,CADnB,YAAa,CALb,eAAiB,CAOjB,OAAQ,CAHR,mBAAqB,CARrB,gBAAiB,CAOjB,wBAKF,CAEA,oBAGE,kDAAqD,CADrD,+BAEF,CAEA,qBACE,uBACF,CCzlBA,4BASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,0BACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAAmF,CAEnF,iCAIF,CAEA,iBACE,aAAc,CAGd,8BACF,CA0BA,kBAKE,oBAAiC,CAHjC,0BAAwC,CACxC,kBAAmB,CAFnB,kBAAmB,CAGnB,eAEF,CAEA,iBAKE,kBAAmB,CAJnB,qDAAqF,CAKrF,iCAA+C,CAH/C,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,mBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,gBAAiB,CACjB,eACF,CAEA,wCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,gBACE,aAAc,CACd,6BACF,CAEA,kBACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAwC,CACxC,aAMF,CAEA,kBACE,sDAAoF,CAEpF,+BACF,CAGA,YACE,gBAEF,CAEA,UAEE,iCAA+C,CAD/C,iBAAkB,CAElB,uBACF,CAEA,gBACE,oBACF,CAEA,yBACE,oBAAkC,CAClC,4BACF,CAEA,eAEE,kBAAmB,CAEnB,UACF,CAEA,oCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CACf,QAAS,CAJT,6BAA8B,CAE9B,UAGF,CAEA,WACE,aAAc,CAEd,QAAO,CADP,gBAEF,CAEA,aAGE,oBAAmC,CAGnC,0BAAyC,CADzC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAGF,CAEA,iBACE,iDAAqD,CAIrD,kBAAmB,CAHnB,UAAY,CACZ,gBAAkB,CAGlB,eAAgB,CAFhB,eAAgB,CAGhB,+BACF,CAGA,cAGE,8BAMF,CAEA,YAEE,QAGF,CAqBA,yBAEE,0BAAwC,CACxC,aAKF,CAEA,+BAEE,oBAAqB,CACrB,6BACF,CA+BA,cACE,kDAGF,CAEA,mCAEE,+BACF,CAWA,iEAEE,SACF,CAEA,6EAEE,oBAAiC,CACjC,iBACF,CAEA,6EAEE,kDAAqD,CACrD,iBACF,CAEA,yFAEE,kDACF,CAGA,yBACE,0BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,iBAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,sBACF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CACF,CC7WA,yBAEE,gFAAqF,CACrF,UAAc,CAEd,+BAAmC,CAJnC,gBAAiB,CAGjB,YAEF,CAiBA,cAKE,iBAMF,CA2BA,kBAGE,eAAiB,CAFjB,gBAIF,CAqBA,eAIE,YAGF,CAEA,iBAIE,kBAEF,CAcA,eACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAHhB,gBAIF,CAEA,qBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAGA,kBAIE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAHnB,QAAS,CAET,iBAGF,CAGA,sBAME,kBAAmB,CADnB,iBAIF,CAEA,aAIE,kBAAmB,CAInB,eAAiB,CAHjB,gBAQF,CAgBA,oBAGE,gBAAiB,CACjB,eAIF,CAEA,iBAIE,kBAAmB,CAOnB,+BAA8C,CAN9C,iBAAkB,CAOlB,kBACF,CAEA,uBAGE,iDAAoD,CADpD,+BAA8C,CAD9C,0BAGF,CAEA,SACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAFnB,UAAW,CAMX,cAAe,CAFf,+BAAmC,CACnC,eAAgB,CAFhB,gBAAiB,CAIjB,uBACF,CAEA,8BAEE,+BAA6C,CAD7C,qBAEF,CAEA,kBACE,oBAAoC,CACpC,eAA+B,CAC/B,kBACF,CAEA,gBACE,aAAc,CAEd,gBAAiB,CACjB,eAEF,CASA,uBAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,+BAAmC,CACnC,eAAgB,CAJhB,iBAAkB,CAMlB,uBACF,CAEA,aACE,iDAAoD,CACpD,UACF,CAEA,UACE,iDAAoD,CACpD,UACF,CAEA,mCAEE,+BAA6C,CAD7C,0BAEF,CAGA,0BAaE,eAAgB,CADhB,iBAEF,CAGA,iDATE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAQnB,+BAA6C,CAJ7C,YAAa,CAGb,QAAS,CADT,6BAA8B,CAJ9B,kBAAmB,CADnB,YAyBF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,YAAa,CAFb,sBAAuB,CAGvB,iBAAkB,CAFlB,WAGF,CAEA,oBAGE,WAAY,CACZ,kBAAmB,CAHnB,iBAAkB,CAClB,UAGF,CAEA,eAQE,wCAAyC,CAFzC,yDAA6D,CAC7D,2BAEF,CAEA,6BANE,sBAA6B,CAD7B,iBAAkB,CADlB,WAAY,CAFZ,iBAAkB,CAClB,UAeF,CAEA,QAEE,6CAA8C,CAD9C,4BAEF,CAEA,QAEE,qDAAsD,CADtD,8BAEF,CAEA,QAEE,+CAAgD,CADhD,+BAEF,CAEA,cAOE,sDAAmF,CAEnF,6BAA2C,CAH3C,WAAY,CADZ,UAKF,CAEA,0BAJE,iBAAkB,CALlB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAkBF,CAVA,YASE,4CAA6C,CAF7C,kBAAmB,CADnB,WAAY,CADZ,UAKF,CAEA,WAME,WAAY,CAHZ,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAChC,UAEF,CAEA,aAKE,wCAAyC,CADzC,gDAA4D,CAD5D,UAAW,CAFX,iBAAkB,CAClB,SAIF,CAEA,qBACE,mBAAqB,CACrB,wBACF,CAEA,qBACE,kBAAmB,CACnB,wBACF,CAEA,WAEE,QAAS,CAGT,WAAY,CAFZ,MAAO,CAGP,UAAY,CALZ,iBAAkB,CAGlB,OAGF,CAEA,WAKE,sCAAuC,CAHvC,qDAAqE,CACrE,UAAW,CAFX,iBAAkB,CAGlB,UAEF,CAEA,uBAAkC,kBAAmB,CAA3B,KAA6B,CACvD,wBAAoC,mBAAqB,CAA/B,OAAiC,CAC3D,wBAAqC,kBAAmB,CAA9B,QAAgC,CAC1D,wBAAqC,oBAAqB,CAAhC,QAAkC,CAG5D,sBAGE,kBAAmB,CAFnB,YAAa,CAIb,QAAO,CAHP,QAAS,CAET,sBAEF,CAGA,eAGE,kBACF,CAEA,WACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAGnB,cAAe,CAFf,iBAMF,CAEA,kBAOE,uDAAsF,CADtF,WAAY,CAFZ,UAAW,CADX,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,wBACE,SACF,CAEA,iBAGE,sBAAoC,CADpC,+BAA6C,CAD7C,0BAGF,CAEA,aAGE,aAAc,CADd,eAAgB,CAEhB,iBAAkB,CAElB,8BACF,CAEA,WAEE,aAAc,CACd,eAAgB,CAChB,UACF,CAGA,iBAEE,sDAAmF,CACnF,0BAAwC,CAFxC,cAAe,CAGf,uBACF,CAEA,uBACE,sDAAmF,CACnF,sBAAoC,CACpC,+BAA6C,CAC7C,sCACF,CAEA,8BACE,YAAc,CACd,8BACF,CAEA,4BACE,UACF,CAEA,aAEE,oBAAqB,CADrB,gBAAiB,CAEjB,6BACF,CAEA,oCACE,oBACF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,YAEE,QAAS,CACT,kBAGF,CAEA,WAEE,0BAAwC,CACxC,kBAAmB,CACnB,iBAEF,CAEA,+BAEE,iBAAkB,CADlB,iBAEF,CAEA,YACE,aAIF,CAEA,YACE,UAAc,CACd,gBAEF,CAGA,0BACE,YACF,CAEA,iBACE,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CACnB,kBAAmB,CACnB,eACF,CAEA,cACE,qDAAkF,CAGlF,iCAA+C,CAD/C,cAAe,CADf,iBAAkB,CAGlB,uBACF,CAEA,oBACE,qDACF,CAEA,aAEE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAIb,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,eACE,aAAc,CACd,cAAe,CACf,6BACF,CAGA,sBAGE,sDAAmF,CAGnF,0BAAwC,CADxC,kBAAmB,CAGnB,6BAA2C,CAP3C,UAAc,CAQd,oBAAqB,CAPrB,eAAgB,CAQhB,YAAa,CANb,eAAgB,CAGhB,6BAA2C,CAI3C,uBACF,CAEA,4BAEE,sDAAmF,CACnF,sBAAoC,CAEpC,6BAA2C,CAJ3C,UAAc,CAGd,8BAA4C,CAE5C,qBACF,CAGA,iBAEE,YACF,CAEA,gBAGE,oBAAqC,CADrC,wBAAyB,CAEzB,kBAAmB,CACnB,eAAgB,CAJhB,UAKF,CAEA,mBACE,qDAAkF,CAMlF,iCAA+C,CAL/C,UAAc,CAId,eAAiB,CADjB,eAAgB,CAFhB,iBAAkB,CAKlB,uBAAgB,CAAhB,eAAgB,CAJhB,iBAAkB,CAKlB,KAAM,CACN,UACF,CAEA,mBAEE,iCAAiD,CADjD,gBAAiB,CAEjB,iBAAkB,CAElB,uBAAyB,CADzB,qBAEF,CAEA,mBACE,oBACF,CAIA,UAAY,WAAc,CAC1B,eAAiB,WAAc,CAC/B,kBAAoB,WAAc,CAClC,YAAc,WAAc,CAC5B,YAAc,WAAc,CAC5B,iBAAmB,WAAc,CACjC,eAAiB,UAAa,CAC9B,oCAAuC,WAAc,CAGrD,oBACE,cAAe,CACf,iBACF,CAEA,0BACE,oBAAkC,CAClC,0BACF,CAEA,YAEE,wBAAyB,CAOzB,qBAAsB,CAJtB,eAKF,CAEA,kBAEE,oBAAqB,CACrB,4BACF,CAEA,cAGE,eACF,CAGA,YACE,oBAAoC,CACpC,+BACF,CAEA,gBACE,oBAAmC,CACnC,+BACF,CAGA,mBAGE,kBAAmB,CAGnB,gFAAqF,CACrF,UAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,gBAGF,CAEA,iBAIE,0BAA6B,CAA7B,wBAIF,CAQA,uBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,sBACE,MAEE,SAAU,CADV,uCAEF,CACA,IAEE,UAAY,CADZ,yCAEF,CACF,CAEA,qBACE,GAEE,SAAU,CADV,oCAEF,CACA,IAEE,SAAU,CADV,kCAEF,CACA,GAEE,SAAU,CADV,mCAEF,CACF,CAEA,qBACE,GAEE,SAAU,CADV,2BAEF,CACA,IACE,SACF,CACA,GAEE,SAAU,CADV,0BAEF,CACF,CAGA,SAIE,gBACF,CAGA,0BACE,0BAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,iBAGE,kBAAmB,CADnB,WAAY,CAEZ,sBAAuB,CAHvB,UAIF,CAEA,oBAEE,WAAY,CACZ,eAAgB,CAChB,iBAAkB,CAHlB,UAIF,CAEA,sBAEE,cAAe,CACf,QAAS,CAFT,sBAGF,CAEA,kBAGE,kBAAmB,CAFnB,qBAAsB,CACtB,QAEF,CAEA,sBAEE,sBAAuB,CADvB,UAEF,CAEA,iBAEE,eAAgB,CADhB,UAEF,CAEA,uBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,eAEE,cAAe,CACf,QAAS,CAFT,sBAGF,CAEA,iBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,kBACE,qBAAsB,CACtB,QACF,CAEA,YAEE,kBAAmB,CADnB,qBAEF,CACF,CAEA,yBACE,yBACE,YACF,CAEA,YACE,gBACF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,eACE,OACF,CAEA,cAEE,qBAAsB,CADtB,OAEF,CAEA,aAEE,iBAAkB,CADlB,OAEF,CAEA,WACE,cAAe,CACf,iBACF,CAEA,aACE,gBACF,CAEA,aAEE,eAAiB,CADjB,gBAEF,CAEA,oBACE,cAAe,CACf,cACF,CAEA,iBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,oBAEE,WAAY,CACZ,iBAAkB,CAFlB,UAGF,CAEA,sBACE,QACF,CAEA,eACE,YACF,CAEA,gBAEE,cAAe,CADf,sBAEF,CAEA,gBACE,eACF,CAEA,sCAEE,eACF,CACF,CAiBA,0FACE,iDACF,CAGA,wBAWE,kBAAmB,CAMnB,2CAA4C,CAV5C,0DAA8D,CAD9D,iBAAkB,CAOlB,qDAE0C,CAP1C,cAAe,CAEf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CATvB,SAAU,CAFV,cAAe,CACf,SAAU,CAcV,uBAAyB,CAZzB,UAAW,CAKX,YASF,CAEA,8BAKE,cAAe,CAHf,0DAE0C,CAH1C,qCAKF,CAEA,+BAQE,0DAA8D,CAD9D,iBAAkB,CADlB,WAAY,CALZ,UAAW,CASX,gBAAiB,CANjB,SAAU,CAOV,UAAY,CATZ,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAMT,UAGF,CAEA,UAEE,kBAAmB,CADnB,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CACvB,UAEF,CAEA,aAEE,UAAW,CAEX,gDAAqD,CAHrD,cAAe,CAEf,+BAEF,CAEA,aAKE,gBAA8B,CAU9B,0BAAwC,CAPxC,kBAAmB,CAFnB,aAAc,CAGd,cAAe,CACf,eAAgB,CAPhB,SAAU,CASV,SAAU,CALV,gBAAiB,CANjB,iBAAkB,CAClB,OAAQ,CAER,0BAA2B,CAU3B,uBAAyB,CADzB,iBAAkB,CAFlB,kBAKF,CAEA,2CACE,SAAU,CACV,kBACF,CAEA,qBACE,MACE,qDAGF,CACA,IACE,0DAGF,CACF,CAGA,0BAeE,qCAAuC,CAJvC,kCAA2B,CAA3B,0BAA2B,CAJ3B,oBAAkC,CAClC,0BAAwC,CACxC,kBAAmB,CAGnB,mDAEiC,CAXjC,QAAS,CAGT,gBAAiB,CALjB,cAAe,CACf,OAAQ,CAER,8BAAgC,CAChC,WAAY,CAKZ,YAMF,CAEA,0BACE,GACE,SAAU,CACV,wCACF,CACA,GACE,SAAU,CACV,uCACF,CACF,CAEA,qBAGE,kBAAmB,CAEnB,iCAA+C,CAJ/C,YAAa,CACb,6BAA8B,CAE9B,YAEF,CAEA,wBACE,aAAc,CACd,8BAAkC,CAClC,gBAAiB,CACjB,QAAS,CACT,8BACF,CAEA,iBAWE,kBAAmB,CAVnB,oBAAkC,CAElC,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAOd,cAAe,CACf,YAAa,CAHb,cAAe,CACf,eAAiB,CAFjB,WAAY,CAMZ,sBAAuB,CACvB,uBAAyB,CARzB,UASF,CAEA,uBACE,oBAAkC,CAClC,oBACF,CAEA,sBACE,YACF,CAEA,gBACE,YAAa,CACb,kBAAmB,CACnB,cAAe,CACf,OAAQ,CAGR,kBAAmB,CAFnB,gBAAiB,CACjB,eAEF,CAEA,eACE,oBAAqC,CACrC,0BAAwC,CACxC,iBAAkB,CAUlB,qBAAsB,CATtB,UAAc,CAKd,cAAe,CAGf,6BAA4B,CAA5B,WAA4B,CAA5B,aAA4B,CAN5B,+BAAmC,CACnC,gBAAkB,CAClB,eAAgB,CAHhB,gBAAiB,CAMjB,iBAAkB,CADlB,uBAIF,CAEA,qBACE,oBAAmC,CACnC,oBAAqB,CACrB,yBACF,CAEA,wBACE,qDAAkF,CAClF,oBAAqB,CAErB,+BAA6C,CAD7C,aAEF,CAEA,gBACE,YAAa,CACb,OAAQ,CACR,6BACF,CAEA,oCAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAPf,QAAO,CAIP,+BAAmC,CAEnC,gBAAkB,CADlB,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,kBACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAEA,wBACE,oBAAkC,CAClC,0BACF,CAEA,kBACE,iDAAoD,CAEpD,wBAAyB,CADzB,UAEF,CAEA,wBACE,iDAAoD,CAEpD,+BAA6C,CAD7C,0BAEF,CAGA,sBAQE,kBAAmB,CACnB,wCAA0C,CAR1C,qDAAoF,CACpF,0BAAwC,CACxC,kBAAmB,CAGnB,YAAa,CACb,6BAA8B,CAF9B,kBAAmB,CADnB,iBAMF,CAEA,6BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,oBACE,aAAc,CAGd,gBAAiB,CACjB,6BACF,CAEA,sCANE,+BAAmC,CACnC,eAeF,CAVA,kBACE,oBAAkC,CAElC,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAMd,cAAe,CAHf,gBAAiB,CAIjB,uBACF,CAEA,wBACE,oBAAkC,CAClC,qBACF,CAGA,mCACE,SACF,CAEA,yCACE,oBAAoC,CACpC,iBACF,CAEA,yCACE,iDAAoD,CACpD,iBACF,CAEA,+CACE,iDACF,CAGA,yBACE,wBAEE,WAAY,CAEZ,SAAU,CADV,SAAU,CAFV,UAIF,CAEA,aACE,cACF,CAEA,0BAEE,eAAgB,CADhB,SAEF,CAEA,aAEE,cAAe,CADf,SAEF,CACF,CAKA,sBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAGA,uBAoBE,kBAAmB,CAnBnB,0DAA8D,CAE9D,WAAY,CACZ,kBAAmB,CAWnB,mEAGwC,CAhBxC,UAAW,CAOX,cAAe,CAUf,YAAa,CAbb,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAchB,OAAQ,CAER,sBAAuB,CATvB,kBAAmB,CAQnB,eAAgB,CAVhB,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CAElB,wBAAyB,CAHzB,uBAcF,CAEA,8BAOE,uDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,mDACE,SACF,CAEA,4CAME,0DAA8D,CAJ9D,yEAGoC,CAJpC,sCAMF,CAEA,8BACE,sCAAuC,CACvC,uBACF,CAEA,gCACE,oBAAoC,CAIpC,sDAEwC,CALxC,eAA+B,CAC/B,kBAAmB,CACnB,cAIF,CAEA,uCACE,YACF,CAGA,uBAME,uCAAwC,CADxC,0BAAsB,CADtB,iBAAkB,CAClB,qBAAsB,CAHtB,WAAY,CADZ,UAMF,CAEA,sBACE,GACE,uBACF,CACF,CAGA,6BAYE,yCAA0C,CAL1C,iFACuD,CAGvD,yBAA0B,CAF1B,kBAAmB,CAHnB,WAAY,CALZ,UAAW,CAGX,SAAU,CASV,SAAU,CAXV,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAWT,2BAA6B,CAJ7B,UAKF,CAEA,kDACE,UACF,CAEA,0BACE,GACE,yBACF,CACA,IACE,4BACF,CACA,GACE,yBACF,CACF,CAGA,uBAIE,QAAO,CACP,aACF,CAEA,2CANE,kBAAmB,CADnB,YAAa,CAEb,sBAgBF,CAXA,oBAIE,cAAe,CAMf,iDAAsD,CAPtD,YAAa,CAFb,iBAAkB,CAKlB,0CAAiD,CADjD,wBAAiB,CAAjB,gBAAiB,CAHjB,WASF,CAEA,0BAEE,iDAAsD,CADtD,0BAEF,CAEA,wBAaE,kCAA2B,CAA3B,0BAA2B,CAN3B,oEAGgC,CAEhC,0BAAwC,CAGxC,sDAEyC,CANzC,sFAA0F,CAA1F,8EAA0F,CAN1F,YAAa,CAFb,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAIR,8BAAgC,CAQhC,0CAAiD,CAVjD,WAcF,CAEA,kDACE,oEAGgC,CAEhC,sDAE0C,CAH1C,uDAIF,CAEA,iBASE,yCAA0C,CAC1C,mEAGkB,CANlB,0BAAyC,CACzC,sFAA0F,CAA1F,8EAA0F,CAH1F,YAAa,CAFb,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAIR,8BAAgC,CAFhC,WAUF,CAWA,6EACE,YACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,qBAME,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,sBAAuB,CANvB,iBAAkB,CAClB,UAAW,CAMX,UACF,CAEA,gBAGE,WAAY,CACZ,iBAAkB,CAHlB,iBAAkB,CAClB,UAGF,CAEA,gBAWE,yCAA0C,CAL1C,+DAG0B,CAC1B,kBAAmB,CALnB,WAAY,CAOZ,gBAAiB,CATjB,SAAU,CAFV,iBAAkB,CAGlB,UAAW,CAFX,QAWF,CAEA,WAGE,4CAA6C,CAF7C,iBAAkB,CAClB,SAEF,CAEA,sBACE,MACE,uBACF,CACA,IACE,0BACF,CACF,CAEA,WACE,YACF,CAEA,qBACE,GAEE,SAAU,CADV,KAEF,CACA,IACE,SACF,CACA,IACE,SACF,CACA,GAEE,SAAU,CADV,QAEF,CACF,CAEA,aACE,iBACF,CAEA,cAQE,6BAAoC,CAFpC,yDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CARrB,aAAc,CACd,eAAiB,CACjB,eAAgB,CAEhB,iBAAkB,CADlB,6BAMF,CAEA,kBAKE,WAAY,CAJZ,iBAAkB,CAElB,WAAY,CADZ,SAAU,CAEV,UAEF,CAEA,WAKE,2CAA4C,CAF5C,kBAAmB,CACnB,iBAAkB,CAElB,6BAA2C,CAJ3C,WAAY,CADZ,UAMF,CAEA,qBACE,MAEE,SAAU,CADV,kBAEF,CACA,IAEE,UAAY,CADZ,oBAEF,CACF,CAUA,6EAGE,YACF,CAEA,sBACE,MACE,SAAU,CACV,mBACF,CACA,IACE,SAAU,CACV,mBACF,CACF,CAGA,yBACE,sBACE,sBAAuB,CACvB,UACF,CAEA,uBAGE,eAAiB,CAFjB,eAAgB,CAChB,iBAEF,CAEA,oBAEE,YAAa,CADb,WAEF,CAEA,uBACE,aACF,CACF,CCjsDA,gCASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,8BACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAMF,CAEA,iBACE,aAAc,CAGd,8BACF,CA0BA,iBACE,oBAAkC,CAClC,0BAIF,CAcA,cACE,aAGF,CAEA,cAEE,oBAAmC,CAGnC,0BAAyC,CAJzC,aAKF,CAEA,oBAEE,0BAAwC,CACxC,aAKF,CAEA,0BAEE,oBAAqB,CACrB,6BACF,CAWA,8CAGE,oBAEF,CAGA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mBAIE,oBAAiC,CAHjC,0BAAwC,CACxC,kBAAmB,CACnB,eAEF,CAEA,kBAKE,kBAAmB,CAJnB,qDAAqF,CAKrF,iCAA+C,CAH/C,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,oBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,gBAAiB,CACjB,eACF,CAEA,yCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,iBACE,aAAc,CACd,6BACF,CAEA,mBACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAwC,CACxC,aAMF,CAEA,kBACE,sDAAoF,CAEpF,+BACF,CAGA,iBACE,gBAAiB,CACjB,eACF,CAEA,eAEE,iCAA+C,CAD/C,iBAAkB,CAElB,uBACF,CAEA,qBACE,oBACF,CAEA,oBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,UACF,CAEA,yCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,UACF,CAEA,gBACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAEA,uBAOE,qBAAsB,CAJtB,oBAAmC,CAGnC,0BAAyC,CADzC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAIF,CAcA,YAGE,aAGF,CAEA,iBACE,oBAAmC,CAGnC,0BAAyC,CACzC,6BAEF,CAaA,yBAEE,0BAAwC,CACxC,aAKF,CAEA,+BAEE,oBAAqB,CACrB,6BACF,CA+BA,cACE,kDAGF,CAEA,mCAEE,+BACF,CAWA,sEAEE,SACF,CAEA,kFAEE,oBAAiC,CACjC,iBACF,CAEA,kFAEE,kDAAqD,CACrD,iBACF,CAEA,8FAEE,kDACF,CAGA,yBACE,8BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,kBAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YAEE,cAAe,CADf,sBAEF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CC9aA,uBAEE,gFAAqF,CACrF,UAAc,CAId,qBAAsB,CAFtB,+BAAmC,CAJnC,gBAAiB,CAGjB,YAMF,CAGA,oCALE,kBAAmB,CAFnB,YAAa,CAGb,UAYF,CARA,aAGE,6BAA8B,CAC9B,kBAAmB,CAEnB,cAAe,CADf,iBAGF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,eAEE,QAAO,CADP,iBAEF,CAEA,cACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,gBAAiB,CADjB,eAAgB,CAIhB,QAAS,CANT,iBAAkB,CAKlB,uBAEF,CAEA,oBAEE,+BAA6C,CAD7C,0BAEF,CAEA,YAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,eAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,UACF,CAEA,aAEE,kBAAmB,CADnB,YAEF,CAEA,kBAEE,kBAAmB,CACnB,cAAe,CAFf,gBAIF,CAQA,wBACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAEA,0BACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAGA,eAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CASxC,cAAe,CACf,QAAS,CAPT,kBAAmB,CADnB,iBAAkB,CAGlB,UAMF,CAGA,eAIE,QACF,CAEA,gCALE,kBAAmB,CADnB,YAAa,CAEb,cAUF,CANA,iBAGE,QAAS,CAET,0BACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,oBACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,gBACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAFf,gBAAiB,CAGjB,WACF,CAEA,sBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,uBACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,6BACE,oBAAkC,CAClC,UACF,CAEA,gBACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAEf,eAAgB,CAJhB,gBAAiB,CAKjB,sBAAuB,CACvB,kBAAmB,CAHnB,WAIF,CAEA,sBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,uBACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,6BACE,oBAAkC,CAClC,UACF,CAGA,sBAEE,kBAAmB,CAGnB,oBAAmC,CAGnC,0BAAwC,CADxC,kBAAmB,CAEnB,oCAAkD,CARlD,YAAa,CAGb,QAAS,CADT,sBAAuB,CAGvB,gBAAiB,CAIjB,yBAAkB,CAAlB,iBACF,CAEA,aACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA6C,CAV7C,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,eAAiB,CADjB,eAAgB,CAFhB,gBAAiB,CAMjB,gBAAiB,CADjB,uBAGF,CAEA,kCAGE,iDAAoD,CADpD,+BAA6C,CAD7C,sCAGF,CAEA,sBACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAEA,oBACE,aAAc,CAMd,8BAAkC,CAJlC,gBAAkB,CADlB,eAAgB,CAEhB,cAAe,CACf,iBAAkB,CAClB,6BAEF,CAEA,iBACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA8C,CAV9C,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAMhB,QAAS,CART,iBAAkB,CAMlB,gBAAiB,CADjB,uBAIF,CAEA,uBAEE,+BAA8C,CAD9C,sCAEF,CAUA,YACE,YAAa,CAGb,cAAe,CAFf,QAAS,CAGT,sBAAuB,CAFvB,kBAAmB,CAGnB,UACF,CAEA,WAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CACnB,iBAGF,CAEA,YACE,aAAc,CACd,eAAiB,CACjB,gBACF,CAEA,YACE,aAAc,CAEd,cAAe,CADf,eAEF,CAGA,kBAQE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,iBAAkB,CAIlB,YAAa,CAEb,OAAQ,CACR,eAAgB,CANhB,gBAAiB,CACjB,iBAMF,CAEA,8BACE,aAAc,CACd,eACF,CAEA,8BACE,aAAc,CAEd,cAAe,CADf,eAEF,CAGA,iBAGE,oBAAqC,CACrC,0BAAwC,CAFxC,kBAAmB,CAInB,aAAc,CACd,8BAA+B,CAN/B,eAAgB,CAOhB,eAAgB,CAHhB,UAIF,CAEA,mBAEE,wBAAyB,CACzB,+BAAmC,CAFnC,UAGF,CASA,+CALE,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CACV,WAeF,CAZA,sBACE,8DAA0E,CAI1E,0BAAwC,CAMxC,8BAAwC,CATxC,aAAc,CAKd,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAClB,iBAQF,CAGA,sBAEE,0BAA0C,CAD1C,iBAAkB,CAIlB,iBAAkB,CADlB,iBAAkB,CADlB,qBAGF,CAEA,mBACE,oBAAkC,CAClC,sBACF,CAGA,YAAyB,cAAe,CAA1B,QAA4B,CAE1C,yBAA2B,eAAgB,CAA3B,QAA6B,CAC7C,YAA0B,eAAgB,CAA5B,SAA8B,CAC5C,aAA2B,eAAgB,CAA5B,SAA8B,CAC7C,iBAA8B,eAAgB,CAA3B,QAA6B,CAChD,YAAyB,cAAe,CAA1B,QAA4B,CAC1C,YAAyB,eAAgB,CAA3B,QAA6B,CAC3C,oCAAkD,eAAgB,CAA3B,QAA6B,CAGpE,eACE,cAAe,CACf,uBACF,CAEA,qBACE,oBAAmC,CACnC,oBACF,CAEA,YAEE,oBAAoC,CACpC,wBAAyB,CACzB,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,eAAiB,CAFjB,WAAY,CAGZ,eAAgB,CAChB,iBAAkB,CATlB,UAUF,CAEA,kBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,cACE,aAAc,CAId,iBAAkB,CAFlB,oBAAqB,CADrB,qBAIF,CAGA,YACE,8BAA6C,CAC7C,gCACF,CAEA,gBACE,8BAA8C,CAC9C,gCACF,CAGA,qBAGE,kBAAmB,CAFnB,YAAa,CAIb,qBAAsB,CACtB,QAAS,CAJT,sBAAuB,CAEvB,gBAGF,CAEA,sCAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,uBACE,aAAc,CACd,gBAAiB,CACjB,iBACF,CAGA,eACE,YAAa,CAGb,cAAe,CAFf,QAAS,CAGT,sBAAuB,CAFvB,kBAAmB,CAGnB,UACF,CAEA,WAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAKnB,eAAgB,CAEhB,eAAgB,CANhB,YAAa,CAKb,iBAAkB,CAJlB,iBAAkB,CAElB,uBAIF,CAEA,kBAQE,mCAAoC,CADpC,oDAAgF,CANhF,UAAW,CAKX,WAAY,CAFZ,SAAU,CAKV,SAAU,CAPV,iBAAkB,CAClB,QAAS,CAOT,2BAA6B,CAL7B,UAMF,CAEA,wBACE,SACF,CAEA,iBAEE,oBAAqB,CACrB,gCAA8C,CAF9C,0BAGF,CAEA,aAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,WACE,UAAc,CACd,eAAiB,CACjB,UACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,sBACF,CAEA,cACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,eAAiB,CADjB,eAAgB,CAFhB,gBAAiB,CAKjB,uBACF,CAEA,oBAEE,+BAA+C,CAD/C,0BAEF,CAGA,0BACE,iBACE,QACF,CAEA,sBACE,cAAe,CACf,QACF,CAEA,eACE,QACF,CAEA,WACE,eACF,CAGA,oCAAmD,eAAgB,CAA5B,SAA8B,CACvE,CAEA,yBACE,uBACE,YACF,CAEA,YACE,gBACF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,eACE,OACF,CAEA,cACE,OAAQ,CACR,UACF,CAEA,aACE,OACF,CAEA,WACE,QAAO,CACP,eACF,CAEA,aACE,gBACF,CAEA,aAEE,eAAiB,CADjB,gBAEF,CAEA,oBACE,cACF,CAEA,eACE,YACF,CAEA,iBACE,qBAAsB,CACtB,QACF,CAEA,mBACE,eACF,CAEA,4CAEE,eACF,CAGA,iBACE,8BACF,CACF,CAGA,oCACE,UAAW,CACX,SACF,CAEA,0CACE,oBAAoC,CACpC,iBACF,CAEA,0CACE,oBAAkC,CAClC,iBACF,CAEA,gDACE,oBACF,CAQA,kBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,sBACE,GACE,+CACF,CACA,IACE,gDACF,CACA,GACE,+CACF,CACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,4BAGE,kBAAmB,CAWnB,4CAA6C,CAP7C,kBAAmB,CACnB,0BAAwC,CACxC,iBAAkB,CAGlB,aAAc,CAFd,cAAe,CARf,YAAa,CAWb,cAAe,CAPf,WAAY,CAFZ,sBAAuB,CAHvB,iBAAkB,CAUlB,oDAA4D,CAN5D,UAUF,CAEA,mCAWE,0CAA2C,CAF3C,sBAAyB,CAFzB,iBAAkB,CAElB,wBAAyB,CAHzB,WAAY,CALZ,UAAW,CAGX,SAAU,CAFV,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAOT,8BAEF,CAEA,kCAGE,oBAAkC,CADlC,sBAAoC,CADpC,qBAGF,CAEA,yCACE,qBACF,CAEA,yCACE,6BACF,CAEA,+CACE,uBACF,CAEA,0CAYE,kBAAmB,CARnB,kDAAqD,CAWrD,0BAA0C,CAT1C,iBAAkB,CAQlB,6BAA6C,CAT7C,UAAY,CAMZ,YAAa,CAFb,cAAe,CACf,eAAiB,CAFjB,WAAY,CAKZ,sBAAuB,CAZvB,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAeN,6BAA+B,CAV/B,UAAW,CASX,SAEF,CAGA,SAGE,WAA+B,CAC/B,gBAAiB,CAFjB,YAAa,CAGb,UACF,CAGA,sCAiBE,kCAAoC,CAPpC,kCAA2B,CAA3B,0BAA2B,CAH3B,sDAAiF,CACjF,sBAAwC,CACxC,kBAAmB,CAEnB,uEAGwC,CAXxC,QAAS,CAGT,gBAAiB,CAUjB,eAAgB,CAfhB,cAAe,CACf,OAAQ,CAER,8BAAgC,CAChC,WAAY,CAUZ,YAGF,CAEA,uBACE,GACE,SAAU,CACV,wCACF,CACA,GACE,SAAU,CACV,uCACF,CACF,CAEA,iCAGE,kBAAmB,CAEnB,qDAAkF,CAClF,6BAA+C,CAL/C,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,oCAEE,UAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAAS,CAIT,8BACF,CAEA,6BAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAMZ,iBAAkB,CALlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAGf,WAAY,CAIZ,sBAAuB,CACvB,uBAAyB,CANzB,UAOF,CAEA,mCACE,oBAAoC,CACpC,uBACF,CAEA,kCACE,YAAa,CACb,qBAAsB,CAEtB,gBAAiB,CACjB,iBAAkB,CAFlB,mBAGF,CAEA,4BAIE,YAAa,CAHb,QAAO,CAIP,kBAAmB,CACnB,cAAe,CACf,OAAQ,CACR,kBAAmB,CANnB,eAAgB,CAChB,gBAMF,CAEA,2BACE,oBAAkC,CAClC,0BAAwC,CAGxC,iBAAkB,CAMlB,qBAAsB,CARtB,UAAc,CAGd,cAAe,CAIf,6BAA4B,CAA5B,WAA4B,CAA5B,aAA4B,CAF5B,gBAAkB,CAJlB,gBAAiB,CAKjB,iBAAkB,CAFlB,uBAKF,CAEA,iCACE,gBAAkC,CAClC,sBAAoC,CACpC,6BACF,CAEA,oCACE,sDAAmF,CACnF,iBAAqB,CAErB,yBAA2C,CAD3C,UAEF,CAEA,4BAEE,YAAa,CADb,aAAc,CAEd,OACF,CAEA,4DAIE,WAAY,CADZ,iBAAkB,CAElB,cAAe,CAJf,QAAO,CAKP,gBAAkB,CAClB,eAAgB,CALhB,iBAAkB,CAMlB,uBACF,CAEA,8BACE,oBAAoC,CAEpC,0BAA0C,CAD1C,aAEF,CAEA,oCACE,oBAAoC,CACpC,6BACF,CAEA,8BACE,kDAAqD,CAErD,0BAAwC,CADxC,UAEF,CAEA,oCAEE,+BAA6C,CAD7C,0BAEF,CAGA,qDACE,SACF,CAEA,2DACE,oBAAkC,CAClC,iBACF,CAEA,2DACE,oBAAkC,CAClC,iBACF,CAEA,iEACE,oBACF,CCj8BA,8BASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,4BACE,sDAAmF,CACnF,0BAAyC,CACzC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAAmF,CAEnF,iCAAgD,CADhD,YAKF,CAEA,iBACE,aAAc,CACd,gBAAiB,CACjB,QAAS,CACT,8BACF,CAEA,WACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CAEd,cAAe,CADf,gBAAiB,CAEjB,gBAAiB,CAEjB,uBACF,CAEA,iBACE,oBAAkC,CAClC,oBACF,CAGA,eAGE,YACF,CAGA,iBACE,oBAAmC,CACnC,0BAAyC,CACzC,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CAFf,QAAS,CACT,kBAEF,CAEA,wBACE,eACF,CAEA,cACE,aAAc,CAEd,cACF,CAEA,cAEE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAHnB,aAAc,CAEd,gBAGF,CAEA,oBACE,oBAAiC,CACjC,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAId,cAAe,CADf,eAAiB,CAFjB,gBAIF,CAEA,0BAEE,oBAAqB,CACrB,6BAA4C,CAF5C,YAGF,CAEA,yBAEE,kBAAmB,CAEnB,aAAc,CACd,cAAe,CAJf,YAAa,CAEb,OAAQ,CAGR,gBACF,CAEA,8CAGE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAGA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,eAIE,oBAAiC,CAHjC,0BAAyC,CACzC,kBAAmB,CACnB,eAEF,CAEA,cACE,qDAAqF,CAKrF,iCAAgD,CAFhD,6BAA8B,CAF9B,iBAKF,CAEA,8BAJE,kBAAmB,CAFnB,YAYF,CANA,gBAGE,cAAe,CACf,gBAAiB,CACjB,eACF,CAEA,qCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,aACE,aAAc,CACd,6BACF,CAEA,eACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CACf,eAAiB,CAHjB,gBAAiB,CAIjB,uBACF,CAEA,kBACE,sDAAoF,CAEpF,+BAA8C,CAD9C,0BAEF,CAGA,kBACE,gBAAiB,CACjB,eACF,CAEA,gBAEE,iCAAgD,CADhD,iBAAkB,CAElB,uBACF,CAEA,sBACE,oBACF,CAEA,qBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,UACF,CAEA,0CAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,UACF,CAEA,mBACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAEA,wBAOE,qBAAsB,CAJtB,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAIF,CAGA,cAME,kBAAmB,CALnB,qDAAgF,CAEhF,8BAA6C,CAI7C,cAAe,CAFf,6BAA8B,CAH9B,YAOF,CAEA,YAGE,aAAc,CAFd,YAAa,CAIb,cAAe,CADf,eAAgB,CAFhB,QAIF,CAEA,iBACE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAGnB,eAAiB,CAJjB,gBAAiB,CAGjB,6BAEF,CAEA,kBACE,aACF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAGb,eAAgB,CADhB,QAEF,CAEA,yBACE,oBAAiC,CACjC,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAId,cAAe,CADf,eAAiB,CAFjB,gBAIF,CAEA,+BAEE,oBAAqB,CACrB,6BAA4C,CAF5C,YAGF,CAOA,0BAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CARnB,iBAAkB,CAOlB,wBAAyB,CADzB,uBAGF,CAEA,YACE,sDAAmF,CAEnF,0BAAwC,CADxC,aAEF,CAEA,kBACE,sDAAmF,CAEnF,+BAA6C,CAD7C,0BAEF,CAEA,cACE,kDAAqD,CAErD,sBAA6B,CAD7B,UAEF,CAEA,mCAEE,+BAA8C,CAD9C,0BAEF,CAEA,uBACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAGA,uEAEE,SACF,CAEA,mFAEE,oBAAiC,CACjC,iBACF,CAEA,mFAEE,kDAAqD,CACrD,iBACF,CAEA,+FAEE,kDACF,CAGA,yBACE,4BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,cAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YAEE,cAAe,CADf,sBAEF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CC5aA,aAEE,8DAA0E,CAG1E,aAAc,CADd,+CAAsD,CAHtD,gBAAiB,CAEjB,YAGF,CAGA,aAGE,kBAAmB,CAGnB,UAAW,CALX,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAAgB,CAEhB,2BACF,CAEA,iBAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAI7B,6BAA4C,CAN5C,WAAY,CAKZ,kBAAmB,CANnB,UAQF,CAQA,YAME,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,0BAAyC,CAFzC,kBAAmB,CAGnB,6BAA4C,CAR5C,6BAA8B,CAE9B,kBAAmB,CAInB,iBAAkB,CAGlB,iBACF,CAEA,yBAXE,kBAAmB,CAFnB,YAiBF,CAJA,aAGE,QACF,CAEA,aAGE,kDAAqD,CADrD,WAAY,CAGZ,iBAAkB,CADlB,UAAW,CAEX,cAAe,CAIf,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CAUlB,2BAAsC,CAFtC,uBAGF,CAEA,mBAGE,kDAAqD,CADrD,+BAA8C,CAD9C,0BAGF,CAEA,yBACE,UAAW,CAKX,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAKhB,QAAS,CAJT,QAAS,CAGT,iBAAkB,CAGlB,iBAAkB,CALlB,8BAA6C,CAI7C,0BAEF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,aACE,aAAc,CACd,cACF,CAEA,kBAEE,kBAAmB,CAEnB,cAAe,CADf,eAAgB,CAFhB,gBAAiB,CAIjB,wBACF,CAEA,0BAEE,oBAAkC,CAOlC,wBAAyB,CAHzB,iBAAkB,CAIlB,4BAA0C,CAT1C,aAAc,CAId,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,eAAgB,CADhB,gBAIF,CAEA,0BACE,oBAAmC,CAEnC,wBAAyB,CAEzB,4BAA2C,CAH3C,aAAc,CAEd,2BAEF,CAEA,gBAIE,kDAAqD,CADrD,0BAAyC,CAGzC,kBAAmB,CAOnB,+BAA8C,CAR9C,UAAW,CAEX,cAAe,CAIf,eAAgB,CAHhB,uBASF,CAEA,iCAJE,kBAAmB,CALnB,iCAA0B,CAA1B,yBAA0B,CAI1B,YAAa,CALb,cAAe,CAPf,WAAY,CAcZ,sBAAuB,CAfvB,iBAAkB,CAWlB,+BAuBF,CAhBA,iBAGE,0BAAyC,CAGzC,kBAUF,CAEA,sBACE,kDAAqD,CACrD,oBAAqB,CAErB,6BAA4C,CAD5C,0BAEF,CAEA,uBACE,kDAAqD,CACrD,oBAAqB,CAErB,6BAA4C,CAD5C,0BAEF,CAGA,kBAME,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,4BAA2C,CAF3C,kBAAmB,CAGnB,6BAA6C,CAN7C,kBAAmB,CAQnB,gBAAiB,CACjB,iBAAkB,CAClB,eAAgB,CANhB,SAAU,CAGV,yBAAkB,CAAlB,iBAIF,CAEA,8BAdE,kBAAmB,CAFnB,YAAa,CACb,sBAgCF,CAjBA,YAIE,kDAA6D,CAD7D,WAAY,CAGZ,kBAAmB,CAMnB,8BAA8C,CAP9C,UAAW,CAEX,cAAe,CAMf,wCAA+C,CAJ/C,cAAe,CACf,eAAgB,CARhB,WAAY,CASZ,YAAa,CAVb,cAAe,CAOf,uBASF,CAEA,qBAGE,kDAA6D,CAE7D,WAAY,CADZ,UAAW,CAFX,kBAAmB,CADnB,UAKF,CAEA,iCACE,kDAA6D,CAE7D,+BAA8C,CAD9C,sCAEF,CAEA,gBAaE,kBAAmB,CAHnB,gBAAuB,CACvB,eAAgB,CAVhB,UAAW,CAWX,YAAa,CAPb,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAYhB,WAAY,CADZ,sBAAuB,CAPvB,oBAAqB,CADrB,aAAc,CAEd,eAAgB,CAChB,iBAAkB,CALlB,6BAYF,CAGA,aAME,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAAiC,CAMjC,0BAAyC,CALzC,kBAAmB,CAEnB,4BAA0C,CAC1C,kBAAmB,CAFnB,YAKF,CAGA,6BAIE,gBAAuB,CADvB,0BAAyC,CADzC,kBAAmB,CAGnB,6BAA4C,CAJ5C,eAKF,CAGA,gBAEE,wBAAyB,CAGzB,aAAc,CAFd,cAAe,CACf,eAAgB,CAHhB,UAKF,CAEA,sBACE,yDAA6D,CAC7D,UACF,CAEA,mBAKE,gCAAgD,CAGhD,UAAW,CAJX,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAClB,iBAAkB,CAOlB,+BAAsC,CAFtC,wBAAyB,CADzB,kBAIF,CAEA,8BACE,iBACF,CAGA,cAEE,iCAAgD,CADhD,uBAEF,CAEA,4BACE,0BACF,CAEA,oBACE,0BAAyC,CAEzC,8BAAwC,CADxC,0BAEF,CAGA,YACE,oCACF,CAEA,kBACE,0BACF,CAEA,qBACE,kCAAwC,CAGxC,aAAc,CAFd,eAAgB,CAChB,qBAEF,CAGA,eAGE,kBAAmB,CADnB,gCAA+C,CAD/C,gBAAiB,CAIjB,iBAAkB,CADlB,qBAEF,CAEA,0BACE,iBACF,CAGA,YAAc,UAAa,CAC3B,eAAiD,UAAW,CAA7B,eAAgB,CAA9B,WAA6C,CAC9D,YAA4B,eAAgB,CAA9B,WAAgC,CAC9C,WAA0B,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAC5D,YAAc,WAAc,CAC5B,cAA8B,eAAgB,CAA9B,WAAgC,CAChD,WAA0B,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAC5D,gBAAkB,WAAc,CAChC,iBAAgC,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAGlE,UACE,cAAe,CACf,iBACF,CAEA,8BACE,0BACF,CAEA,cAGE,iBAAkB,CAFlB,eAAgB,CAChB,WAAY,CAEZ,uBACF,CAEA,8BACE,0BAAyC,CACzC,yBACF,CAGA,4BACE,8BAA+C,CAG/C,oCAAqD,CAFrD,yBAA0C,CAC1C,4BAA8B,CAE9B,uBACF,CAEA,kCACE,8BAAgD,CAChD,oCAAqD,CACrD,wBACF,CAGA,gCACE,8BAA+C,CAC/C,qBACF,CAGA,aASE,kBAAmB,CANnB,wBAAyB,CACzB,iBAAkB,CAOlB,6BAA4C,CAD5C,aAAc,CAJd,cAAe,CAJf,eAAgB,CAMhB,YAAa,CAHb,WAAY,CAEZ,eAAgB,CANhB,UAWF,CAEA,mBACE,oBACF,CAGA,SAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,WACE,cAAe,CACf,aAAc,CACd,6BACF,CAGA,YAGE,kBAAmB,CAEnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,0BAA0C,CAF1C,kBAAmB,CALnB,YAAa,CACb,wBAAyB,CAKzB,iBAEF,CAEA,YACE,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,6BACF,CAGA,0BACE,gBACE,cACF,CAEA,yCAGE,eACF,CACF,CAEA,yBACE,aACE,YACF,CAEA,YAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAAS,CAET,YACF,CAEA,2BAGE,kBAAmB,CADnB,qBAAsB,CAEtB,QACF,CAEA,kBAKE,kBAAmB,CAJnB,qBAAsB,CACtB,QAAS,CACT,WAAY,CAGZ,aAAc,CAFd,UAGF,CACA,4BAEE,cAAe,CADf,WAAY,CAGZ,YAAa,CADb,cAEF,CACA,gBACE,cAAe,CAEf,YAAa,CADb,cAEF,CAEA,aACE,YACF,CAEA,gBACE,cACF,CAEA,eACE,eACF,CACF,CAcA,uDACE,6BACF,CAGA,gDACE,UAAW,CACX,SACF,CAEA,sDACE,oBAAiC,CACjC,iBACF,CAEA,sDACE,kBAAmB,CAEnB,wBAAyB,CADzB,iBAEF,CAEA,4DACE,kBACF,CAGA,eAGE,kBAAmB,CAKnB,iCAA0B,CAA1B,yBAA0B,CAJ1B,oBAAiC,CAKjC,0BAAyC,CAJzC,kBAAmB,CAKnB,+BAAyC,CATzC,YAAa,CACb,6BAA8B,CAK9B,kBAAmB,CADnB,iBAKF,CAQA,6BAJE,kBAAmB,CADnB,YAAa,CAEb,QAOF,CAGA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,cACE,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,cAAe,CADf,6BAEF,CAEA,4BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,yBAYE,kCAA2B,CAA3B,0BAA2B,CAX3B,sDAAiF,CACjF,0BAAyC,CACzC,iBAAkB,CAUlB,+BAA8C,CAT9C,aAAc,CAId,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CANhB,gBAAiB,CAIjB,uBAKF,CAEA,+BACE,sBAAqC,CACrC,+BAA8C,CAC9C,0BACF,CAEA,+BAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,gCACE,kBAAmB,CACnB,aAAc,CACd,WACF,CAKA,uBAEE,kBAAmB,CAMnB,iCAA0B,CAA1B,yBAA0B,CAH1B,oBAAkC,CAElC,0BAAwC,CADxC,kBAAmB,CALnB,YAAa,CAEb,OAAQ,CACR,gBAKF,CAEA,aACE,aAAc,CACd,cAAe,CACf,eACF,CAEA,aACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,6BACF,CAGA,gBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CACT,WAAY,CAFZ,wBAGF,CAKA,yBACE,eAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,6BAGE,kBAAmB,CADnB,qBAAsB,CAEtB,QACF,CAEA,gBAGE,QAAS,CAFT,sBAAuB,CACvB,UAEF,CAEA,wBAEE,eAAgB,CADhB,SAEF,CAEA,8CAGE,iBACF,CACF,CC7rBA,6BAUE,kBAAmB,CAEnB,6BAA+B,CAL/B,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAA8B,CAD9B,QAAS,CAGT,YAAa,CACb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CASN,YAEF,CAEA,qBAWE,8BAAgC,CAVhC,kDAAqD,CAWrD,0BAAyC,CARzC,kBAAmB,CAMnB,gDAA8E,CAR9E,aAAc,CAMd,YAAa,CACb,qBAAsB,CANtB,+BAAmC,CAInC,eAAgB,CADhB,eAAgB,CADhB,SAQF,CAGA,cACE,iDAAoD,CAGpD,2BAA4B,CAF5B,UAAW,CACX,iBAKF,CAEA,iBAIE,+BAAmC,CAFnC,cAAe,CACf,eAAgB,CAEhB,8BACF,CAEA,cAGE,UAAW,CACX,cAAe,CAIf,WAAY,CAFZ,SAAU,CACV,UAOF,CAEA,oBACE,gBAAoC,CACpC,uBACF,CAGA,eAGE,QAAO,CADP,eAAgB,CADhB,YAGF,CAGA,oBAGE,sDAAqF,CAErF,0BAAyC,CADzC,kBAAmB,CAHnB,kBAAmB,CACnB,YAIF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,WAGE,oBAA8B,CAE9B,0BAAyC,CADzC,iBAAkB,CAFlB,YAAa,CADb,iBAKF,CAEA,YAGE,aAAc,CAFd,aAAc,CACd,cAAe,CAEf,iBACF,CAEA,YAIE,aAAc,CAHd,aAAc,CAId,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAGhB,8BACF,CAEA,qBACE,aAAc,CACd,8BACF,CAGA,mBACE,kBACF,CASA,sBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAAkB,CAIlB,6BACF,CAEA,mBAEE,kDAAqD,CAErD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAKX,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CANhB,gBAAiB,CAQjB,uBAEF,CAEA,yBAEE,+BAA8C,CAD9C,0BAEF,CAEA,4BACE,kDACF,CAGA,iBAKE,gBAA8B,CAF9B,0BAAyC,CACzC,iBAAkB,CAHlB,gBAAiB,CACjB,eAIF,CAEA,eAEE,sBAAuB,CAGvB,iCAAgD,CADhD,cAAe,CAHf,YAAa,CAEb,YAAa,CAGb,uBACF,CAEA,0BACE,kBACF,CAEA,qBACE,oBACF,CAEA,oCAIE,oBAAqB,CAHrB,iBAAkB,CAClB,cAAe,CACf,oBAEF,CAEA,gCACE,QACF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,iBACF,CAEA,gBAGE,UAAW,CACX,QAAO,CAHP,cAAe,CACf,eAGF,CAEA,iBAIE,oBAAmC,CAEnC,iBAAkB,CAJlB,aAAc,CADd,cAAe,CAEf,eAAgB,CAEhB,eAEF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,oCAGE,aAAc,CADd,cAEF,CAEA,kBACE,aACF,CAQA,YAEE,kBAAmB,CAGnB,0BAAyC,CACzC,iBAAkB,CAHlB,cAAe,CAFf,YAAa,CAGb,iBAAkB,CAGlB,uBACF,CAEA,kBACE,oBAAmC,CACnC,sBACF,CAEA,8BAEE,oBAAqB,CADrB,gBAEF,CAGA,cACE,eACF,CAEA,UACE,oBAAoC,CACpC,0BAAyC,CACzC,iBAAkB,CAClB,YACF,CAEA,aAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,aAGE,aAAc,CAFd,QAAS,CACT,iBAEF,CAEA,aAEE,cAAe,CADf,iBAEF,CAGA,cAME,gBAA8B,CAC9B,2BAA4B,CAL5B,8BAA6C,CAD7C,iBAOF,CAEA,gCAGE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAJhB,iBAAkB,CAMlB,uBAEF,CAEA,eACE,oBAAoC,CAEpC,0BAA0C,CAD1C,aAEF,CAEA,qBACE,oBACF,CAEA,iBACE,kDAAqD,CACrD,UAAW,CACX,eACF,CAEA,sCAEE,+BACF,CASA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,yBACE,qBAEE,eAAgB,CADhB,SAEF,CAEA,YACE,mCACF,CAEA,gBAEE,QACF,CAEA,8BAJE,qBAMF,CACF,CC9XA,uBAQE,kBAAmB,CANnB,gFAAqF,CACrF,UAAc,CAGd,YAAa,CACb,qBAAsB,CAFtB,+BAAmC,CAJnC,gBAAiB,CAGjB,YAAa,CAKb,UACF,CAGA,oCAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,kBAAmB,CAEnB,cAAe,CADf,iBAAkB,CAElB,UACF,CAEA,uCAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,sCAEE,QAAO,CADP,iBAEF,CAEA,uCAKE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAOlB,0BAAwC,CATxC,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,gBAAiB,CADjB,eAAgB,CARhB,SAAU,CAMV,iBAAkB,CARlB,cAAe,CACf,QAAS,CAYT,uBAAyB,CAVzB,YAYF,CAEA,6CAEE,+BAA+C,CAD/C,0BAEF,CAGA,qCACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAOlB,0BAAwC,CATxC,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,gBAAiB,CADjB,eAAgB,CAFhB,iBAAkB,CAKlB,uBAEF,CAEA,2CAEE,+BAA+C,CAD/C,0BAEF,CAEA,mCAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,sCAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,UACF,CAEA,oCAEE,kBAAmB,CADnB,YAEF,CAEA,yCAEE,kBAAmB,CACnB,gBAAkB,CAClB,eAAgB,CAHhB,gBAIF,CAEA,iDACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,+CACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,iDACE,oBAAkC,CAElC,0BAAwC,CAGxC,kBAAmB,CAJnB,aAAc,CAEd,cAAe,CACf,gBAEF,CAEA,wCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA+C,CAV/C,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAMhB,QAAS,CART,iBAAkB,CAMlB,gBAAiB,CADjB,uBAIF,CAEA,8CAEE,+BAA+C,CAD/C,sCAEF,CAGA,sCAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CAOnB,6BAA8B,CAL9B,kBAAmB,CADnB,iBAAkB,CAGlB,UAMF,CAGA,4EAPE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CACf,QASF,CAEA,wCAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAAS,CAET,0BACF,CAEA,qCAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,2CACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,sCACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAFf,gBAAiB,CAGjB,WACF,CAEA,4CAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,6CACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,mDACE,oBAAkC,CAClC,UACF,CAGA,yCAEE,kBAAmB,CAGnB,oBAAkC,CAElC,0BAAwC,CADxC,kBAAmB,CALnB,YAAa,CAEb,OAAQ,CACR,gBAIF,CAEA,mCACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,mCACE,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,6BACF,CAGA,mDAQE,kBAAmB,CANnB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAMlB,cAAe,CAHf,YAAa,CAKb,gBAAiB,CANjB,WAAY,CAGZ,sBAAuB,CAIvB,gBAAiB,CAZjB,iBAAkB,CAUlB,uBAAyB,CANzB,UASF,CAEA,yDACE,oBAAkC,CAElC,+BAA6C,CAD7C,oBAEF,CAEA,oCACE,gBACF,CAEA,qCAUE,kBAAmB,CANnB,iDAAoD,CAEpD,iBAAkB,CAQlB,8BAA4C,CAT5C,UAAW,CAIX,YAAa,CAGb,eAAiB,CACjB,eAAgB,CALhB,WAAY,CAGZ,sBAAuB,CAVvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAKT,UAQF,CAGA,uCAGE,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,iBACF,CAGA,6CAEE,kBAAmB,CAGnB,oBAAmC,CAGnC,0BAAwC,CADxC,kBAAmB,CAEnB,oCAAkD,CARlD,YAAa,CAGb,QAAS,CADT,sBAAuB,CAGvB,iBAAkB,CAIlB,yBAAkB,CAAlB,iBACF,CAEA,oCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA+C,CAV/C,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAFhB,gBAAiB,CAMjB,gBAAiB,CADjB,uBAGF,CAEA,yDAGE,iDAAoD,CADpD,+BAA+C,CAD/C,sCAGF,CAEA,6CACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAEA,2CACE,aAAc,CAMd,8BAAkC,CAJlC,gBAAiB,CADjB,eAAgB,CAEhB,cAAe,CACf,iBAAkB,CAClB,6BAEF,CAGA,qCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA8C,CAV9C,UAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAMlB,gBAAiB,CADjB,uBAGF,CAEA,2CAEE,+BAA8C,CAD9C,sCAEF,CAGA,sCAcE,kCAA2B,CAA3B,0BAA2B,CAT3B,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAQnB,gCAA0C,CAZ1C,QAAS,CAST,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CALhB,SAAU,CAPV,cAAe,CACf,OAAQ,CAER,8BAAgC,CAMhC,SAAU,CADV,YAOF,CAEA,iCAGE,kBAAmB,CAGnB,qDAAkF,CADlF,iCAA+C,CAJ/C,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,oCAEE,aAAc,CACd,8BAAkC,CAClC,gBAAiB,CAHjB,QAAS,CAIT,8BACF,CAEA,6BAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CATlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CALvB,uBAAyB,CACzB,UAMF,CAEA,mCACE,oBAAkC,CAClC,oBACF,CAEA,kCACE,YACF,CAEA,4BAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,kBAAmB,CACnB,gBAAiB,CACjB,eACF,CAEA,2BAEE,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAKnB,UAAc,CAJd,cAAe,CAGf,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CADlB,uBAIF,CAEA,iCACE,oBAAkC,CAClC,oBAAqB,CAErB,+BAA6C,CAD7C,0BAEF,CAEA,oCACE,qDAAkF,CAClF,oBAAqB,CACrB,aAAc,CACd,eAAgB,CAChB,6BACF,CAEA,4BACE,YAAa,CAEb,QAAS,CADT,wBAEF,CAEA,4DAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,8BACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,oCACE,oBAAkC,CAClC,0BACF,CAEA,8BACE,iDAAoD,CAEpD,sBAA6B,CAD7B,UAEF,CAEA,oCAEE,+BAA6C,CAD7C,0BAEF,CAGA,wCASE,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAA0C,CAF1C,kBAAmB,CAInB,aAAc,CACd,8BAA+B,CAP/B,eAAgB,CAChB,eAAgB,CAIhB,UAIF,CAEA,0CAEE,wBAAyB,CACzB,eAAiB,CAFjB,UAIF,CASA,6FALE,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CACV,WAiBF,CAdA,6CACE,8DAA0E,CAI1E,0BAA0C,CAM1C,0BAAwC,CATxC,aAAc,CAUd,cAAe,CALf,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAClB,iBAAkB,CASlB,wBAAiB,CAAjB,gBACF,CAEA,wDACE,iBACF,CAEA,mDAEE,kBAAmB,CACnB,uBACF,CAEA,yDAEE,oBACF,CAEA,kCAGE,iBAAkB,CAFlB,YAAa,CAIb,iBAAkB,CADlB,qBAEF,CAEA,6CACE,iBACF,CAGA,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,eAAgB,CAA9B,WAAgC,CACtE,iCAAgD,cAAe,CAA5B,UAA8B,CACjE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAG7E,sCAQE,oBAAqB,CAGrB,gBAAuB,CACvB,sBAA6B,CAT7B,iBAAkB,CAUlB,qBAAsB,CATtB,cAAe,CAEf,aAAc,CAId,eAAgB,CAThB,eAAgB,CAChB,WAAY,CAGZ,uBAAyB,CAIzB,oBAAqB,CAFrB,UAOF,CAEA,2DAGE,gBAAuB,CADvB,2BAA2C,CAE3C,qBACF,CAEA,kFAIE,gBAAuB,CADvB,0BAA0C,CAE1C,6BACF,CAEA,+CAEE,gBAAuB,CAGvB,sBAA6B,CAF7B,kBAAmB,CACnB,UAEF,CAEA,4CAGE,0BAAkC,CAFlC,eAA+B,CAC/B,iBAEF,CAGA,kEACE,iBACF,CAEA,kDAEE,0BAAkC,CADlC,UAEF,CAOA,sGAEE,0BACF,CAGA,wCACE,0BAAkC,CAClC,qBAAuB,CACvB,yBACF,CAGA,yCACE,eAAgB,CAChB,QAAS,CACT,SACF,CAGA,oCAaE,kCAA2B,CAA3B,0BAA2B,CAT3B,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAClB,UAAc,CACd,mBAAoB,CACpB,iBAAkB,CAClB,eAAgB,CARhB,eAAgB,CAUhB,YAAa,CATb,WAAY,CAQZ,eAAgB,CAVhB,UAaF,CAEA,0CACE,oBAAqB,CACrB,6BACF,CAGA,gCAKE,oBAAqC,CACrC,kBAAmB,CAHnB,WAA+B,CAC/B,gBAAiB,CAGjB,aAAc,CALd,YAAa,CADb,iBAOF,CAGA,0CAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,gBAEF,CAWA,qCACE,aAAc,CACd,gBAAiB,CACjB,eACF,CAQA,4CAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,gBAEF,CAEA,wCAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,8CACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,8BACF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,0BAEE,0CACE,gBACF,CAEA,wDAEE,gBACF,CACF,CAEA,0BAEE,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,eAAgB,CAA9B,WAAgC,CACtE,iCAAiD,cAAe,CAA7B,WAA+B,CAClE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAE7E,sCACE,qBAAsB,CACtB,QACF,CAOA,6EAEE,sBAAuB,CADvB,UAEF,CACF,CAEA,yBACE,uBACE,YACF,CAEA,aAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,eACE,OACF,CAEA,qCAEE,qBAAsB,CADtB,OAEF,CAEA,gBAEE,sBAAuB,CADvB,OAEF,CAEA,wCAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,qCAEE,mBAAoB,CADpB,qBAAsB,CAEtB,OACF,CAEA,6CACE,qBAAsB,CACtB,QACF,CAEA,sCAEE,WAAY,CADZ,SAEF,CAEA,4BACE,yBACF,CAEA,4BACE,qBACF,CAGA,0CACE,gBACF,CAEA,wDAEE,eACF,CAGA,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,cAAe,CAA7B,WAA+B,CACrE,iCAAgD,cAAe,CAA5B,UAA8B,CACjE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAC/E,CAGA,2DACE,UAAW,CACX,SACF,CAEA,iEACE,oBAAoC,CACpC,iBACF,CAEA,iEACE,oBAAoC,CACpC,iBACF,CAEA,uEACE,oBACF,CAGA,yBACE,wCACE,8BACF,CACF,CAGA,kBAAqB,WAAc,CACnC,qBAAwB,WAAc,CACtC,sBAAyB,WAAc,CACvC,sBAAyB,WAAc,CACvC,wBAA2B,WAAc,CACzC,kBAAqB,WAAc,CACnC,kBAAqB,WAAc,CCj5BnC,wBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,gBAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAMnB,gCAA0C,CAH1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cAGE,kBAAmB,CAGnB,qDAAkF,CADlF,iCAA+C,CAJ/C,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,iBAIE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAFjB,QAAS,CAIT,8BACF,CAEA,cAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CATlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CALvB,uBAAyB,CACzB,UAMF,CAEA,oBACE,oBAAkC,CAClC,oBACF,CAGA,eAEE,UAAc,CADd,YAEF,CAGA,mBACE,kBACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,kBAEE,kBAAmB,CAGnB,aAAc,CADd,cAAe,CAHf,YAAa,CAKb,eAAgB,CAHhB,OAIF,CAEA,uCACE,oBAAqB,CACrB,oBACF,CAEA,YAME,oBAAqC,CAHrC,0BAAwC,CACxC,kBAAmB,CAHnB,gBAAiB,CACjB,eAAgB,CAGhB,YAEF,CAEA,eAEE,sBAAuB,CAIvB,iCAAiD,CADjD,cAAe,CAJf,YAAa,CAEb,QAAS,CACT,cAAe,CAGf,uBACF,CAEA,0BACE,kBACF,CAEA,qBACE,oBAAkC,CAClC,iBAAkB,CAClB,iBAAkB,CAClB,kBACF,CAEA,oCACE,oBAAqB,CAErB,cAAe,CADf,oBAEF,CAEA,WACE,YAAa,CAGb,QAAO,CAFP,qBAAsB,CACtB,OAEF,CAEA,aACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,YACE,UAAc,CAEd,eAAgB,CADhB,eAEF,CAEA,kBACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAGA,gBACE,kBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,gBACE,YAAa,CACb,QACF,CAEA,eAEE,kBAAmB,CAOnB,oBAAqC,CAHrC,0BAAwC,CACxC,iBAAkB,CAHlB,cAAe,CAHf,YAAa,CAEb,OAAQ,CAER,iBAAkB,CAGlB,uBAEF,CAEA,qBACE,oBAAkC,CAClC,oBACF,CAEA,iCACE,oBAAqB,CACrB,oBACF,CAEA,oBACE,UAAc,CACd,eACF,CAGA,qBACE,kBACF,CAEA,wBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,sBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,mBACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,oBACE,oBAAiC,CACjC,wBAAyB,CACzB,iBAAkB,CAElB,UAAc,CAGd,cAAe,CAFf,mBAAoB,CACpB,cAAe,CAEf,YAAa,CALb,iBAAkB,CAMlB,uBACF,CAEA,0BACE,oBAAqB,CACrB,6BACF,CAEA,2BACE,oBAAkC,CAClC,UACF,CAEA,eAIE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CALlB,aAAc,CACd,eAAgB,CAChB,iBAIF,CAGA,iBACE,kBACF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,iBAEE,kBAAmB,CAOnB,oBAAqC,CAHrC,0BAAwC,CACxC,iBAAkB,CAHlB,cAAe,CAHf,YAAa,CAEb,QAAS,CAET,iBAAkB,CAGlB,uBAEF,CAEA,uBACE,oBAAkC,CAClC,oBACF,CAEA,sCACE,oBAAqB,CACrB,oBACF,CAEA,sBACE,UAAc,CACd,eACF,CAGA,iBACE,kBACF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,cACE,oBAAkC,CAClC,0BAAwC,CACxC,kBAAmB,CACnB,YACF,CAEA,cAGE,kBAAmB,CAEnB,iCAAiD,CAJjD,YAAa,CACb,6BAA8B,CAE9B,aAEF,CAEA,yBACE,kBACF,CAEA,eACE,aAAc,CACd,eACF,CAEA,eACE,UAAc,CACd,eACF,CAGA,cAME,oBAAqC,CADrC,8BAA4C,CAJ5C,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,iBAGF,CAEA,eACE,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAId,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAMlB,uBACF,CAEA,qBACE,oBAAkC,CAClC,0BACF,CAEA,iBACE,iDAAoD,CACpD,WAAY,CACZ,iBAAkB,CAElB,UAAc,CAId,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAMlB,uBACF,CAEA,sCAEE,+BAA6C,CAD7C,0BAEF,CAEA,0BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,yBACE,gBAEE,WAAY,CADZ,SAEF,CAMA,6BACE,YACF,CAEA,cACE,YAEF,CAMA,oDACE,qBACF,CACF,CC9aA,oBAEE,gFAAqF,CACrF,UAAc,CACd,+BAAmC,CAHnC,gBAAiB,CAKjB,eAAgB,CADhB,iBAEF,CAGA,+BAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,iDAME,+CAAgD,CAFhD,eAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAFX,iBAAkB,CAClB,SAKF,CAEA,6DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,8DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,8DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,mDAUE,8CAA+C,CAJ/C,oGAEqE,CACrE,yBAA0B,CAJ1B,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAOF,CAGA,2BAEE,kBAAmB,CAKnB,kCAA2B,CAA3B,0BAA2B,CAF3B,gBAA8B,CAC9B,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAAkB,CAIlB,iBAAkB,CAClB,UACF,CAEA,qBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,QAEF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,sBAEF,CAEA,sBAIE,QAAO,CADP,wBAEF,CAEA,qBAEE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBAEF,CAEA,2BAEE,+BAA6C,CAD7C,0BAEF,CAEA,sBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,QAAS,CADT,8BAEF,CAQA,6CAJE,kBAAmB,CADnB,YAsBF,CAjBA,uBAKE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,eAAiB,CACjB,eAAgB,CARhB,OAAQ,CAaR,sBAAuB,CADvB,eAAgB,CAXhB,iBAAkB,CASlB,uBAIF,CAEA,6BAEE,+BAA6C,CAD7C,0BAEF,CAGA,+BACE,8CAAoD,CACpD,+BACF,CAEA,qCACE,+BACF,CAEA,6BACE,8CAAoD,CACpD,+BACF,CAEA,mCACE,+BACF,CAEA,+BACE,8CAAoD,CACpD,+BACF,CAEA,qCACE,+BACF,CAGA,qBAEE,kBAAmB,CAEnB,UAAc,CAHd,YAAa,CAIb,eAAiB,CACjB,eAAgB,CAHhB,OAIF,CAEA,0BAIE,+CAAgD,CADhD,iBAAkB,CADlB,UAAW,CADX,SAIF,CAYA,oGACE,eAAgB,CAChB,yBACF,CAGA,wBAEE,kBAAmB,CAInB,gBAA8B,CAC9B,iCAA+C,CAN/C,YAAa,CAGb,QAAS,CADT,sBAAuB,CAEvB,YAAa,CAGb,iBAAkB,CAClB,UACF,CAEA,8BAEE,UAAc,CADd,gBAAiB,CAEjB,eACF,CAEA,wBAEE,gBAA8B,CAC9B,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAGd,cAAe,CAFf,+BAAmC,CACnC,cAAe,CAGf,eAAgB,CAThB,iBAAkB,CAQlB,uBAEF,CAEA,8BAEE,iBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,+BACE,kBAAmB,CACnB,UACF,CAKA,wBAKE,oBAA8B,CAC9B,kBAAmB,CAInB,+BAAyC,CADzC,QAAS,CAPT,6BAA8B,CAE9B,kBAAmB,CAGnB,YAIF,CAGA,gDAXE,kBAAmB,CAKnB,0BAAwC,CAPxC,YA+BF,CAlBA,wBAKE,gBAAuB,CAEvB,iBAAkB,CAClB,eAA+B,CAI/B,cAAe,CAHf,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CARhB,QAAS,CAcT,sBAAuB,CADvB,eAAgB,CADhB,eAAgB,CAXhB,iBAAkB,CAUlB,iBAAkB,CADlB,uBAKF,CAEA,+BAOE,uDAAoF,CANpF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,qCACE,SACF,CAEA,+BACE,qDAAkF,CAGlF,sBAAoC,CADpC,+BAA6C,CAD7C,UAGF,CAEA,8BAGE,sBAAoC,CAFpC,UAAc,CACd,0BAEF,CAEA,sBAEE,8CAAmD,CADnD,gBAEF,CAEA,uBACE,eAAgB,CAChB,8BAA4C,CAC5C,kBACF,CAGA,wBAEE,kBAAmB,CAEnB,gBAAuB,CAGvB,0BAAwC,CAFxC,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CAGT,iBAAkB,CAElB,uBACF,CAEA,+CAYE,kBAAmB,CATnB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAGd,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAaF,CAEA,qDACE,oBAAkC,CAClC,sBAAoC,CACpC,oBACF,CAEA,+CAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAFlB,8BAGF,CAOA,2BAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,iBAEF,CAEA,yBAME,yCAA0C,CAF1C,0BAA6B,CAC7B,iBAAkB,CADlB,qBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,sBACE,UAAc,CACd,gBAAiB,CACjB,eACF,CAGA,yBACE,MAEE,UAAY,CADZ,oCAEF,CACA,IAEE,SAAU,CADV,0CAEF,CACF,CAEA,4BACE,GACE,sBACF,CACA,GACE,8BACF,CACF,CAEA,yBACE,MACE,SAAU,CACV,kBACF,CACA,IACE,UAAY,CACZ,oBACF,CACF,CAEA,wBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAKA,4BACE,WACF,CAEA,0CACE,oBACF,CAEA,kCACE,oBACF,CAGA,+CAEE,oBAAmC,CACnC,eACF,CAGA,uBAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,0BAA4B,CAD5B,oBAEF,CAEA,oBAEE,eACF,CAEA,6BACE,cAAe,CACf,oCACF,CAEA,mCACE,oBACF,CAEA,6BACE,oBAAoC,CACpC,WACF,CAGA,4CAEE,UAAW,CADX,SAEF,CAEA,kDACE,oBAA8B,CAC9B,iBACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,wDACE,oBACF,CAGA,0BACE,sBACE,cACF,CAEA,wBACE,qBAAsB,CACtB,QAAS,CACT,YACF,CAEA,wBACE,eAAgB,CAChB,iBACF,CACF,CAEA,yBACE,2BACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,qBACE,qBAAsB,CACtB,QACF,CAEA,sBACE,gBAAiB,CACjB,iBACF,CAEA,wBACE,qBAAsB,CACtB,QACF,CAEA,wBACE,eACF,CAEA,4BACE,iBACF,CACF,CC3iBA,wBAEE,kBAAmB,CAGnB,gBAA8B,CAC9B,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAGA,0BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,uBAYE,kBAAmB,CATnB,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CASlB,+BAA6C,CAR7C,UAAW,CAGX,cAAe,CAEf,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAcF,CAEA,6BAEE,+BAA6C,CAD7C,0BAEF,CAEA,uBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAFlB,8BAGF,CAiBA,0BAEE,+BAA6C,CAD7C,0BAEF,CAKA,mBAIE,gBAA8B,CAS9B,qBAAuB,CAXvB,uBAAyB,CAMzB,kBAAmB,CALnB,gBAAiB,CAajB,yCAAoD,CAXpD,+BAAmC,CACnC,gBAAkB,CAIlB,eAAgB,CAKhB,sBAAwB,CANxB,eAAgB,CAEhB,eAAgB,CAJhB,iBAAkB,CANlB,UAgBF,CAGA,yBAEE,qDACF,CAEA,sBAgBE,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAA0C,CAO1C,2BAA4B,CAZ5B,WAAkB,CAAlB,iCAAkB,CAUlB,8BAAwC,CAfxC,UAAc,CADd,eAAgB,CAehB,eAAgB,CAlBhB,iBAAkB,CAWlB,iCAA2B,CAA3B,yBAA2B,CAV3B,iBAAkB,CAelB,6BAA2C,CAJ3C,eAAiB,CAVjB,qBAAsB,CAetB,kBAAmB,CAJnB,sBASF,CAEA,sBACE,+DAA+F,CAC/F,eAAiB,CACjB,eACF,CAGA,4BACE,iCAA+C,CAC/C,uBACF,CAGA,uCACE,4BACF,CAEA,kCACE,oBAAkC,CAClC,sBACF,CAEA,0CACE,oBACF,CAEA,gDACE,oBACF,CAGA,sBAQE,oBAAqB,CAHrB,WAAY,CAOZ,WAAY,CAFZ,eAAgB,CAChB,eAAgB,CAEhB,eAAgB,CANhB,eAAgB,CANhB,gBAAiB,CAKjB,iBAAkB,CAJlB,iBAAkB,CAClB,qBAAsB,CAMtB,oBAKF,CAGA,uBACE,oBAAkC,CAElC,UAAc,CAQd,eAAiB,CATjB,eAAgB,CAIhB,cAAe,CADf,cAAe,CAKf,gBAAiB,CAFjB,iBAAkB,CAClB,qBAAsB,CAFtB,UAKF,CAEA,wBACE,oBAAmC,CAGnC,eAAgB,CAGhB,eAKF,CAEA,6CANE,oBAAqB,CAErB,eAAgB,CAJhB,eAAgB,CAHhB,iBAAkB,CADlB,eAAgB,CAShB,qBAAsB,CAFtB,oBAeF,CAVA,qBAKE,eAKF,CAEA,qBAEE,UAAc,CADd,eAAgB,CAIhB,cAAe,CADf,cAAe,CAKf,gBAAiB,CAFjB,iBAAkB,CAClB,qBAAsB,CAFtB,UAIF,CAEA,uBAIE,oBAAqB,CAErB,eAAgB,CAHhB,eAAgB,CADhB,eAAgB,CAMhB,iBAAkB,CADlB,qBAAsB,CAFtB,oBAIF,CAEA,wBAGE,eAAgB,CADhB,eAAgB,CAKhB,gBAAiB,CAFjB,iBAAkB,CAClB,qBAAsB,CAFtB,WAIF,CAGA,oBASE,oBAAqB,CAJrB,cAAe,CAMf,eAAgB,CARhB,eAAgB,CAShB,eAAgB,CAVhB,eAAgB,CAMhB,iBAAkB,CAJlB,iBAAkB,CASlB,iBAAkB,CAPlB,uBAAyB,CACzB,qBAAsB,CAGtB,oBAIF,CAGA,oBAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,0BAA4B,CAC5B,2BAA6B,CAF7B,oBAGF,CAGA,6BACE,oBAAmC,CACnC,+BACF,CAEA,mCACE,oBAAmC,CACnC,mCAAiD,CACjD,qBACF,CAGA,6BACE,oBAAqC,CAGrC,+BAA+C,CAF/C,WAA+B,CAC/B,kBAEF,CAEA,mCACE,oBACF,CAGA,4BACE,0BACF,CAEA,8BACE,8BACF,CAGA,4BAME,oBAA8B,CAC9B,iBAAkB,CAHlB,eAAiB,CAKjB,WAAY,CAJZ,UAAY,CAJZ,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAMR,UAAW,CAKX,SACF,CAGA,kDANE,kBAAmB,CADnB,YAAa,CAEb,sBAmBF,CAdA,sBASE,eAAgC,CAIhC,qBAAsB,CALtB,eAAiB,CALjB,eAAgB,CADhB,eAAgB,CAIhB,wBAAyB,CACzB,iBAAkB,CAFlB,oBAAqB,CAJrB,UAAW,CAGX,qBAUF,CAGA,mBAEE,aAAc,CADd,iBAEF,CAEA,8BACE,eACF,CAEA,yBACE,YACF,CAGA,2BACE,gBAAiB,CACjB,eAAgB,CAGhB,iBAAkB,CADlB,+BAAmD,CADnD,oBAGF,CAEA,8CACE,SACF,CAEA,oDACE,gBAA8B,CAC9B,iBACF,CAEA,oDACE,oBAAkC,CAClC,iBACF,CAEA,0DACE,oBACF,CAGA,sCAEE,eAA6B,CAD7B,aAAc,CAEd,eACF,CAGA,mCAEE,eAA+B,CAD/B,WAAY,CAEZ,iBACF,CAGA,oBAGE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CACd,+BAAmC,CACnC,eAAiB,CANjB,eAAgB,CAQhB,YAAa,CACb,WAAY,CAFZ,iBAAkB,CAGlB,uBAAyB,CAXzB,UAYF,CAEA,0BACE,oBAAkC,CAClC,iBAAqB,CACrB,6BAA2C,CAC3C,qBACF,CAEA,uBAgBE,oBAAqB,CAZrB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CACd,+BAAmC,CACnC,eAAiB,CAIjB,eAAgB,CAVhB,gBAAiB,CADjB,eAAgB,CAQhB,YAAa,CACb,WAAY,CACZ,eAAgB,CAEhB,uBAAyB,CACzB,oBAAqB,CAdrB,UAgBF,CAEA,6BACE,oBAAkC,CAClC,iBAAqB,CACrB,6BAA2C,CAC3C,qBACF,CAGA,0CACE,SACF,CAEA,gDACE,gBAA8B,CAC9B,iBACF,CAEA,gDACE,oBAAkC,CAClC,iBACF,CAEA,sDACE,oBACF,CAGA,sBACE,gBAA8B,CAC9B,0BAAwC,CACxC,iBAAkB,CAElB,QAAS,CADT,SAEF,CAEA,yBAGE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CACf,QAAS,CAFT,6BAA8B,CAH9B,iBAMF,CAEA,8CAGE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,4CAEE,UAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,yFAKE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAJhB,gBAAiB,CAMjB,uBACF,CAEA,mBACE,iDAAoD,CACpD,UACF,CAEA,yBACE,iDAAoD,CACpD,0BACF,CAEA,6CAEE,iDAAoD,CACpD,UACF,CAEA,yDAEE,iDAAoD,CACpD,0BACF,CAEA,yBACE,iDAAoD,CACpD,UACF,CAEA,+BACE,iDAAoD,CACpD,0BACF,CAGA,uBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,qBACE,8DAA0E,CAC1E,0BAAwC,CACxC,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAKF,CAEA,sBACE,oBAAkC,CAClC,iBAKF,CAEA,yBAGE,+BAEF,CAEA,qBACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAc,CAEd,cAAe,CADf,gBAAiB,CAEjB,WAAY,CAEZ,uBACF,CAEA,2BACE,oBAAkC,CAClC,oBACF,CAEA,uBAEE,gBAAiB,CADjB,YAGF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,oBACE,oBAAgC,CAChC,0BAAsC,CACtC,iBAAkB,CAClB,YACF,CAEA,qBACE,UAAc,CAEd,eAAiB,CADjB,eAAgB,CAEhB,iBACF,CAEA,uBACE,aAAc,CACd,eAAgB,CAChB,iBACF,CAEA,oBACE,WAA+B,CAC/B,eAAiB,CACjB,iBACF,CAEA,qBACE,WAA+B,CAC/B,eAAiB,CACjB,iBACF,CAEA,sBAKE,QAAS,CAHT,iBAKF,CAEA,8CAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAKlB,uBACF,CAEA,uBACE,iDAAoD,CACpD,UACF,CAEA,6BACE,iDAAoD,CACpD,0BACF,CAEA,uBACE,iDAAoD,CACpD,UACF,CAEA,6BACE,iDAAoD,CACpD,0BACF,CAGA,qBACE,oBAAkC,CAClC,0BACF,CAEA,2BAOE,uDAAoF,CADpF,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAGA,0BACE,wBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,0BACE,sBACF,CAEA,oBACE,iBACF,CAEA,mBACE,gBACF,CAEA,4CAEE,eACF,CAGA,uBAKE,eACF,CAGA,4CAPE,cAAe,CADf,cAAe,CAGf,eAAgB,CADhB,UAWF,CAGA,oBAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,yBAA2B,CAD3B,oBAEF,CAEA,sBAEE,eAAiB,CADjB,eAEF,CAEA,oBACE,eACF,CACF,CAEA,yBACE,wBACE,iBACF,CAEA,uBACE,cAAe,CACf,eACF,CAEA,uBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,oBAEE,eAAiB,CADjB,iBAEF,CAEA,mBACE,eACF,CAEA,4CAEE,eACF,CAGA,uBAKE,gBACF,CAGA,4CAPE,cAAe,CADf,cAAe,CAGf,eAAgB,CADhB,UAWF,CAGA,oBAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,yBAA2B,CAD3B,oBAEF,CAEA,6CAEE,gBACF,CAEA,sBAEE,gBAAkB,CADlB,cAEF,CAEA,oBACE,cACF,CAEA,0BACE,eAAiB,CACjB,WACF,CACF,CC5zBA,8BAQE,kBAAmB,CAGnB,kCAA2B,CAA3B,0BAA2B,CAL3B,gBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YAEF,CAEA,sBASE,2CAA6C,CAR7C,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,sBAEE,kBAAmB,CAGnB,qDAAkF,CAClF,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAEA,yBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,QAAS,CACT,8BACF,CAEA,mBAYE,kBAAmB,CATnB,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CASlB,+BAA6C,CAR7C,UAAW,CAGX,cAAe,CAEf,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAcF,CAEA,yBAEE,+BAA6C,CAD7C,oBAEF,CAGA,uBAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAGA,2BACE,kBACF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAC9B,kBAGF,CAEA,2BAIE,UAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAEhB,QAAS,CACT,6BACF,CAEA,4BAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAEA,0BAEE,kBAAmB,CAEnB,UAAc,CAEd,cAAe,CALf,YAAa,CAIb,eAAiB,CAFjB,OAAQ,CAIR,yBACF,CAEA,gCACE,UACF,CAEA,+CAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,yBAKE,oBAAkC,CAElC,0BAAwC,CADxC,iBAAkB,CALlB,UAAc,CACd,eAAiB,CACjB,eAAgB,CAChB,eAIF,CAGA,yBAKE,oBAA8B,CAF9B,0BAAwC,CACxC,iBAAkB,CAHlB,gBAAiB,CACjB,eAAgB,CAIhB,YACF,CAEA,4CACE,SACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,wDACE,oBACF,CAEA,wBAEE,kBAAmB,CAOnB,iBAAkB,CAJlB,UAAc,CAEd,cAAe,CANf,YAAa,CAKb,eAAiB,CAHjB,QAAS,CAOT,iBAAkB,CANlB,gBAAiB,CAIjB,uBAGF,CAEA,8BACE,oBAAkC,CAClC,yBACF,CAEA,mCACE,eACF,CAEA,6CAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,wBACE,QAAO,CACP,eACF,CAGA,wBACE,kBACF,CAEA,2BAIE,UAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAEhB,eAAkB,CAClB,6BACF,CAEA,wBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,uBAEE,kBAAmB,CAGnB,oBAA8B,CAC9B,0BAAwC,CACxC,kBAAmB,CACnB,UAAc,CAEd,cAAe,CATf,YAAa,CAWb,QAAO,CAHP,cAAe,CANf,QAAS,CAUT,eAAgB,CAThB,iBAAkB,CAOlB,uBAGF,CAEA,6BAEE,oBAAkC,CADlC,iBAAqB,CAGrB,+BAA6C,CAD7C,0BAEF,CAEA,yCAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,qBAEE,8CAAmD,CADnD,gBAEF,CAEA,qBAEE,QAAO,CADP,eAEF,CAGA,sBAEE,kBAAmB,CAInB,oBAA8B,CAC9B,8BAA4C,CAN5C,YAAa,CAGb,QAAS,CADT,wBAAyB,CAEzB,iBAGF,CAEA,oBAEE,gBAAuB,CACvB,0BAA0C,CAC1C,iBAAkB,CAClB,UAAc,CAId,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBACF,CAEA,0BAEE,oBAAoC,CADpC,iBAEF,CAEA,6BAEE,kBAAmB,CADnB,UAEF,CAEA,oBAEE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBAEF,CAEA,yCAEE,+BAA6C,CAD7C,0BAEF,CAEA,6BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,gCACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,yBACE,sBAEE,eAAgB,CADhB,SAEF,CAEA,sBACE,iBACF,CAEA,yBACE,gBACF,CAEA,uBACE,YACF,CAEA,wBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,4BAEE,6BAA8B,CAD9B,UAEF,CAEA,wBACE,qBACF,CAEA,uBACE,cACF,CAEA,sBAEE,qBAAsB,CACtB,QAAS,CAFT,iBAGF,CAEA,wCAGE,iBAAkB,CADlB,UAEF,CACF", "sources": ["styles/index.css", "styles/App.css", "模块一/styles/HomePage.css", "components/FileSelector.css", "模块一/styles/WorkTarget.css", "模块一/components/SelectiveDownloadModal.css", "styles/WorkTracking.css", "模块二/components/WorkTrackingDownloadModal.css", "模块三/styles/WorldClass.css", "模块三/components/WorldClassDownloadModal.css", "模块四/styles/MonthlyKPI.css", "模块四/components/KPISelectiveDownloadModal.css", "模块五/styles/ProjectOne.css", "模块五/styles/ProjectOneDownloadModal.css", "模块六/styles/ModuleSix.css", "模块六/styles/KPITable.css", "模块六/styles/ExportModal.css"], "sourcesContent": ["/* 全局重置 */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  min-height: 100vh;\r\n  overflow-x: hidden;\r\n}\r\n\r\ncode {\r\n  font-family: 'Orbitron', 'Courier New', monospace;\r\n}\r\n\r\n/* 滚动条样式 */\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n  border-radius: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n}\r\n\r\n/* 通用动画 */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(20px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n@keyframes slideIn {\r\n  from { opacity: 0; transform: translateX(-20px); }\r\n  to { opacity: 1; transform: translateX(0); }\r\n}\r\n\r\n@keyframes glow {\r\n  0%, 100% { box-shadow: 0 0 20px rgba(32, 255, 77, 0.3); }\r\n  50% { box-shadow: 0 0 30px rgba(32, 255, 77, 0.6); }\r\n} ", "/* App主容器 */\n.App {\n  text-align: center;\n  min-height: 100vh;\n  position: relative;\n}\n\n/* 加载界面 */\n.loading-screen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  text-align: center;\n  animation: fadeIn 1s ease-out;\n}\n\n.loading-logo .logo-text {\n  font-family: 'Orbitron', monospace;\n  font-size: 3rem;\n  font-weight: 900;\n  color: #20ff4d;\n  text-shadow: \n    0 0 10px rgba(32, 255, 77, 0.8),\n    0 0 20px rgba(32, 255, 77, 0.6),\n    0 0 30px rgba(32, 255, 77, 0.4);\n  margin-bottom: 0.5rem;\n  letter-spacing: 3px;\n}\n\n.loading-logo .logo-subtitle {\n  font-family: '<PERSON><PERSON>ni', sans-serif;\n  font-size: 1.2rem;\n  color: #00d4aa;\n  letter-spacing: 8px;\n  margin-bottom: 3rem;\n  opacity: 0.8;\n}\n\n.loading-progress {\n  width: 300px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 auto 2rem;\n  position: relative;\n}\n\n.progress-bar {\n  height: 100%;\n  background: linear-gradient(90deg, #20ff4d, #00d4aa, #20ff4d);\n  background-size: 200% 100%;\n  animation: progressBar 2s ease-in-out infinite;\n  border-radius: 2px;\n}\n\n@keyframes progressBar {\n  0% {\n    width: 0%;\n    background-position: 200% 0;\n  }\n  50% {\n    width: 70%;\n    background-position: 100% 0;\n  }\n  100% {\n    width: 100%;\n    background-position: 0% 0;\n  }\n}\n\n.loading-status {\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1.1rem;\n  color: #ffffff;\n  opacity: 0.7;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .loading-logo .logo-text {\n    font-size: 2rem;\n    letter-spacing: 2px;\n  }\n  \n  .loading-logo .logo-subtitle {\n    font-size: 1rem;\n    letter-spacing: 4px;\n  }\n  \n  .loading-progress {\n    width: 250px;\n  }\n}\n\n@media (max-width: 480px) {\n  .loading-logo .logo-text {\n    font-size: 1.5rem;\n    letter-spacing: 1px;\n  }\n  \n  .loading-logo .logo-subtitle {\n    font-size: 0.9rem;\n    letter-spacing: 2px;\n  }\n  \n  .loading-progress {\n    width: 200px;\n  }\n} ", "/* 首页主容器 */\r\n.homepage {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 背景动画 */\r\n.background-animation {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  width: 4px;\r\n  height: 4px;\r\n  background: #20ff4d;\r\n  border-radius: 50%;\r\n  animation: float 20s infinite linear;\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.particle:nth-child(1) {\r\n  top: 20%;\r\n  left: 10%;\r\n  animation-delay: 0s;\r\n  animation-duration: 25s;\r\n}\r\n\r\n.particle:nth-child(2) {\r\n  top: 60%;\r\n  left: 80%;\r\n  animation-delay: -5s;\r\n  animation-duration: 30s;\r\n}\r\n\r\n.particle:nth-child(3) {\r\n  top: 80%;\r\n  left: 30%;\r\n  animation-delay: -10s;\r\n  animation-duration: 35s;\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translateY(0px) translateX(0px); opacity: 0; }\r\n  10% { opacity: 1; }\r\n  90% { opacity: 1; }\r\n  100% { transform: translateY(-100vh) translateX(50px); opacity: 0; }\r\n}\r\n\r\n.grid-lines {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-image: \r\n    linear-gradient(rgba(32, 255, 77, 0.03) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(32, 255, 77, 0.03) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  animation: gridMove 20s linear infinite;\r\n}\r\n\r\n@keyframes gridMove {\r\n  0% { transform: translate(0, 0); }\r\n  100% { transform: translate(50px, 50px); }\r\n}\r\n\r\n/* 顶部时间显示 */\r\n.time-display {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  z-index: 10;\r\n  text-align: right;\r\n}\r\n\r\n.current-time {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.2rem;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.system-status {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #00d4aa;\r\n  margin-top: 5px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 文件选择器按钮 */\r\n.file-selector-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #1a1a2e;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  margin-top: 10px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.file-selector-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 主标题区域 */\r\n.main-header {\r\n  text-align: center;\r\n  padding: 80px 20px 60px;\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n\r\n.title-container {\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.homepage .main-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 3.5rem;\r\n  font-weight: 900;\r\n  color: #ffffff;\r\n  text-shadow:\r\n    0 0 20px rgba(32, 255, 77, 0.6),\r\n    0 0 40px rgba(32, 255, 77, 0.3);\r\n  margin-bottom: 1rem;\r\n  letter-spacing: 4px;\r\n  animation: fadeIn 1s ease-out;\r\n}\r\n\r\n.sub-title {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.8rem;\r\n  font-weight: 600;\r\n  color: #00d4aa;\r\n  margin-bottom: 2rem;\r\n  letter-spacing: 2px;\r\n  animation: fadeIn 1s ease-out 0.3s both;\r\n}\r\n\r\n.title-divider {\r\n  width: 200px;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, transparent, #20ff4d, transparent);\r\n  margin: 2rem auto;\r\n  animation: fadeIn 1s ease-out 0.6s both;\r\n}\r\n\r\n.system-description {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  letter-spacing: 1px;\r\n  animation: fadeIn 1s ease-out 0.9s both;\r\n}\r\n\r\n/* 导航网格 */\r\n.navigation-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\r\n  gap: 30px;\r\n  padding: 0 40px 40px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  z-index: 1;\r\n  position: relative;\r\n  flex: 1;\r\n}\r\n\r\n.nav-card {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(32, 255, 77, 0.2);\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  animation: slideIn 0.6s ease-out var(--delay) both;\r\n}\r\n\r\n.nav-card:hover {\r\n  transform: translateY(-10px);\r\n  border-color: var(--color);\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.3),\r\n    0 0 30px rgba(32, 255, 77, 0.2);\r\n}\r\n\r\n.nav-card:hover .card-glow {\r\n  opacity: 1;\r\n}\r\n\r\n.card-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: radial-gradient(circle at center, var(--color), transparent 70%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  pointer-events: none;\r\n  mix-blend-mode: screen;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-icon {\r\n  font-size: 2.5rem;\r\n  filter: grayscale(100%);\r\n  transition: filter 0.3s ease;\r\n}\r\n\r\n.nav-card:hover .card-icon {\r\n  filter: grayscale(0%);\r\n}\r\n\r\n.card-number {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--color);\r\n  opacity: 0.6;\r\n}\r\n\r\n.card-content {\r\n  text-align: left;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.card-title {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.4rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 8px;\r\n  line-height: 1.3;\r\n}\r\n\r\n.card-subtitle {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  color: var(--color);\r\n  margin-bottom: 12px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.card-description {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.95rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  line-height: 1.5;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  opacity: 0;\r\n  transform: translateY(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-card:hover .card-footer {\r\n  opacity: 1;\r\n  transform: translateY(0);\r\n}\r\n\r\n.enter-text {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  color: var(--color);\r\n  font-weight: 600;\r\n}\r\n\r\n.arrow {\r\n  font-size: 1.2rem;\r\n  color: var(--color);\r\n  animation: arrowPulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes arrowPulse {\r\n  0%, 100% { transform: translateX(0); }\r\n  50% { transform: translateX(5px); }\r\n}\r\n\r\n/* 底部状态栏 */\r\n.status-bar {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 40px;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-top: 1px solid rgba(32, 255, 77, 0.2);\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n\r\n.status-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-label {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.status-value {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 0.9rem;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-value.online {\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .navigation-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n    gap: 25px;\r\n    padding: 0 30px 30px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-title {\r\n    font-size: 2.5rem;\r\n    letter-spacing: 2px;\r\n  }\r\n  \r\n  .sub-title {\r\n    font-size: 1.4rem;\r\n  }\r\n  \r\n  .navigation-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n    padding: 0 20px 20px;\r\n  }\r\n  \r\n  .nav-card {\r\n    padding: 25px;\r\n  }\r\n  \r\n  .status-bar {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .time-display {\r\n    position: static;\r\n    text-align: center;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .main-title {\r\n    font-size: 2rem;\r\n    letter-spacing: 1px;\r\n  }\r\n  \r\n  .sub-title {\r\n    font-size: 1.2rem;\r\n  }\r\n  \r\n  .main-header {\r\n    padding: 60px 15px 40px;\r\n  }\r\n  \r\n  .navigation-grid {\r\n    padding: 0 15px 15px;\r\n  }\r\n  \r\n  .nav-card {\r\n    padding: 20px;\r\n  }\r\n} ", "/* FileSelector.css - 文件选择器组件样式 */\r\n\r\n.file-selector-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.file-selector-modal {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 15px;\r\n  padding: 0;\r\n  width: 90%;\r\n  max-width: 500px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\r\n  animation: modalSlideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-50px) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.file-selector-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.file-selector-header h3 {\r\n  margin: 0;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ffffff;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  padding: 5px;\r\n  border-radius: 50%;\r\n  width: 35px;\r\n  height: 35px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.file-selector-content {\r\n  padding: 25px;\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-file-info {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.current-file-info p {\r\n  margin: 0;\r\n  color: #00d4aa;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.file-upload-area {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-zone {\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 12px;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background: rgba(255, 255, 255, 0.02);\r\n}\r\n\r\n.upload-zone:hover {\r\n  border-color: #00d4aa;\r\n  background: rgba(0, 212, 170, 0.05);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 3rem;\r\n  margin-bottom: 15px;\r\n  opacity: 0.7;\r\n}\r\n\r\n.upload-zone p {\r\n  margin: 5px 0;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n  margin-top: 10px !important;\r\n}\r\n\r\n.selected-file-info {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.selected-file-info p {\r\n  margin: 5px 0;\r\n  color: #20ff4d;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.upload-status {\r\n  padding: 12px 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.upload-status.success {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  color: #20ff4d;\r\n}\r\n\r\n.upload-status.error {\r\n  background: rgba(255, 87, 87, 0.1);\r\n  border: 1px solid rgba(255, 87, 87, 0.3);\r\n  color: #ff5757;\r\n}\r\n\r\n.upload-status.info {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n  color: #ffc107;\r\n}\r\n\r\n.file-selector-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  padding: 20px 25px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  background: rgba(255, 255, 255, 0.02);\r\n}\r\n\r\n.cancel-btn, .upload-btn {\r\n  flex: 1;\r\n  padding: 12px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cancel-btn {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: #ffffff;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.cancel-btn:hover:not(:disabled) {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.upload-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #1a1a2e;\r\n}\r\n\r\n.upload-btn:hover:not(:disabled) {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.cancel-btn:disabled, .upload-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none !important;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.file-selector-content::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 170, 0.5);\r\n  border-radius: 3px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 170, 0.7);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .file-selector-modal {\r\n    width: 95%;\r\n    margin: 20px;\r\n  }\r\n  \r\n  .file-selector-header {\r\n    padding: 15px 20px;\r\n  }\r\n  \r\n  .file-selector-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .file-selector-actions {\r\n    padding: 15px 20px;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .upload-zone {\r\n    padding: 30px 15px;\r\n  }\r\n  \r\n  .upload-icon {\r\n    font-size: 2.5rem;\r\n  }\r\n} ", "/* 工作目标管理责任书页面样式 - 优化版 */\r\n.work-target {\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding: 10px 15px;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  border-bottom: 2px solid rgba(32, 255, 77, 0.3);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.back-button {\r\n  padding: 8px 16px;\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n  border: none;\r\n  border-radius: 6px;\r\n  color: #000;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.work-target .page-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 2.2rem;\r\n  font-weight: 700;\r\n  color: #00d4aa;\r\n  text-align: center;\r\n  flex: 1;\r\n  margin: 0 15px;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.sync-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #00d4aa;\r\n  font-size: 0.8rem;\r\n}\r\n\r\n.status-dot {\r\n  width: 6px;\r\n  height: 6px;\r\n  background: #20ff4d;\r\n  border-radius: 50%;\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n/* 数据概览 */\r\n.data-overview {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 30px;\r\n  padding: 15px 30px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.overview-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.overview-number {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: #20ff4d;\r\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.5);\r\n}\r\n\r\n.overview-label {\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.content-area {\r\n  padding: 20px 30px 30px;\r\n  max-width: 1600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 三个大标题区域 */\r\n.section-tabs {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n  gap: 15px;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.section-tab {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.section-tab:hover,\r\n.section-tab.active {\r\n  transform: translateY(-3px);\r\n  border-color: var(--section-color);\r\n  background: rgba(255, 255, 255, 0.08);\r\n  box-shadow: \r\n    0 8px 25px rgba(0, 0, 0, 0.3),\r\n    0 0 20px var(--section-color);\r\n}\r\n\r\n.tab-icon {\r\n  font-size: 2rem;\r\n  filter: grayscale(100%);\r\n  transition: filter 0.3s ease;\r\n}\r\n\r\n.section-tab:hover .tab-icon,\r\n.section-tab.active .tab-icon {\r\n  filter: grayscale(0%);\r\n}\r\n\r\n.tab-content {\r\n  flex: 1;\r\n  text-align: left;\r\n}\r\n\r\n.tab-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 5px;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.tab-count {\r\n  font-size: 0.8rem;\r\n  color: var(--section-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.tab-arrow {\r\n  font-size: 1rem;\r\n  color: var(--section-color);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: rgba(255, 255, 255, 0.03);\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  border: 1px solid rgba(32, 255, 77, 0.2);\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.table-header h3 {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.2rem;\r\n  color: #20ff4d;\r\n  margin: 0;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.table-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.edit-hint {\r\n  font-size: 0.8rem;\r\n  color: #00d4aa;\r\n  opacity: 0.8;\r\n}\r\n\r\n.refresh-btn {\r\n  padding: 6px 12px;\r\n  background: rgba(32, 255, 77, 0.2);\r\n  border: 1px solid #20ff4d;\r\n  border-radius: 4px;\r\n  color: #20ff4d;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.8rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 表格概要信息 */\r\n.table-summary {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 15px;\r\n  padding: 10px 15px;\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  font-size: 0.9rem;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.table-summary span {\r\n  color: #20ff4d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 重点工作说明内联样式 */\r\n.key-work-notice-inline {\r\n  color: #ffa500 !important;\r\n  font-weight: 600 !important;\r\n  font-family: 'Orbitron', monospace !important;\r\n  background: rgba(255, 165, 0, 0.15);\r\n  padding: 4px 12px;\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(255, 165, 0, 0.4);\r\n  font-size: 0.85rem !important;\r\n}\r\n\r\n/* 数据表格 - 优化列宽 */\r\n.data-table-container {\r\n  overflow-x: auto;\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.data-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  font-size: 0.9rem; /* 增大字体提升可读性 */\r\n  table-layout: fixed;\r\n  line-height: 1.5; /* 增加行高 */\r\n}\r\n\r\n/* 优化的列宽设置 */\r\n.data-table .col-number { width: 5%; }\r\n.data-table .col-indicator { width: 15%; }\r\n.data-table .col-target { width: 30%; }\r\n.data-table .col-weight { width: 8%; }\r\n.data-table .col-standard { width: 22%; }\r\n.data-table .col-category { width: 10%; }\r\n.data-table .col-responsible { width: 10%; }\r\n\r\n.data-table th {\r\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.4), rgba(32, 255, 77, 0.3)); /* 增强背景对比度 */\r\n  color: #ffffff;\r\n  padding: 12px 10px; /* 增加内边距 */\r\n  text-align: center;\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 0.85rem; /* 稍微增大表头字体 */\r\n  font-weight: 700; /* 增强字体粗细 */\r\n  border-bottom: 2px solid #20ff4d;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n  vertical-align: middle;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */\r\n}\r\n\r\n.data-row {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.data-row:hover {\r\n  background: rgba(32, 255, 77, 0.12); /* 增强悬停效果 */\r\n  transform: translateY(-1px); /* 轻微上移效果 */\r\n  box-shadow: 0 2px 8px rgba(32, 255, 77, 0.2); /* 添加阴影 */\r\n}\r\n\r\n.data-row:nth-child(even) {\r\n  background: rgba(255, 255, 255, 0.04); /* 增强斑马纹对比度 */\r\n}\r\n\r\n/* 正确的合并单元格样式及内容居中 */\r\n.merged-start-row {\r\n  /* border-left: 3px solid rgba(32, 255, 77, 0.6); // 可以移除或调整 */\r\n}\r\n\r\n.merged-row {\r\n  background: rgba(32, 255, 77, 0.03) !important;\r\n  /* border-left: 3px solid rgba(32, 255, 77, 0.3); // 可以移除或调整 */\r\n}\r\n\r\n.merged-row:hover {\r\n  background: rgba(32, 255, 77, 0.1) !important;\r\n}\r\n\r\n.data-cell.merged-cell-content { /* 应用于具有rowSpan的<td> */\r\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.15), rgba(32, 255, 77, 0.1));\r\n  text-align: center;     /* 水平居中 */\r\n  vertical-align: middle; /* 垂直居中 */\r\n  font-weight: 600;\r\n  border-right: 2px solid rgba(32, 255, 77, 0.4);\r\n  position: relative;\r\n}\r\n\r\n.data-cell.merged-cell-content::after { /* 强调合并单元格的左边框 */\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 3px;\r\n  background: linear-gradient(to bottom, #20ff4d, #00d4aa);\r\n}\r\n\r\n.data-cell {\r\n  padding: 10px 8px; /* 增加内边距提升可读性 */\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* 增强边框对比度 */\r\n  border-right: 1px solid rgba(255, 255, 255, 0.08);\r\n  transition: all 0.2s ease;\r\n  text-align: center;        /* 添加：水平居中 */\r\n  vertical-align: middle;    /* 修改：从top改为middle，垂直居中 */\r\n  word-wrap: break-word;\r\n  color: #ffffff; /* 确保文字颜色 */\r\n  line-height: 1.6; /* 增加行高 */\r\n  /* white-space: pre-wrap; // 如果使用<br/>则不再严格需要，但保留无害 */\r\n}\r\n\r\n.data-cell.editable {\r\n  cursor: pointer;\r\n}\r\n\r\n.data-cell.editable:hover {\r\n  background: rgba(32, 255, 77, 0.18); /* 增强悬停背景 */\r\n  border-color: rgba(32, 255, 77, 0.3); /* 添加边框高亮 */\r\n  box-shadow: inset 0 0 8px rgba(32, 255, 77, 0.2); /* 内阴影效果 */\r\n}\r\n\r\n.cell-content {\r\n  display: block; /* 使span占据整个单元格，方便点击和对齐 */\r\n  width: 100%;\r\n  color: #ffffff;\r\n  line-height: 1.6; /* 增加行高使多行文本更舒适 */\r\n  word-wrap: break-word;\r\n  overflow-wrap: break-word;\r\n  white-space: normal; /* 允许自动换行，并通过<br/>强制换行 */\r\n  text-align: center;        /* 添加：确保内容居中显示 */\r\n  font-weight: 500; /* 稍微增加字体粗细 */\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 添加文字阴影增强可读性 */\r\n}\r\n\r\n.editable-cell {\r\n  border-radius: 3px;\r\n  padding: 4px 6px;\r\n  transition: all 0.2s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.editable-cell:hover {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border-color: rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.cell-input { /* 修改为textarea */\r\n  width: 100%;\r\n  box-sizing: border-box; /* 确保padding和border不增加总宽度 */\r\n  background: rgba(0, 0, 0, 0.8);\r\n  border: 2px solid #20ff4d;\r\n  border-radius: 3px;\r\n  padding: 6px 8px;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.85rem;\r\n  outline: none;\r\n  resize: vertical; /* 允许用户调整高度 */\r\n  min-height: 30px; /* 最小高度 */\r\n  line-height: 1.4;\r\n  text-align: center;        /* 添加：编辑时也居中显示 */\r\n}\r\n\r\n.cell-input:focus {\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.5);\r\n  background: rgba(10, 10, 20, 0.9); /* 深一点的背景色 */\r\n}\r\n\r\n/* 欢迎消息 */\r\n.welcome-message {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.welcome-message h3 {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.3rem;\r\n  color: #20ff4d;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.welcome-message p {\r\n  font-size: 1rem;\r\n  margin-bottom: 20px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-data {\r\n  text-align: center;\r\n  padding: 30px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* 加载状态 */\r\n.work-target-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 100vh;\r\n  color: #20ff4d;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 30px;\r\n  height: 30px;\r\n  border: 2px solid rgba(32, 255, 77, 0.3);\r\n  border-top: 2px solid #20ff4d;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .section-tabs {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .content-area {\r\n    padding: 15px 20px 25px;\r\n  }\r\n  \r\n  /* 调整表格列宽 */\r\n  .data-table .col-target { width: 200px; }\r\n  .data-table .col-standard { width: 180px; }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .data-overview {\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    margin-bottom: 20px;\r\n    padding: 10px 15px;\r\n  }\r\n  \r\n  .page-title {\r\n    font-size: 1.2rem;\r\n    margin: 0;\r\n  }\r\n  \r\n  .table-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .data-table {\r\n    font-size: 0.8rem; /* 稍微增大移动端字体 */\r\n  }\r\n\r\n  .data-table th,\r\n  .data-table td {\r\n    padding: 8px 6px; /* 增加移动端内边距 */\r\n  }\r\n  \r\n  /* 移动端列宽调整 */\r\n  .data-table .col-target { width: 150px; }\r\n  .data-table .col-standard { width: 120px; }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeIn {\r\n  from { \r\n    opacity: 0; \r\n    transform: translateY(15px); \r\n  }\r\n  to { \r\n    opacity: 1; \r\n    transform: translateY(0); \r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 0.6; }\r\n  50% { opacity: 1; }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 新材级重点工作突出显示样式 */\r\n.highlight-work {\r\n  background: rgba(255, 165, 0, 0.08) !important;\r\n  border-left: 4px solid #ffa500 !important;\r\n}\r\n\r\n.highlight-work:hover {\r\n  background: rgba(255, 165, 0, 0.15) !important;\r\n}\r\n\r\n.highlight-cell {\r\n  background: rgba(255, 165, 0, 0.1);\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  box-shadow: inset 0 0 0 1px rgba(255, 165, 0, 0.3);\r\n}\r\n\r\n.highlight-merged-cell {\r\n  background: linear-gradient(135deg, rgba(255, 165, 0, 0.25), rgba(255, 165, 0, 0.15)) !important;\r\n  border-right: 2px solid rgba(255, 165, 0, 0.6) !important;\r\n}\r\n\r\n.highlight-merged-cell::after {\r\n  background: linear-gradient(to bottom, #ffa500, #ff8c00) !important;\r\n}\r\n\r\n/* 下载按钮样式 */\r\n.download-btn {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n  border: none;\r\n  color: #000;\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.download-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(77, 208, 255, 0.4);\r\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\r\n}\r\n\r\n.download-btn:active {\r\n  transform: translateY(0);\r\n} ", "/* 选择性下载模态框样式 */\r\n.selective-download-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.selective-download-modal {\r\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\r\n  border: 2px solid rgba(32, 255, 77, 0.3);\r\n  border-radius: 15px;\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.6),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  width: 90%;\r\n  max-width: 800px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模态框头部 */\r\n.modal-header {\r\n  background: linear-gradient(90deg, rgba(32, 255, 77, 0.1), rgba(77, 255, 200, 0.1));\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(32, 255, 77, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #20ff4d;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff4757;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 71, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 模态框内容 */\r\n.modal-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n/* 分类部分 */\r\n.category-section {\r\n  margin-bottom: 25px;\r\n  border: 1px solid rgba(32, 255, 77, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(15, 15, 35, 0.5);\r\n}\r\n\r\n.category-header {\r\n  background: linear-gradient(90deg, rgba(32, 255, 77, 0.15), rgba(77, 255, 200, 0.15));\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid rgba(32, 255, 77, 0.2);\r\n}\r\n\r\n.category-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.category-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #20ff4d;\r\n  cursor: pointer;\r\n}\r\n\r\n.category-title {\r\n  color: #20ff4d;\r\n  text-shadow: 0 0 5px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.category-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.2), rgba(77, 255, 200, 0.2));\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  color: #20ff4d;\r\n  padding: 6px 12px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.3), rgba(77, 255, 200, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 项目列表 */\r\n.items-list {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.item-row {\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid rgba(32, 255, 77, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.item-row:hover {\r\n  background: rgba(32, 255, 77, 0.05);\r\n}\r\n\r\n.item-row.highlight-item {\r\n  background: rgba(255, 165, 0, 0.1);\r\n  border-left: 3px solid #ffa500;\r\n}\r\n\r\n.item-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.item-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #20ff4d;\r\n  cursor: pointer;\r\n}\r\n\r\n.item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.item-name {\r\n  color: #e8e8e8;\r\n  font-size: 0.95rem;\r\n  flex: 1;\r\n}\r\n\r\n.item-weight {\r\n  color: #4dd0ff;\r\n  font-size: 0.85rem;\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.highlight-badge {\r\n  background: linear-gradient(135deg, #ffa500, #ff8c00);\r\n  color: white;\r\n  font-size: 0.75rem;\r\n  padding: 3px 8px;\r\n  border-radius: 12px;\r\n  font-weight: 600;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(32, 255, 77, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.statistics {\r\n  display: flex;\r\n  gap: 20px;\r\n  color: #4dd0ff;\r\n  font-weight: 600;\r\n}\r\n\r\n.statistics span {\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 8px 15px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\r\n}\r\n\r\n.format-selection {\r\n  color: #e8e8e8;\r\n}\r\n\r\n.format-selection label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-selection select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  color: #20ff4d;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.format-selection select:focus {\r\n  outline: none;\r\n  border-color: #20ff4d;\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.cancel-btn, .download-btn {\r\n  padding: 12px 25px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\r\n  color: #ff4757;\r\n  border: 1px solid rgba(255, 71, 87, 0.5);\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.download-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 20px rgba(32, 255, 77, 0.4);\r\n}\r\n\r\n.download-btn:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.modal-content::-webkit-scrollbar,\r\n.items-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-track,\r\n.items-list::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb,\r\n.items-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb:hover,\r\n.items-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .selective-download-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .modal-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .modal-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .category-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modal-footer {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .statistics {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .format-selection {\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n} ", "/* WorkTracking.css - 重点工作跟踪页面样式 */\r\n\r\n.work-tracking-container {\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  padding: 15px;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n/* 页面头部 - 优化布局 */\r\n.page-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding: 10px 0;\r\n}\r\n\r\n.header-center {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.back-btn-top {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  order: -1;\r\n}\r\n\r\n.back-btn-top:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.page-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 2.2rem;\r\n  color: #00d4aa;\r\n  margin-bottom: 8px;\r\n  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 1rem;\r\n  color: #20ff4d;\r\n  margin-bottom: 0;\r\n  opacity: 0.8;\r\n}\r\n\r\n.sync-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.status-indicator {\r\n  padding: 5px 12px;\r\n  border-radius: 20px;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-indicator.success {\r\n  background: rgba(32, 255, 77, 0.2);\r\n  color: #20ff4d;\r\n  border: 1px solid #20ff4d;\r\n}\r\n\r\n.status-indicator.error {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n}\r\n\r\n.status-indicator.pending {\r\n  background: rgba(255, 193, 7, 0.2);\r\n  color: #ffc107;\r\n  border: 1px solid #ffc107;\r\n}\r\n\r\n/* 控制面板 */\r\n.control-panel {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 25px;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 30px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-group label {\r\n  color: #00d4aa;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n\r\n.type-selector {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  padding: 8px 15px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  min-width: 150px;\r\n}\r\n\r\n.type-selector:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 月份导航 */\r\n.month-navigation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(0, 212, 170, 0.1);\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 新月份导航样式 */\r\n.month-navigation-new {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(0, 212, 170, 0.08);\r\n  padding: 12px 20px;\r\n  border-radius: 25px;\r\n  border: 1px solid rgba(0, 212, 170, 0.4);\r\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\r\n}\r\n\r\n.nav-btn-new {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 18px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: none;\r\n  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.nav-btn-new:hover:not(:disabled) {\r\n  transform: scale(1.05) translateY(-1px);\r\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n}\r\n\r\n.nav-btn-new:disabled {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.3);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.current-months-new {\r\n  color: #00d4aa;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n  min-width: 100px;\r\n  text-align: center;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\r\n  font-family: 'Orbitron', monospace;\r\n}\r\n\r\n.refresh-btn-new {\r\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 12px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(77, 208, 255, 0.2);\r\n  white-space: nowrap;\r\n}\r\n\r\n.refresh-btn-new:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(77, 208, 255, 0.4);\r\n  background: linear-gradient(45deg, #00d4aa, #4dd0ff);\r\n}\r\n\r\n.nav-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 15px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-btn:hover:not(:disabled) {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.nav-btn:disabled {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.3);\r\n  cursor: not-allowed;\r\n}\r\n\r\n.current-months {\r\n  color: #00d4aa;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n  min-width: 120px;\r\n  text-align: center;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.refresh-btn, .back-btn {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\r\n  color: #000;\r\n}\r\n\r\n.back-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n}\r\n\r\n.refresh-btn:hover, .back-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 统一控制面板 V2 - 优化美观布局 */\r\n.unified-control-panel-v2 {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 30px;\r\n  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 统一控制面板 - 美观布局 (保留兼容) */\r\n.unified-control-panel {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 30px;\r\n  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);\r\n}\r\n\r\n/* 高科技装饰元素 */\r\n.tech-decoration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 120px;\r\n  height: 100px;\r\n  position: relative;\r\n}\r\n\r\n.hologram-container {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hologram-ring {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  border: 2px solid transparent;\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  background-clip: padding-box;\r\n  animation: rotate-ring 4s linear infinite;\r\n}\r\n\r\n.ring-segment {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.ring-1 {\r\n  border-top: 2px solid #00d4aa;\r\n  animation: rotate-segment-1 2s linear infinite;\r\n}\r\n\r\n.ring-2 {\r\n  border-right: 2px solid #20ff4d;\r\n  animation: rotate-segment-2 3s linear infinite reverse;\r\n}\r\n\r\n.ring-3 {\r\n  border-bottom: 2px solid #4dd0ff;\r\n  animation: rotate-segment-3 2.5s linear infinite;\r\n}\r\n\r\n.central-core {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 30px;\r\n  height: 30px;\r\n  background: radial-gradient(circle, rgba(0, 212, 170, 0.8), rgba(32, 255, 77, 0.4));\r\n  border-radius: 50%;\r\n  box-shadow: 0 0 20px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.core-pulse {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  background: #00d4aa;\r\n  border-radius: 50%;\r\n  animation: pulse-core 2s ease-in-out infinite;\r\n}\r\n\r\n.core-data {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.data-stream {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 8px;\r\n  background: linear-gradient(to bottom, #20ff4d, transparent);\r\n  animation: data-flow 1.5s linear infinite;\r\n}\r\n\r\n.data-stream.delay-1 {\r\n  animation-delay: 0.5s;\r\n  transform: rotate(120deg);\r\n}\r\n\r\n.data-stream.delay-2 {\r\n  animation-delay: 1s;\r\n  transform: rotate(240deg);\r\n}\r\n\r\n.tech-grid {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 20px;\r\n  opacity: 0.3;\r\n}\r\n\r\n.grid-line {\r\n  position: absolute;\r\n  background: linear-gradient(90deg, transparent, #00d4aa, transparent);\r\n  height: 1px;\r\n  width: 100%;\r\n  animation: grid-scan 3s linear infinite;\r\n}\r\n\r\n.grid-line:nth-child(1) { top: 0; animation-delay: 0s; }\r\n.grid-line:nth-child(2) { top: 5px; animation-delay: 0.5s; }\r\n.grid-line:nth-child(3) { top: 10px; animation-delay: 1s; }\r\n.grid-line:nth-child(4) { top: 15px; animation-delay: 1.5s; }\r\n\r\n/* 中间统计区域 */\r\n.stats-section-center {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n/* 左侧统计区域 (保留兼容) */\r\n.stats-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  background: linear-gradient(145deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\r\n  border: 1px solid rgba(0, 212, 170, 0.4);\r\n  border-radius: 12px;\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  min-width: 80px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n  transition: left 0.6s ease;\r\n}\r\n\r\n.stat-card:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);\r\n  border-color: rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.stat-number {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: #00d4aa;\r\n  margin-bottom: 5px;\r\n  font-family: 'Orbitron', monospace;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.stat-text {\r\n  font-size: 0.9rem;\r\n  color: #20ff4d;\r\n  font-weight: 500;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 全部展开/折叠卡片特殊样式 */\r\n.toggle-all-card {\r\n  cursor: pointer;\r\n  background: linear-gradient(145deg, rgba(255, 165, 0, 0.1), rgba(255, 215, 0, 0.1));\r\n  border: 1px solid rgba(255, 165, 0, 0.4);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.toggle-all-card:hover {\r\n  background: linear-gradient(145deg, rgba(255, 165, 0, 0.2), rgba(255, 215, 0, 0.2));\r\n  border-color: rgba(255, 165, 0, 0.6);\r\n  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);\r\n  transform: translateY(-3px) scale(1.05);\r\n}\r\n\r\n.toggle-all-card .stat-number {\r\n  color: #ffa500;\r\n  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);\r\n}\r\n\r\n.toggle-all-card .stat-text {\r\n  color: #ffd700;\r\n}\r\n\r\n.toggle-icon {\r\n  font-size: 1.5rem;\r\n  display: inline-block;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.toggle-all-card:hover .toggle-icon {\r\n  transform: scale(1.2);\r\n}\r\n\r\n/* 右侧控制区域 */\r\n.controls-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n/* 数据统计 */\r\n.data-stats {\r\n  display: flex;\r\n  gap: 30px;\r\n  margin-bottom: 25px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  border-radius: 12px;\r\n  padding: 15px 25px;\r\n  text-align: center;\r\n}\r\n\r\n.data-stats-compact .stat-item {\r\n  padding: 10px 15px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.stat-label {\r\n  color: #20ff4d;\r\n  font-size: 0.9rem;\r\n  display: block;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-value {\r\n  color: #ffffff;\r\n  font-size: 1.4rem;\r\n  font-weight: 700;\r\n}\r\n\r\n/* 工作类型分组 */\r\n.tracking-table-container {\r\n  space-y: 20px;\r\n}\r\n\r\n.work-type-group {\r\n  background: rgba(255, 255, 255, 0.03);\r\n  border: 1px solid rgba(0, 212, 170, 0.2);\r\n  border-radius: 15px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n}\r\n\r\n.group-header {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.2), rgba(32, 255, 77, 0.1));\r\n  padding: 15px 25px;\r\n  cursor: pointer;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.group-header:hover {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));\r\n}\r\n\r\n.group-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  color: #00d4aa;\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.collapse-icon {\r\n  color: #20ff4d;\r\n  font-size: 1rem;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* 项目数量高亮显示 */\r\n.item-count-highlight {\r\n  color: #ffd700;\r\n  font-weight: 800;\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(255, 215, 0, 0.4);\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);\r\n  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\r\n  display: inline-block;\r\n  margin: 0 2px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.item-count-highlight:hover {\r\n  color: #ffff00;\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.2));\r\n  border-color: rgba(255, 215, 0, 0.6);\r\n  text-shadow: 0 0 12px rgba(255, 215, 0, 0.8);\r\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* 表格容器 */\r\n.table-container {\r\n  overflow-x: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.tracking-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background: rgba(255, 255, 255, 0.02);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n}\r\n\r\n.tracking-table th {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));\r\n  color: #ffffff;\r\n  padding: 15px 10px;\r\n  text-align: center;\r\n  font-weight: 700;\r\n  font-size: 0.9rem;\r\n  border-bottom: 2px solid rgba(0, 212, 170, 0.5);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.tracking-table td {\r\n  padding: 12px 8px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  text-align: center;\r\n  vertical-align: middle;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.data-row:hover td {\r\n  background: rgba(0, 212, 170, 0.05);\r\n}\r\n\r\n/* 列宽控制 */\r\n.col-number { width: 60px; }\r\n.col-type { width: 120px; }\r\n.col-indicator { width: 150px; }\r\n.col-total-target { width: 120px; }\r\n.col-target { width: 150px; }\r\n.col-method { width: 200px; }\r\n.col-responsible { width: 100px; }\r\n.col-frequency { width: 80px; }\r\n.col-month-plan, .col-month-complete { width: 150px; }\r\n\r\n/* 可编辑单元格 */\r\n.data-cell.editable {\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.data-cell.editable:hover {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.cell-input {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 2px solid #00d4aa;\r\n  border-radius: 4px;\r\n  color: #ffffff;\r\n  padding: 5px 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.cell-input:focus {\r\n  outline: none;\r\n  border-color: #20ff4d;\r\n  box-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.cell-content {\r\n  display: block;\r\n  word-break: break-word;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 月份列特殊样式 */\r\n.month-plan {\r\n  background: rgba(77, 208, 255, 0.05);\r\n  border-left: 3px solid rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.month-complete {\r\n  background: rgba(32, 255, 77, 0.05);\r\n  border-left: 3px solid rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: 3px solid rgba(0, 212, 170, 0.3);\r\n  border-top: 3px solid #00d4aa;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 高科技元素动画 */\r\n@keyframes rotate-ring {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-1 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-2 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-3 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes pulse-core {\r\n  0%, 100% { \r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% { \r\n    transform: translate(-50%, -50%) scale(1.2);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n@keyframes data-flow {\r\n  0% { \r\n    transform: translateY(-10px) scale(1);\r\n    opacity: 0;\r\n  }\r\n  50% { \r\n    transform: translateY(0) scale(1.2);\r\n    opacity: 1;\r\n  }\r\n  100% { \r\n    transform: translateY(10px) scale(1);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes grid-scan {\r\n  0% { \r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  50% { \r\n    opacity: 1;\r\n  }\r\n  100% { \r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  font-size: 1.2rem;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .unified-control-panel-v2 {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .tech-decoration {\r\n    width: 100%;\r\n    height: 60px;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n  }\r\n\r\n  .hologram-container {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin-bottom: 0;\r\n    margin-right: 20px;\r\n  }\r\n\r\n  .stats-section-center {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n  }\r\n\r\n  .controls-section {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .month-navigation-new {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n\r\n  .refresh-btn-new {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n\r\n  .unified-control-panel {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .stats-section {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .filter-controls {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .month-navigation {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .data-stats {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .work-tracking-container {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .page-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .header-center {\r\n    order: 1;\r\n  }\r\n\r\n  .back-btn-top {\r\n    order: 0;\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .sync-status {\r\n    order: 2;\r\n    align-self: center;\r\n  }\r\n\r\n  .stat-card {\r\n    min-width: 60px;\r\n    padding: 10px 15px;\r\n  }\r\n\r\n  .stat-number {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .nav-btn-new {\r\n    padding: 6px 12px;\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .current-months-new {\r\n    font-size: 1rem;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .tech-decoration {\r\n    width: 100%;\r\n    height: 50px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .hologram-container {\r\n    width: 40px;\r\n    height: 40px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .stats-section-center {\r\n    gap: 10px;\r\n  }\r\n  \r\n  .control-panel {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .tracking-table {\r\n    font-size: 0.8rem;\r\n  }\r\n  \r\n  .tracking-table th,\r\n  .tracking-table td {\r\n    padding: 8px 5px;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条 */\r\n.table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n}\r\n\r\n/* 负责人筛选圆球按钮样式 */\r\n.responsible-filter-orb {\r\n  position: fixed;\r\n  top: 120px;\r\n  left: 30px;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  cursor: pointer;\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: \r\n    0 8px 32px rgba(0, 212, 170, 0.4),\r\n    inset 0 2px 8px rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n  animation: orb-pulse 2s infinite ease-in-out;\r\n}\r\n\r\n.responsible-filter-orb:hover {\r\n  transform: scale(1.1) translateY(-3px);\r\n  box-shadow: \r\n    0 12px 40px rgba(0, 212, 170, 0.6),\r\n    inset 0 2px 8px rgba(255, 255, 255, 0.3);\r\n  animation: none;\r\n}\r\n\r\n.responsible-filter-orb::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  z-index: -1;\r\n  filter: blur(8px);\r\n  opacity: 0.7;\r\n}\r\n\r\n.orb-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.filter-icon {\r\n  font-size: 28px;\r\n  color: #000;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));\r\n}\r\n\r\n.orb-tooltip {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 85px;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: #00d4aa;\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  white-space: nowrap;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.responsible-filter-orb:hover .orb-tooltip {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n@keyframes orb-pulse {\r\n  0%, 100% {\r\n    box-shadow: \r\n      0 8px 32px rgba(0, 212, 170, 0.4),\r\n      inset 0 2px 8px rgba(255, 255, 255, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow: \r\n      0 12px 40px rgba(0, 212, 170, 0.6),\r\n      inset 0 2px 8px rgba(255, 255, 255, 0.3);\r\n  }\r\n}\r\n\r\n/* 负责人筛选面板样式 */\r\n.responsible-filter-panel {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 400px;\r\n  max-height: 500px;\r\n  background: rgba(26, 26, 46, 0.95);\r\n  border: 2px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 20px;\r\n  z-index: 2000;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: \r\n    0 20px 60px rgba(0, 0, 0, 0.3),\r\n    0 0 40px rgba(0, 212, 170, 0.2);\r\n  animation: panel-slide-in 0.3s ease-out;\r\n}\r\n\r\n@keyframes panel-slide-in {\r\n  from {\r\n    opacity: 0;\r\n    transform: translate(-50%, -60%) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%) scale(1);\r\n  }\r\n}\r\n\r\n.filter-panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.filter-panel-header h3 {\r\n  color: #00d4aa;\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.4rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.close-panel-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n  border-radius: 50%;\r\n  width: 35px;\r\n  height: 35px;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-panel-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.filter-panel-content {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-option {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  padding: 8px 12px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.95rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  flex: 0 0 calc(33.33% - 4px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.filter-option:hover {\r\n  background: rgba(0, 212, 170, 0.15);\r\n  border-color: #00d4aa;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.filter-option.selected {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.3));\r\n  border-color: #00d4aa;\r\n  color: #00d4aa;\r\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.filter-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: space-between;\r\n}\r\n\r\n.clear-filter-btn, .apply-filter-btn {\r\n  flex: 1;\r\n  padding: 10px 16px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.95rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-filter-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n}\r\n\r\n.clear-filter-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.apply-filter-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: 1px solid #00d4aa;\r\n}\r\n\r\n.apply-filter-btn:hover {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 筛选状态横幅样式 */\r\n.filter-status-banner {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.15), rgba(32, 255, 77, 0.15));\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 15px;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  animation: banner-slide-down 0.3s ease-out;\r\n}\r\n\r\n@keyframes banner-slide-down {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.filter-status-text {\r\n  color: #00d4aa;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.clear-status-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n  border-radius: 8px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-status-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* 滚动条样式 */\r\n.filter-options::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  border-radius: 3px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .responsible-filter-orb {\r\n    width: 60px;\r\n    height: 60px;\r\n    top: 100px;\r\n    left: 20px;\r\n  }\r\n  \r\n  .filter-icon {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .responsible-filter-panel {\r\n    width: 90%;\r\n    max-width: 350px;\r\n  }\r\n  \r\n  .orb-tooltip {\r\n    left: 75px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* ==================== 选择性下载功能样式 ==================== */\r\n\r\n/* 操作按钮组样式 */\r\n.action-buttons-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 下载按钮样式 */\r\n.download-btn-tracking {\r\n  background: linear-gradient(135deg, #ff6b6b, #ffa726, #42a5f5);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 12px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 700;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  box-shadow: \r\n    0 4px 15px rgba(255, 107, 107, 0.3),\r\n    0 2px 8px rgba(0, 0, 0, 0.2),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.download-btn-tracking::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled)::before {\r\n  left: 100%;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled) {\r\n  transform: translateY(-3px) scale(1.05);\r\n  box-shadow: \r\n    0 8px 25px rgba(255, 107, 107, 0.4),\r\n    0 4px 15px rgba(255, 167, 38, 0.3),\r\n    0 2px 10px rgba(66, 165, 245, 0.2);\r\n  background: linear-gradient(135deg, #ff8a80, #ffcc02, #64b5f6);\r\n}\r\n\r\n.download-btn-tracking:active {\r\n  transform: translateY(-1px) scale(1.02);\r\n  transition: all 0.1s ease;\r\n}\r\n\r\n.download-btn-tracking:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: \r\n    0 2px 8px rgba(0, 0, 0, 0.1),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.download-btn-tracking:disabled::before {\r\n  display: none;\r\n}\r\n\r\n/* 下载按钮中的小loading动画 */\r\n.loading-spinner-small {\r\n  width: 14px;\r\n  height: 14px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: #000;\r\n  animation: spin-small 1s linear infinite;\r\n}\r\n\r\n@keyframes spin-small {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 科技感强化 - 为下载按钮添加科技边框效果 */\r\n.download-btn-tracking::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background: linear-gradient(45deg, \r\n    #00d4aa, #20ff4d, #4dd0ff, #ff6b6b, #ffa726, #00d4aa);\r\n  border-radius: 14px;\r\n  z-index: -1;\r\n  background-size: 300% 300%;\r\n  animation: gradient-shift 3s ease infinite;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled)::after {\r\n  opacity: 0.7;\r\n}\r\n\r\n@keyframes gradient-shift {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n  }\r\n}\r\n\r\n/* 高科技过滤模块 - 重新设计了一个科技感的筛选按钮 */\r\n.filter-section-center {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n  margin: 0 20px;\r\n}\r\n\r\n.tech-filter-module {\r\n  position: relative;\r\n  width: 120px;\r\n  height: 120px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  filter: drop-shadow(0 4px 12px rgba(0, 212, 170, 0.3));\r\n}\r\n\r\n.tech-filter-module:hover {\r\n  transform: translateY(-2px);\r\n  filter: drop-shadow(0 8px 20px rgba(0, 212, 170, 0.4));\r\n}\r\n\r\n.tech-module-background {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 100px;\r\n  height: 100px;\r\n  transform: translate(-50%, -50%);\r\n  background: linear-gradient(135deg, \r\n    rgba(0, 212, 170, 0.15) 0%, \r\n    rgba(32, 255, 77, 0.1) 50%, \r\n    rgba(77, 208, 255, 0.08) 100%);\r\n  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);\r\n  border: 2px solid rgba(0, 212, 170, 0.6);\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: \r\n    0 0 20px rgba(0, 212, 170, 0.3),\r\n    inset 0 0 20px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.tech-filter-module:hover .tech-module-background {\r\n  background: linear-gradient(135deg, \r\n    rgba(0, 212, 170, 0.25) 0%, \r\n    rgba(32, 255, 77, 0.2) 50%, \r\n    rgba(77, 208, 255, 0.15) 100%);\r\n  transform: translate(-50%, -50%) scale(1.08) rotate(5deg);\r\n  box-shadow: \r\n    0 0 30px rgba(0, 212, 170, 0.5),\r\n    inset 0 0 30px rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.circuit-pattern {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 110px;\r\n  height: 110px;\r\n  transform: translate(-50%, -50%);\r\n  border: 1px solid rgba(77, 208, 255, 0.4);\r\n  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);\r\n  animation: rotate-ring 15s linear infinite;\r\n  background: linear-gradient(45deg, \r\n    transparent 48%, \r\n    rgba(77, 208, 255, 0.2) 50%, \r\n    transparent 52%);\r\n}\r\n\r\n.circuit-pattern::before,\r\n.circuit-pattern::after {\r\n  display: none;\r\n}\r\n\r\n.energy-flow {\r\n  display: none;\r\n}\r\n\r\n.hologram-border {\r\n  display: none;\r\n}\r\n\r\n@keyframes hologram-scan {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.tech-module-content {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.icon-container {\r\n  position: relative;\r\n  width: 60px;\r\n  height: 60px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.icon-glow-ring {\r\n  position: absolute;\r\n  top: -5px;\r\n  left: -5px;\r\n  right: -5px;\r\n  bottom: -5px;\r\n  background: linear-gradient(45deg, \r\n    rgba(0, 212, 170, 0.3),\r\n    rgba(32, 255, 77, 0.2),\r\n    rgba(77, 208, 255, 0.3));\r\n  border-radius: 12px;\r\n  animation: pulse 2.5s ease-in-out infinite;\r\n  filter: blur(2px);\r\n}\r\n\r\n.tech-icon {\r\n  position: relative;\r\n  z-index: 2;\r\n  animation: icon-float 4s ease-in-out infinite;\r\n}\r\n\r\n@keyframes icon-float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-3px);\r\n  }\r\n}\r\n\r\n.scan-line {\r\n  display: none;\r\n}\r\n\r\n@keyframes scan-move {\r\n  0% {\r\n    top: 0%;\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 1;\r\n  }\r\n  90% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    top: 100%;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.module-info {\r\n  position: relative;\r\n}\r\n\r\n.module-title {\r\n  color: #00d4aa;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.6);\r\n  margin-bottom: 5px;\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.active-indicator {\r\n  position: absolute;\r\n  top: -15px;\r\n  right: -10px;\r\n  width: 12px;\r\n  height: 12px;\r\n}\r\n\r\n.pulse-dot {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #20ff4d;\r\n  border-radius: 50%;\r\n  animation: pulse-dot 1s ease-in-out infinite;\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.8);\r\n}\r\n\r\n@keyframes pulse-dot {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.3);\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n.tech-accent-lines {\r\n  display: none;\r\n}\r\n\r\n.accent-line {\r\n  display: none;\r\n}\r\n\r\n.accent-line-1,\r\n.accent-line-2,\r\n.accent-line-3 {\r\n  display: none;\r\n}\r\n\r\n@keyframes line-sweep {\r\n  0%, 100% {\r\n    opacity: 0;\r\n    transform: scaleX(0);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scaleX(1);\r\n  }\r\n}\r\n\r\n/* 响应式调整 - 下载按钮 */\r\n@media (max-width: 768px) {\r\n  .action-buttons-group {\r\n    justify-content: center;\r\n    width: 100%;\r\n  }\r\n  \r\n  .download-btn-tracking {\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .tech-filter-module {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n  \r\n  .filter-section-center {\r\n    margin: 0 10px;\r\n  }\r\n} ", "/* 模块二选择性下载模态框样式 */\r\n.work-tracking-download-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.work-tracking-download-modal {\r\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\r\n  border: 2px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.6),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  width: 90%;\r\n  max-width: 900px;\r\n  max-height: 85vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模态框头部 */\r\n.modal-header {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.1), rgba(77, 208, 255, 0.1));\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #00d4aa;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff4757;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 71, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 模态框内容 */\r\n.modal-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n/* 当前筛选条件显示 */\r\n.current-filters {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  color: #00d4aa;\r\n  font-weight: 600;\r\n  min-width: 80px;\r\n}\r\n\r\n.filter-value {\r\n  color: #4dd0ff;\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 4px 10px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.filter-item select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 6px 10px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.filter-item select:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.include-filter-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #e8e8e8;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.include-filter-checkbox input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 工作类型部分 */\r\n.work-types-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.work-type-section {\r\n  border: 1px solid rgba(0, 212, 170, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(15, 15, 35, 0.5);\r\n}\r\n\r\n.work-type-header {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.15), rgba(77, 208, 255, 0.15));\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.work-type-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.work-type-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n.work-type-title {\r\n  color: #00d4aa;\r\n  text-shadow: 0 0 5px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.work-type-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(77, 208, 255, 0.2));\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 6px 12px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.3), rgba(77, 208, 255, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 工作项目列表 */\r\n.work-items-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.work-item-row {\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.work-item-row:hover {\r\n  background: rgba(0, 212, 170, 0.05);\r\n}\r\n\r\n.work-item-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.work-item-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n.work-item-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  width: 100%;\r\n}\r\n\r\n.work-item-name {\r\n  color: #e8e8e8;\r\n  font-size: 0.95rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.work-item-responsible {\r\n  color: #4dd0ff;\r\n  font-size: 0.85rem;\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  align-self: flex-start;\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(0, 212, 170, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.statistics {\r\n  display: flex;\r\n  gap: 15px;\r\n  color: #4dd0ff;\r\n  font-weight: 600;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.statistics span {\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 8px 15px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.format-selection {\r\n  color: #e8e8e8;\r\n}\r\n\r\n.format-selection label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-selection select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.format-selection select:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.cancel-btn, .download-btn {\r\n  padding: 12px 25px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\r\n  color: #ff4757;\r\n  border: 1px solid rgba(255, 71, 87, 0.5);\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #00d4aa, #4dd0ff);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.download-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.download-btn:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.modal-content::-webkit-scrollbar,\r\n.work-items-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-track,\r\n.work-items-list::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb,\r\n.work-items-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #00d4aa, #4dd0ff);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb:hover,\r\n.work-items-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #4dd0ff, #00d4aa);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .work-tracking-download-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .modal-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .modal-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .work-type-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modal-footer {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .statistics {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .format-selection {\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filter-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n} ", "/* WorldClass.css - 对标世界一流举措页面样式 */\n\n.world-class-container {\n  min-height: 100vh;\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n  padding: 15px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%;\n}\n\n/* 页面头部样式 - 复用模块一设计 */\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20px;\n  position: relative;\n  padding: 10px 0;\n  width: 100%;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.header-center {\n  text-align: center;\n  flex: 1;\n}\n\n.back-btn-top {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  font-weight: 600;\n  font-size: 1.1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  order: -1;\n}\n\n.back-btn-top:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n.page-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 2.2rem;\n  color: #00d4aa;\n  margin-bottom: 8px;\n  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);\n}\n\n.page-subtitle {\n  font-size: 1rem;\n  color: #20ff4d;\n  margin-bottom: 0;\n  opacity: 0.8;\n}\n\n.sync-status {\n  display: flex;\n  align-items: center;\n}\n\n.status-indicator {\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.status-indicator.success {\n  background: rgba(32, 255, 77, 0.2);\n  color: #20ff4d;\n  border: 1px solid #20ff4d;\n}\n\n.status-indicator.error {\n  background: rgba(255, 87, 87, 0.2);\n  color: #ff5757;\n  border: 1px solid #ff5757;\n}\n\n.status-indicator.pending {\n  background: rgba(255, 193, 7, 0.2);\n  color: #ffc107;\n  border: 1px solid #ffc107;\n}\n\n/* 控制面板样式 */\n.control-panel {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 15px;\n  padding: 15px 20px;\n  margin-bottom: 25px;\n  backdrop-filter: blur(10px);\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n/* 左侧控件容器 */\n.controls-left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n.filter-controls {\n  display: flex;\n  align-items: center;\n  gap: 30px;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.filter-group label {\n  color: #00d4aa;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.level-selector {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  padding: 8px 15px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  width: 130px;\n}\n\n.level-selector:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.level-selector option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #00d4aa;\n  padding: 8px 15px;\n  font-weight: 600;\n  border: none;\n}\n\n.level-selector option:hover {\n  background: rgba(0, 212, 170, 0.2);\n  color: #ffffff;\n}\n\n.value-selector {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  padding: 8px 15px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  width: 280px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.value-selector:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.value-selector option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #00d4aa;\n  padding: 8px 15px;\n  font-weight: 600;\n  border: none;\n}\n\n.value-selector option:hover {\n  background: rgba(0, 212, 170, 0.2);\n  color: #ffffff;\n}\n\n/* 月份导航样式 - 参考模块二设计但保持独立 */\n.month-navigation-new {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n  background: rgba(0, 212, 170, 0.08);\n  padding: 8px 16px;\n  border-radius: 20px;\n  border: 1px solid rgba(0, 212, 170, 0.4);\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\n  width: fit-content;\n}\n\n.nav-btn-new {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: none;\n  border-radius: 15px;\n  padding: 6px 12px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);\n}\n\n.nav-btn-new:hover:not(:disabled) {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\n}\n\n.nav-btn-new:disabled {\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.3);\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.current-months-new {\n  color: #00d4aa;\n  font-weight: 700;\n  font-size: 0.95rem;\n  min-width: 80px;\n  text-align: center;\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\n  font-family: 'Orbitron', monospace;\n}\n\n.refresh-btn-new {\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\n  color: #000;\n  border: none;\n  border-radius: 15px;\n  padding: 10px 20px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(77, 208, 255, 0.2);\n  order: -1;\n}\n\n.refresh-btn-new:hover {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(77, 208, 255, 0.4);\n}\n\n/* 右侧按钮容器 */\n.action-buttons {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n/* 数据统计样式 */\n.data-stats {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  justify-content: center;\n  width: 100%;\n}\n\n.stat-item {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 10px;\n  padding: 10px 15px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n}\n\n.stat-label {\n  color: #00d4aa;\n  font-size: 0.9rem;\n  margin-right: 8px;\n}\n\n.stat-value {\n  color: #4dd0ff;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n/* 内联统计样式 - 与筛选框同行显示 */\n.stat-item-inline {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  padding: 8px 15px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  min-width: 120px;\n}\n\n.stat-item-inline .stat-label {\n  color: #00d4aa;\n  font-size: 0.9rem;\n}\n\n.stat-item-inline .stat-value {\n  color: #4dd0ff;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n/* 表格容器样式 - 最大化显示 */\n.table-container {\n  overflow-x: auto;\n  border-radius: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  width: 100%;\n  margin: 0 auto;\n  max-height: calc(100vh - 200px);\n  overflow-y: auto;\n}\n\n.world-class-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n/* 表头固定样式 - 强化固定效果 */\n.world-class-table thead {\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n}\n\n.world-class-table th {\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #00d4aa;\n  padding: 15px 10px;\n  text-align: center;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  font-weight: 700;\n  font-size: 1rem;\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n/* 表格样式更新 - 数据居中显示 */\n.world-class-table td {\n  padding: 12px 10px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  vertical-align: middle;\n  text-align: center;\n  position: relative;\n}\n\n.data-row:hover td {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: rgba(0, 212, 170, 0.3);\n}\n\n/* 列宽设置 - 自适应屏幕宽度 */\n.col-number { width: 4%; min-width: 60px; } /* 序号列 - 二字宽度 */\n.col-level { width: 8%; min-width: 120px; } /* 相关指标或方向列 - 分级展示用 */\n.col-criteria { width: 8%; min-width: 120px; } /* 工作准则列 - 缩小一点 */\n.col-target { width: 13%; min-width: 180px; } /* 2025年目标列 - 稍微缩小一点 */\n.col-measure { width: 15%; min-width: 200px; } /* 2025年举措列 - 详细内容显示 */\n.col-responsible { width: 8%; min-width: 100px; } /* 负责人列 - 再扩大一点点 */\n.col-weight { width: 4%; min-width: 50px; } /* 权重列 - 二字宽 */\n.col-remark { width: 8%; min-width: 120px; } /* 备注列 - 缩小一点点 */\n.col-month-plan, .col-month-complete { width: 9%; min-width: 140px; } /* 月度计划和完成情况列 - 增加宽度 */\n\n/* 可编辑单元格样式 */\n.editable-cell {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.editable-cell:hover {\n  background: rgba(77, 208, 255, 0.1);\n  border-color: #4dd0ff;\n}\n\n.cell-input {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid #4dd0ff;\n  border-radius: 4px;\n  color: #ffffff;\n  padding: 8px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  resize: vertical;\n  text-align: center;\n}\n\n.cell-input:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.cell-content {\n  display: block;\n  word-break: break-word;\n  white-space: pre-wrap;\n  min-height: 20px;\n  text-align: center;\n}\n\n/* 月份列样式 */\n.month-plan {\n  background: rgba(32, 255, 77, 0.1) !important;\n  border-color: rgba(32, 255, 77, 0.3) !important;\n}\n\n.month-complete {\n  background: rgba(77, 208, 255, 0.1) !important;\n  border-color: rgba(77, 208, 255, 0.3) !important;\n}\n\n/* 加载状态样式更新 */\n.world-class-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.world-class-loading .loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(0, 212, 170, 0.3);\n  border-top: 3px solid #00d4aa;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.world-class-loading p {\n  color: #00d4aa;\n  font-size: 1.1rem;\n  text-align: center;\n}\n\n/* 统计信息样式 */\n.stats-section {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 25px;\n  flex-wrap: wrap;\n  justify-content: center;\n  width: 100%;\n}\n\n.stat-card {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 15px;\n  padding: 20px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  min-width: 150px;\n  position: relative;\n  overflow: hidden;\n}\n\n.stat-card::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: conic-gradient(transparent, rgba(0, 212, 170, 0.1), transparent 30%);\n  animation: rotate 6s linear infinite;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.stat-card:hover::before {\n  opacity: 1;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  border-color: #00d4aa;\n  box-shadow: 0 10px 30px rgba(0, 212, 170, 0.2);\n}\n\n.stat-number {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.8rem;\n  color: #4dd0ff;\n  margin-bottom: 8px;\n  text-shadow: 0 0 15px rgba(77, 208, 255, 0.5);\n}\n\n.stat-text {\n  color: #ffffff;\n  font-size: 0.9rem;\n  opacity: 0.8;\n}\n\n/* 下载功能样式 */\n.download-controls {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  justify-content: center;\n}\n\n.download-btn {\n  background: linear-gradient(45deg, #ff6b6b, #ff8e53);\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  padding: 5px 10px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.download-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .filter-controls {\n    gap: 20px;\n  }\n  \n  .month-navigation-new {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  \n  .stats-section {\n    gap: 15px;\n  }\n  \n  .stat-card {\n    min-width: 120px;\n  }\n  \n  /* 在中等屏幕上调整列宽 */\n  .col-month-plan, .col-month-complete { width: 10%; min-width: 120px; }\n}\n\n@media (max-width: 768px) {\n  .world-class-container {\n    padding: 10px;\n  }\n  \n  .page-title {\n    font-size: 1.8rem;\n  }\n  \n  .page-header {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .header-center {\n    order: 1;\n  }\n  \n  .back-btn-top {\n    order: 2;\n    width: 100%;\n  }\n  \n  .sync-status {\n    order: 3;\n  }\n  \n  .stat-card {\n    flex: 1;\n    min-width: 100px;\n  }\n  \n  .stat-number {\n    font-size: 1.5rem;\n  }\n  \n  .nav-btn-new {\n    padding: 6px 12px;\n    font-size: 0.8rem;\n  }\n  \n  .current-months-new {\n    font-size: 1rem;\n  }\n  \n  .control-panel {\n    padding: 15px;\n  }\n  \n  .filter-controls {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .world-class-table {\n    font-size: 0.9rem;\n  }\n  \n  .world-class-table th,\n  .world-class-table td {\n    padding: 8px 5px;\n  }\n  \n  /* 移动端表格高度调整 */\n  .table-container {\n    max-height: calc(100vh - 280px);\n  }\n}\n\n/* 滚动条样式 */\n.table-container::-webkit-scrollbar {\n  height: 8px;\n  width: 8px;\n}\n\n.table-container::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n.table-container::-webkit-scrollbar-thumb {\n  background: rgba(0, 212, 170, 0.5);\n  border-radius: 4px;\n}\n\n.table-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 212, 170, 0.7);\n}\n\n/* 动画定义 */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes rotate {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 新的高科技感筛选按钮样式 */\n@keyframes pulse-glow {\n  0% {\n    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 15px rgba(0, 212, 170, 0.6), 0 0 25px rgba(0, 212, 170, 0.5);\n  }\n  100% {\n    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);\n  }\n}\n\n@keyframes rotate-border {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.responsible-filter-btn-new {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 55px;\n  height: 55px;\n  background: #1a1a2e;\n  border: 2px solid rgba(0, 212, 170, 0.5);\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  color: #00d4aa;\n  font-size: 22px;\n  animation: pulse-glow 4s infinite ease-in-out;\n}\n\n.responsible-filter-btn-new::before {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  border-top-color: #20ff4d;\n  transition: transform 1s linear;\n  animation: rotate-border 2s linear infinite;\n}\n\n.responsible-filter-btn-new:hover {\n  transform: scale(1.15);\n  border-color: rgba(32, 255, 77, 0.8);\n  background: rgba(32, 255, 77, 0.1);\n}\n\n.responsible-filter-btn-new:hover::before {\n  animation-duration: 1s;\n}\n\n.responsible-filter-btn-new .filter-icon {\n  transition: transform 0.3s ease;\n}\n\n.responsible-filter-btn-new:hover .filter-icon {\n  transform: rotate(15deg);\n}\n\n.responsible-filter-btn-new .filter-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a52);\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  font-size: 11px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  z-index: 3;\n  transform: translate(20%, -20%);\n}\n\n/* 空数据状态 */\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 1.1rem;\n  width: 100%;\n}\n\n/* 负责人筛选面板样式（复用模块二设计） */\n.world-class-responsible-filter-panel {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 320px;\n  max-height: 500px;\n  background: linear-gradient(135deg, rgba(0, 20, 40, 0.98), rgba(0, 40, 60, 0.98));\n  border: 2px solid rgba(0, 255, 204, 0.4);\n  border-radius: 15px;\n  backdrop-filter: blur(25px);\n  box-shadow: \n    0 15px 50px rgba(0, 0, 0, 0.4),\n    0 0 40px rgba(0, 255, 204, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  z-index: 2000;\n  overflow: hidden;\n  animation: fadeInScale 0.3s ease-out;\n}\n\n@keyframes fadeInScale {\n  0% {\n    opacity: 0;\n    transform: translate(-50%, -50%) scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n}\n\n.world-class-filter-panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  background: linear-gradient(90deg, rgba(0, 255, 204, 0.1), rgba(0, 184, 148, 0.1));\n  border-bottom: 1px solid rgba(0, 255, 204, 0.2);\n}\n\n.world-class-filter-panel-header h3 {\n  margin: 0;\n  color: #00ffcc;\n  font-size: 16px;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(0, 255, 204, 0.5);\n}\n\n.world-class-close-panel-btn {\n  background: none;\n  border: none;\n  color: #ff6b6b;\n  font-size: 24px;\n  cursor: pointer;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.world-class-close-panel-btn:hover {\n  background: rgba(255, 107, 107, 0.2);\n  transform: rotate(90deg);\n}\n\n.world-class-filter-panel-content {\n  display: flex;\n  flex-direction: column;\n  padding: 0 20px 20px 20px;\n  max-height: 400px;\n  overflow-y: hidden;\n}\n\n.world-class-filter-options {\n  flex: 1;\n  overflow-y: auto;\n  padding-top: 20px;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  gap: 6px;\n  margin-bottom: 15px;\n}\n\n.world-class-filter-option {\n  background: rgba(0, 255, 204, 0.1);\n  border: 1px solid rgba(0, 255, 204, 0.3);\n  color: #ffffff;\n  padding: 8px 12px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.95rem;\n  text-align: center;\n  flex: 0 0 calc(33.33% - 4px);\n  box-sizing: border-box;\n}\n\n.world-class-filter-option:hover {\n  background: rgba(0, 255, 204, 0.2);\n  border-color: rgba(0, 255, 204, 0.5);\n  box-shadow: 0 0 15px rgba(0, 255, 204, 0.3);\n}\n\n.world-class-filter-option.selected {\n  background: linear-gradient(135deg, rgba(0, 255, 204, 0.3), rgba(0, 184, 148, 0.3));\n  border-color: #00ffcc;\n  color: #00ffcc;\n  box-shadow: 0 0 20px rgba(0, 255, 204, 0.4);\n}\n\n.world-class-filter-actions {\n  flex-shrink: 0;\n  display: flex;\n  gap: 8px;\n}\n\n.world-class-clear-filter-btn, .world-class-apply-filter-btn {\n  flex: 1;\n  padding: 10px 16px;\n  border-radius: 8px;\n  border: none;\n  cursor: pointer;\n  font-size: 0.95rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.world-class-clear-filter-btn {\n  background: rgba(255, 107, 107, 0.2);\n  color: #ff6b6b;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.world-class-clear-filter-btn:hover {\n  background: rgba(255, 107, 107, 0.3);\n  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);\n}\n\n.world-class-apply-filter-btn {\n  background: linear-gradient(135deg, #00d4aa, #00b894);\n  color: white;\n  border: 1px solid rgba(0, 255, 204, 0.3);\n}\n\n.world-class-apply-filter-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);\n}\n\n/* 自定义滚动条 */\n.world-class-filter-panel-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-track {\n  background: rgba(0, 255, 204, 0.1);\n  border-radius: 3px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-thumb {\n  background: rgba(0, 255, 204, 0.5);\n  border-radius: 3px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 255, 204, 0.7);\n} ", "/* 模块三选择性下载模态框样式 - 复用模块一和模块二的高科技风格 */\r\n.world-class-download-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.world-class-download-modal {\r\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\r\n  border: 2px solid rgba(77, 208, 255, 0.3);\r\n  border-radius: 15px;\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.6),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  width: 90%;\r\n  max-width: 900px;\r\n  max-height: 85vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模态框头部 */\r\n.modal-header {\r\n  background: linear-gradient(90deg, rgba(77, 208, 255, 0.1), rgba(32, 255, 77, 0.1));\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #4dd0ff;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(77, 208, 255, 0.6);\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff4757;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 71, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 模态框内容 */\r\n.modal-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n/* 当前筛选条件显示 */\r\n.current-filters {\r\n  background: rgba(77, 208, 255, 0.1);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  color: #4dd0ff;\r\n  font-weight: 600;\r\n  min-width: 80px;\r\n}\r\n\r\n.filter-value {\r\n  color: #20ff4d;\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 4px 10px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.filter-item select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 6px 10px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.filter-item select:focus {\r\n  outline: none;\r\n  border-color: #4dd0ff;\r\n  box-shadow: 0 0 10px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.include-filter-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #e8e8e8;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.include-filter-checkbox input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 层级部分 */\r\n.levels-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.level-section {\r\n  border: 1px solid rgba(77, 208, 255, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(15, 15, 35, 0.5);\r\n}\r\n\r\n.level-header {\r\n  background: linear-gradient(90deg, rgba(77, 208, 255, 0.15), rgba(32, 255, 77, 0.15));\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.2);\r\n}\r\n\r\n.level-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.level-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n.level-title {\r\n  color: #4dd0ff;\r\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  background: linear-gradient(135deg, rgba(77, 208, 255, 0.2), rgba(32, 255, 77, 0.2));\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 6px 12px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, rgba(77, 208, 255, 0.3), rgba(32, 255, 77, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n/* 层级项目列表 */\r\n.level-items-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.level-item-row {\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.level-item-row:hover {\r\n  background: rgba(77, 208, 255, 0.05);\r\n}\r\n\r\n.level-item-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.level-item-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n.level-item-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  width: 100%;\r\n}\r\n\r\n.level-item-target {\r\n  color: #e8e8e8;\r\n  font-size: 0.95rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.level-item-responsible {\r\n  color: #20ff4d;\r\n  font-size: 0.85rem;\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  align-self: flex-start;\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(77, 208, 255, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.statistics {\r\n  display: flex;\r\n  gap: 15px;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.statistics span {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 8px 15px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  text-shadow: 0 0 5px rgba(32, 255, 77, 0.6);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.format-selection {\r\n  color: #e8e8e8;\r\n}\r\n\r\n.format-selection label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-selection select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.format-selection select:focus {\r\n  outline: none;\r\n  border-color: #4dd0ff;\r\n  box-shadow: 0 0 10px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.cancel-btn, .download-btn {\r\n  padding: 12px 25px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\r\n  color: #ff4757;\r\n  border: 1px solid rgba(255, 71, 87, 0.5);\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.download-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 20px rgba(77, 208, 255, 0.4);\r\n}\r\n\r\n.download-btn:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.modal-content::-webkit-scrollbar,\r\n.level-items-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-track,\r\n.level-items-list::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb,\r\n.level-items-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb:hover,\r\n.level-items-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .world-class-download-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .modal-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .modal-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .level-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modal-footer {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .statistics {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .format-selection {\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filter-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n} ", "@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');\r\n\r\n/* 模块四 - KPI跟踪仪表板样式 */\r\n\r\n.monthly-kpi {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  padding: 20px;\r\n  font-family: 'Rajdhani', 'Microsoft YaHei', sans-serif;\r\n  color: #e0e0e0;\r\n}\r\n\r\n/* 加载状态 */\r\n.kpi-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 80vh;\r\n  color: #fff;\r\n  text-shadow: 0 0 5px #ff4dff;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: 4px solid rgba(255, 77, 255, 0.3);\r\n  border-top: 4px solid #ff4dff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 头部区域 */\r\n.kpi-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background: rgba(15, 15, 35, 0.5);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 15px 25px;\r\n  border: 1px solid rgba(77, 200, 255, 0.3);\r\n  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.back-button {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\r\n  color: #fff;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  font-family: 'Orbitron', sans-serif;\r\n  text-shadow: 0 1px 3px rgba(0,0,0,0.4);\r\n}\r\n\r\n.back-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);\r\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff);\r\n}\r\n\r\n.monthly-kpi .page-title {\r\n  color: #fff;\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(255, 77, 255, 0.8);\r\n  font-family: 'Orbitron', sans-serif;\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  text-align: center;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.sync-status {\r\n  color: #e0e0e0;\r\n  font-size: 14px;\r\n}\r\n\r\n.status-indicator {\r\n  padding: 4px 10px;\r\n  border-radius: 12px;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.status-indicator.success {\r\n  color: #20ff4d;\r\n  background: rgba(32, 255, 77, 0.2);\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: block;\r\n  border-radius: 6px;\r\n  padding: 2px 10px;\r\n  margin-left: 6px;\r\n  border: 1px solid #20ff4d;\r\n  box-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.status-indicator.pending {\r\n  background: rgba(77, 200, 255, 0.2);\r\n  color: #FFE599;\r\n  border: 1px solid #4dc8ff;\r\n  text-shadow: 0 0 4px #FFE599;\r\n  box-shadow: 0 0 8px rgba(77, 200, 255, 0.3);\r\n}\r\n\r\n.refresh-button {\r\n  padding: 10px 20px;\r\n  height: 44px;\r\n  border: 1px solid rgba(77, 200, 255, 0.6);\r\n  background: linear-gradient(135deg, #4dc8ff, #8b5cf6);\r\n  color: #fff;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 14px;\r\n  backdrop-filter: blur(5px);\r\n  font-weight: 600;\r\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\r\n  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.download-button {\r\n  padding: 10px 20px;\r\n  height: 44px;\r\n  border: 1px solid rgba(77, 200, 255, 0.5);\r\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\r\n  color: #fff;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 14px;\r\n  backdrop-filter: blur(5px);\r\n  font-weight: 600;\r\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.refresh-button:hover {\r\n  background: linear-gradient(135deg, #8b5cf6, #4dc8ff);\r\n  border-color: #8b5cf6;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);\r\n}\r\n\r\n.download-button:hover {\r\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff);\r\n  border-color: #4dc8ff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 0 15px rgba(77, 200, 255, 0.4);\r\n}\r\n\r\n/* 月份导航区域 */\r\n.month-navigation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background: rgba(15, 15, 35, 0.7);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 32px;\r\n  padding: 0;\r\n  border: 1.5px solid rgba(255, 107, 77, 0.3);\r\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.08);\r\n  width: fit-content;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  min-width: 340px;\r\n}\r\n\r\n.nav-button {\r\n  padding: 0 20px;\r\n  height: 44px;\r\n  border: none;\r\n  background: linear-gradient(135deg, #4dc8ff 0%, #ff4dff 100%);\r\n  color: #fff;\r\n  border-radius: 32px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-size: 15px;\r\n  font-weight: 700;\r\n  margin: 0 8px;\r\n  box-shadow: 0 2px 8px rgba(255, 77, 255, 0.08);\r\n  font-family: 'Rajdhani', 'Orbitron', sans-serif;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.nav-button:disabled {\r\n  opacity: 0.4;\r\n  cursor: not-allowed;\r\n  background: linear-gradient(135deg, #3a3a5e 0%, #1a1a2e 100%);\r\n  color: #aaa;\r\n  border: none;\r\n}\r\n\r\n.nav-button:hover:not(:disabled) {\r\n  background: linear-gradient(135deg, #ff4dff 0%, #4dc8ff 100%);\r\n  transform: translateY(-1px) scale(1.04);\r\n  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.3);\r\n}\r\n\r\n.current-months {\r\n  color: #fff;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-shadow: 0 0 8px rgba(255, 107, 77, 0.7);\r\n  font-family: 'Orbitron', sans-serif;\r\n  margin: 0 16px;\r\n  letter-spacing: 1.5px;\r\n  min-width: 110px;\r\n  text-align: center;\r\n  background: transparent;\r\n  border-radius: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 48px;\r\n}\r\n\r\n/* 内容区域 */\r\n.kpi-content {\r\n  background: rgba(15, 15, 35, 0.6);\r\n  border-radius: 15px;\r\n  padding: 25px;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 77, 255, 0.1);\r\n}\r\n\r\n/* 表格容器 */\r\n.module4_kpi-table-container {\r\n  overflow-x: auto;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 200, 255, 0.2);\r\n  background: transparent;\r\n  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);\r\n}\r\n\r\n/* 表格样式 */\r\n.kpi-data-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  color: #e0e0e0;\r\n}\r\n\r\n.kpi-data-table thead {\r\n  background: linear-gradient(90deg, #4dc8ff, #ff4dff, #ff6b4d);\r\n  color: #fff;\r\n}\r\n\r\n.kpi-data-table th {\r\n  padding: 15px 10px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  border-right: 1px solid rgba(255, 255, 255, 0.3);\r\n  white-space: nowrap;\r\n  text-transform: uppercase;\r\n  color: #fff;\r\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\r\n}\r\n\r\n.kpi-data-table th:last-child {\r\n  border-right: none;\r\n}\r\n\r\n/* 表格行样式 */\r\n.kpi-data-row {\r\n  transition: all 0.2s ease;\r\n  border-bottom: 1px solid rgba(255, 77, 255, 0.1);\r\n}\r\n\r\n.kpi-data-row:nth-child(even) {\r\n  background-color: rgba(255, 255, 255, 0.03);\r\n}\r\n\r\n.kpi-data-row:hover {\r\n  background-color: rgba(255, 77, 255, 0.1);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 合并单元格样式 */\r\n.merged-row {\r\n  background-color: rgba(255, 255, 255, 0.05) !important;\r\n}\r\n\r\n.merged-start-row {\r\n  background-color: rgba(255, 107, 77, 0.1);\r\n}\r\n\r\n.merged-cell-content {\r\n  background-color: transparent !important;\r\n  font-weight: 600;\r\n  vertical-align: middle;\r\n  color: #ff6b4d;\r\n}\r\n\r\n/* 表格单元格 */\r\n.kpi-data-cell {\r\n  padding: 12px 8px;\r\n  border-right: 1px solid rgba(255, 77, 255, 0.1);\r\n  border-bottom: none; /* Handled by row */\r\n  vertical-align: middle;\r\n  text-align: center;\r\n}\r\n\r\n.kpi-data-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n/* 列宽控制 */\r\n.col-number { width: 60px; }\r\n.col-indicator { width: 180px; text-align: left; color: #fff; }\r\n.col-target { width: 250px; text-align: left; }\r\n.col-score { width: 80px; color: #ff6b4d; font-weight: 600; }\r\n.col-method { width: 150px; }\r\n.col-standard { width: 200px; text-align: left; }\r\n.col-month { width: 80px; color: #ff4dff; font-weight: 600; }\r\n.col-completion { width: 180px; }\r\n.col-score-month { width: 80px; color: #ff6b4d; font-weight: 600; }\r\n\r\n/* 可编辑单元格 */\r\n.editable {\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.editable:hover .cell-content {\r\n  background-color: rgba(255, 107, 77, 0.2);\r\n}\r\n\r\n.cell-content {\r\n  min-height: 20px;\r\n  padding: 4px;\r\n  border-radius: 4px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.cell-content.clickable:hover {\r\n  background-color: rgba(255, 77, 255, 0.1);\r\n  border: 1px dashed #ff4dff;\r\n}\r\n\r\n/* 只读单元格样式 */\r\n.cell-content.readonly-cell {\r\n  background: rgba(128, 128, 128, 0.1) !important;\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n  cursor: not-allowed !important;\r\n  border: 1px solid rgba(128, 128, 128, 0.3) !important;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cell-content.readonly-cell:hover {\r\n  background: rgba(128, 128, 128, 0.15) !important;\r\n  border: 1px solid rgba(128, 128, 128, 0.4) !important;\r\n  transform: none !important;\r\n}\r\n\r\n/* 只读列的表头样式 */\r\n.col-completion.readonly-header {\r\n  background: rgba(128, 128, 128, 0.2) !important;\r\n  color: rgba(255, 255, 255, 0.8) !important;\r\n}\r\n\r\n/* 单元格编辑器 */\r\n.cell-editor {\r\n  width: 100%;\r\n  min-height: 60px;\r\n  border: 2px solid #ff4dff;\r\n  border-radius: 4px;\r\n  padding: 6px;\r\n  font-size: 13px;\r\n  resize: vertical;\r\n  outline: none;\r\n  background: #1a1a2e;\r\n  color: #e0e0e0;\r\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.3);\r\n}\r\n\r\n.cell-editor:focus {\r\n  border-color: #ff6b4d;\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #a0a0a0;\r\n}\r\n\r\n.no-data p {\r\n  font-size: 16px;\r\n  margin: 10px 0;\r\n  text-shadow: 0 0 3px rgba(255, 77, 255, 0.5);\r\n}\r\n\r\n/* 底部统计 */\r\n.kpi-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  background: rgba(15, 15, 35, 0.4);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 12px;\r\n  padding: 15px 20px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.data-stats {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .kpi-data-table {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .col-indicator,\r\n  .col-target,\r\n  .col-standard {\r\n    min-width: 150px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .monthly-kpi {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .kpi-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n    padding: 15px;\r\n  }\r\n  \r\n  .header-left,\r\n  .header-right {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .month-navigation {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    min-width: 0;\r\n    width: 100%;\r\n    border-radius: 20px;\r\n    padding: 6px 0;\r\n  }\r\n  .nav-button, .current-months {\r\n    height: 40px;\r\n    font-size: 15px;\r\n    min-width: 80px;\r\n    margin: 0 4px;\r\n  }\r\n  .current-months {\r\n    font-size: 16px;\r\n    min-width: 80px;\r\n    margin: 0 8px;\r\n  }\r\n  \r\n  .kpi-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .kpi-data-table {\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .kpi-data-cell {\r\n    padding: 8px 4px;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.kpi-content, .kpi-header, .month-navigation, .kpi-footer {\r\n  animation: fadeIn 0.6s ease-out;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.module4_kpi-table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n  width: 8px;\r\n}\r\n\r\n.module4_kpi-table-container::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module4_kpi-table-container::-webkit-scrollbar-thumb {\r\n  background: #ff4dff;\r\n  border-radius: 4px;\r\n  border: 1px solid #ff6b4d;\r\n}\r\n\r\n.module4_kpi-table-container::-webkit-scrollbar-thumb:hover {\r\n  background: #ff6b4d;\r\n}\r\n\r\n/* 控制面板样式 */\r\n.control-panel {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: rgba(15, 15, 35, 0.6);\r\n  border-radius: 15px;\r\n  padding: 20px 25px;\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(77, 200, 255, 0.2);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.control-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.control-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n/* 指标类型选择区域 */\r\n.indicator-type-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.filter-label {\r\n  color: #a78bfa;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 5px rgba(167, 139, 250, 0.3);\r\n  min-width: 70px;\r\n}\r\n\r\n.indicator-selector-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.indicator-type-selector {\r\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.9));\r\n  border: 1px solid rgba(77, 200, 255, 0.3);\r\n  border-radius: 8px;\r\n  color: #4dc8ff;\r\n  padding: 8px 16px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  min-width: 120px;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 2px 10px rgba(77, 200, 255, 0.1);\r\n}\r\n\r\n.indicator-type-selector:hover {\r\n  border-color: rgba(77, 200, 255, 0.6);\r\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.indicator-type-selector:focus {\r\n  outline: none;\r\n  border-color: #4dc8ff;\r\n  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);\r\n}\r\n\r\n.indicator-type-selector option {\r\n  background: #1a1a2e;\r\n  color: #4dc8ff;\r\n  padding: 8px;\r\n}\r\n\r\n\r\n\r\n/* 当前显示统计 */\r\n.current-display-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 16px;\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.stats-label {\r\n  color: #00d4aa;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.stats-value {\r\n  color: #20ff4d;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 操作按钮组 */\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  gap: 15px;\r\n  height: 44px;\r\n}\r\n\r\n\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .control-panel {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .control-left,\r\n  .control-right {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .action-buttons {\r\n    justify-content: center;\r\n    width: 100%;\r\n    gap: 12px;\r\n  }\r\n\r\n  .indicator-filter-panel {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n\r\n  .filter-header,\r\n  .filter-content,\r\n  .filter-footer {\r\n    padding: 15px 20px;\r\n  }\r\n}", "@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');\n\n/* KPI选择性下载模态框样式 */\n\n.kpi-selective-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(10px);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.kpi-selective-modal {\n  background: linear-gradient(180deg, #16213e, #0f0f23);\n  color: #e0e0e0;\n  font-family: 'Rajdhani', sans-serif;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 900px;\n  max-height: 90vh;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 0 40px rgba(77, 200, 255, 0.4), 0 0 20px rgba(255, 77, 255, 0.2);\n  animation: slideUp 0.3s ease-out;\n  border: 1px solid rgba(77, 200, 255, 0.3);\n}\n\n/* 头部 */\n.modal-header {\n  background: linear-gradient(90deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  padding: 20px 25px;\n  border-radius: 16px 16px 0 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 700;\n  font-family: 'Orbitron', sans-serif;\n  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\n}\n\n.close-button {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: rotate(90deg);\n}\n\n/* 内容区域 */\n.modal-content {\n  padding: 25px;\n  overflow-y: auto;\n  flex: 1;\n}\n\n/* 统计信息区域 */\n.statistics-section {\n  margin-bottom: 25px;\n  padding: 20px;\n  background: linear-gradient(135deg, rgba(77, 200, 255, 0.1), rgba(255, 77, 255, 0.1));\n  border-radius: 12px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 15px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 8px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n}\n\n.stat-label {\n  display: block;\n  font-size: 14px;\n  color: #b0b0b0;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  display: block;\n  font-size: 24px;\n  font-weight: 700;\n  color: #4dc8ff;\n  font-family: 'Orbitron', sans-serif;\n  text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);\n}\n\n.stat-value.selected {\n  color: #ff4dff;\n  text-shadow: 0 0 10px rgba(255, 77, 255, 0.5);\n}\n\n/* 选择区域 */\n.selection-section {\n  margin-bottom: 25px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.selection-section h4 {\n  margin: 0 0 15px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #4dc8ff;\n  text-shadow: 0 0 5px rgba(77, 200, 255, 0.3);\n}\n\n.select-all-button {\n  padding: 8px 16px;\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  border: none;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n.select-all-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);\n}\n\n.select-all-button.selected {\n  background: linear-gradient(135deg, #ff4dff, #ff6b4d);\n}\n\n/* 指标列表 */\n.indicators-list {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 15px;\n  cursor: pointer;\n  border-bottom: 1px solid rgba(77, 200, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.checkbox-item:last-child {\n  border-bottom: none;\n}\n\n.checkbox-item:hover {\n  background: rgba(77, 200, 255, 0.1);\n}\n\n.checkbox-item input[type=\"checkbox\"] {\n  margin-right: 12px;\n  margin-top: 2px;\n  transform: scale(1.2);\n  accent-color: #4dc8ff;\n}\n\n.indicator-item .indicator-info {\n  flex: 1;\n}\n\n.indicator-main {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 5px;\n}\n\n.indicator-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #fff;\n  flex: 1;\n}\n\n.indicator-score {\n  font-size: 14px;\n  color: #ff4dff;\n  font-weight: 600;\n  background: rgba(255, 77, 255, 0.1);\n  padding: 2px 8px;\n  border-radius: 4px;\n}\n\n.indicator-details {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n.indicator-target,\n.indicator-method {\n  font-size: 13px;\n  color: #b0b0b0;\n}\n\n.indicator-target {\n  color: #4dc8ff;\n}\n\n/* 格式选择 */\n.format-options {\n  display: flex;\n  gap: 20px;\n}\n\n.radio-item {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 10px 15px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.radio-item:hover {\n  background: rgba(77, 200, 255, 0.1);\n  border-color: rgba(77, 200, 255, 0.4);\n}\n\n.radio-item input[type=\"radio\"] {\n  margin-right: 8px;\n  accent-color: #4dc8ff;\n}\n\n/* 信息区域 */\n.info-section {\n  margin-top: 20px;\n}\n\n.info-box {\n  background: rgba(77, 200, 255, 0.05);\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.info-box h5 {\n  margin: 0 0 10px 0;\n  color: #4dc8ff;\n  font-size: 16px;\n}\n\n.info-box ul {\n  margin: 0;\n  padding-left: 20px;\n  color: #b0b0b0;\n}\n\n.info-box li {\n  margin-bottom: 5px;\n  font-size: 14px;\n}\n\n/* 底部按钮 */\n.modal-footer {\n  padding: 20px 25px;\n  border-top: 1px solid rgba(77, 200, 255, 0.2);\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 0 0 16px 16px;\n}\n\n.cancel-button,\n.download-button {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n.cancel-button {\n  background: rgba(128, 128, 128, 0.3);\n  color: #e0e0e0;\n  border: 1px solid rgba(128, 128, 128, 0.5);\n}\n\n.cancel-button:hover {\n  background: rgba(128, 128, 128, 0.5);\n}\n\n.download-button {\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  min-width: 150px;\n}\n\n.download-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(77, 200, 255, 0.4);\n}\n\n.download-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 动画 */\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(50px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .kpi-selective-modal {\n    width: 95%;\n    max-height: 95vh;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .format-options {\n    flex-direction: column;\n    gap: 10px;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", "/* ProjectOne.css - 1号项目责任状页面样式 */\n\n.project-one-container {\n  min-height: 100vh;\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n  padding: 15px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%;\n}\n\n/* 页面头部样式 - 复用模块三设计 */\n.project-one-container .page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20px;\n  position: relative;\n  padding: 10px 0;\n  width: 100%;\n}\n\n.project-one-container .header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.project-one-container .header-center {\n  text-align: center;\n  flex: 1;\n}\n\n.project-one-container .back-btn-fixed {\n  position: fixed;\n  top: 20px;\n  left: 20px;\n  z-index: 1000;\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd);\n  color: #1a1a2e;\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  font-weight: 600;\n  font-size: 1.1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.project-one-container .back-btn-fixed:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(167, 139, 250, 0.4);\n}\n\n/* 返回首页按钮样式 - 紫蓝色主题 */\n.project-one-container .back-btn-top {\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd);\n  color: #1a1a2e;\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1.1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.project-one-container .back-btn-top:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(167, 139, 250, 0.4);\n}\n\n.project-one-container .page-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 2.2rem;\n  color: #a78bfa;\n  margin-bottom: 8px;\n  text-shadow: 0 0 20px rgba(167, 139, 250, 0.5);\n}\n\n.project-one-container .page-subtitle {\n  font-size: 1rem;\n  color: #c4b5fd;\n  margin-bottom: 0;\n  opacity: 0.8;\n}\n\n.project-one-container .sync-status {\n  display: flex;\n  align-items: center;\n}\n\n.project-one-container .status-indicator {\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.95rem;\n  font-weight: 600;\n}\n\n.project-one-container .status-indicator.success {\n  background: rgba(32, 255, 77, 0.1);\n  color: #20ff4d;\n  border: 1px solid rgba(32, 255, 77, 0.3);\n}\n\n.project-one-container .status-indicator.error {\n  background: rgba(255, 87, 87, 0.1);\n  color: #ff5757;\n  border: 1px solid rgba(255, 87, 87, 0.3);\n}\n\n.project-one-container .status-indicator.pending {\n  background: rgba(255, 193, 7, 0.1);\n  color: #ffc107;\n  border: 1px solid rgba(255, 193, 7, 0.3);\n  font-size: 1rem;\n  padding: 4px 12px;\n  border-radius: 12px;\n}\n\n.project-one-container .refresh-btn-new {\n  background: linear-gradient(45deg, #c4b5fd, #a78bfa);\n  color: #1a1a2e;\n  border: none;\n  border-radius: 15px;\n  padding: 10px 20px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);\n  order: -1;\n}\n\n.project-one-container .refresh-btn-new:hover {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(167, 139, 250, 0.4);\n}\n\n/* 控制面板样式 */\n.project-one-container .control-panel {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(167, 139, 250, 0.3);\n  border-radius: 15px;\n  padding: 15px 20px;\n  margin-bottom: 25px;\n  backdrop-filter: blur(10px);\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n/* 左侧控件容器 */\n.project-one-container .controls-left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n.project-one-container .filter-controls {\n  display: flex;\n  align-items: center;\n  gap: 30px;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n\n.project-one-container .filter-group {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.project-one-container .filter-group label {\n  color: #a78bfa;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.project-one-container .type-selector {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  padding: 8px 15px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  width: 250px;\n}\n\n.project-one-container .type-selector:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.project-one-container .type-selector option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #00d4aa;\n  padding: 8px 15px;\n  font-weight: 600;\n  border: none;\n}\n\n.project-one-container .type-selector option:hover {\n  background: rgba(0, 212, 170, 0.2);\n  color: #ffffff;\n}\n\n/* 统计信息内联显示 */\n.project-one-container .stat-item-inline {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n}\n\n.project-one-container .stat-label {\n  color: #a78bfa;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.project-one-container .stat-value {\n  color: #20ff4d;\n  font-weight: 700;\n  font-size: 1rem;\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\n}\n\n/* 负责人筛选按钮 */\n.project-one-container .responsible-filter-btn-new {\n  position: relative;\n  background: rgba(0, 212, 170, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1.4rem;\n  margin-left: 30px;\n}\n\n.project-one-container .responsible-filter-btn-new:hover {\n  background: rgba(0, 212, 170, 0.2);\n  transform: scale(1.1);\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);\n}\n\n.project-one-container .filter-icon {\n  font-size: 1.4rem;\n}\n\n.project-one-container .filter-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background: linear-gradient(45deg, #ff4757, #ff6b7a);\n  color: #fff;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.7rem;\n  font-weight: 700;\n  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n}\n\n/* 右侧按钮容器 */\n.project-one-container .action-buttons {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  margin-left: -60px;\n}\n\n/* 月份导航样式 - 参考模块三设计但保持独立 */\n.project-one-container .month-navigation-new {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 18px;\n  background: rgba(0, 212, 170, 0.08);\n  padding: 12px 20px;\n  border-radius: 24px;\n  border: 1px solid rgba(0, 212, 170, 0.4);\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\n  width: fit-content;\n}\n\n.project-one-container .nav-btn-new {\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd);\n  color: #1a1a2e;\n  border: none;\n  border-radius: 18px;\n  padding: 8px 16px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);\n}\n\n.project-one-container .nav-btn-new:hover:not(:disabled) {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(167, 139, 250, 0.4);\n  background: linear-gradient(45deg, #c4b5fd, #a78bfa);\n}\n\n.project-one-container .nav-btn-new:disabled {\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.3);\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.project-one-container .current-months-new {\n  color: #a78bfa;\n  font-weight: 700;\n  font-size: 1.1rem;\n  min-width: 90px;\n  text-align: center;\n  text-shadow: 0 0 8px rgba(167, 139, 250, 0.3);\n  font-family: 'Orbitron', monospace;\n}\n\n/* 下载按钮 */\n.project-one-container .download-btn {\n  background: linear-gradient(45deg, #8b5cf6, #a78bfa);\n  color: #ffffff;\n  border: none;\n  border-radius: 15px;\n  padding: 10px 20px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);\n}\n\n.project-one-container .download-btn:hover {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);\n}\n\n/* 负责人筛选面板样式 */\n.project-one-responsible-filter-panel {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 20px;\n  padding: 0;\n  z-index: 1000;\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n}\n\n.project-one-filter-panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 25px;\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\n}\n\n.project-one-filter-panel-header h3 {\n  margin: 0;\n  color: #00d4aa;\n  font-family: 'Orbitron', monospace;\n  font-size: 1.3rem;\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\n}\n\n.project-one-close-panel-btn {\n  background: none;\n  border: none;\n  color: #ff5757;\n  font-size: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n}\n\n.project-one-close-panel-btn:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: scale(1.1);\n}\n\n.project-one-filter-panel-content {\n  padding: 25px;\n}\n\n.project-one-filter-options {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 12px;\n  margin-bottom: 25px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.project-one-filter-option {\n  padding: 12px 16px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n  font-weight: 500;\n  color: #ffffff;\n}\n\n.project-one-filter-option:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: #00d4aa;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);\n}\n\n.project-one-filter-option.selected {\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.3));\n  border-color: #20ff4d;\n  color: #20ff4d;\n  font-weight: 700;\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.5);\n}\n\n.project-one-filter-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n}\n\n.project-one-clear-filter-btn,\n.project-one-apply-filter-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 8px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.project-one-clear-filter-btn {\n  background: rgba(255, 87, 87, 0.1);\n  color: #ff5757;\n  border: 1px solid rgba(255, 87, 87, 0.5);\n}\n\n.project-one-clear-filter-btn:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: translateY(-2px);\n}\n\n.project-one-apply-filter-btn {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: 1px solid transparent;\n}\n\n.project-one-apply-filter-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n/* 表格容器样式 - 参考模块三设计，支持滚动功能 */\n.project-one-container .table-container {\n  overflow-x: auto;\n  overflow-y: auto;\n  border-radius: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(167, 139, 250, 0.3);\n  width: 100%;\n  margin: 0 auto;\n  max-height: calc(100vh - 200px);\n  backdrop-filter: blur(10px);\n}\n\n.project-one-container .project-one-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.9rem;\n  /* 移除min-width限制，允许表格完全适配容器 */\n}\n\n/* 表头固定样式 - 参考模块三设计，强化固定效果 */\n.project-one-container .project-one-table thead {\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n}\n\n.project-one-container .project-one-table th {\n  background: linear-gradient(135deg, #2a1a4a 0%, #1e1e3f 50%, #0f0f2a 100%);\n  color: #a78bfa;\n  padding: 15px 10px;\n  text-align: center;\n  border: 1px solid rgba(167, 139, 250, 0.3);\n  font-weight: 700;\n  font-size: 1rem;\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);\n  cursor: default;\n  user-select: none;\n}\n\n.project-one-container .project-one-table th:last-child {\n  border-right: none;\n}\n\n.project-one-container .project-one-table tbody tr {\n  /* 移除底部分隔线，创造更简洁的视觉效果 */\n  border-bottom: none;\n  transition: all 0.3s ease;\n}\n\n.project-one-container .project-one-table tbody tr:hover {\n  /* 保持轻微的悬停效果 */\n  background: rgba(0, 212, 170, 0.08);\n}\n\n.project-one-container .data-cell {\n  padding: 12px;\n  /* 移除边框，创造更简洁的视觉效果 */\n  border-right: none;\n  vertical-align: middle; /* 垂直居中对齐 */\n  position: relative;\n}\n\n.project-one-container .data-cell:last-child {\n  border-right: none;\n}\n\n/* 优化后的列宽设置 - 适配1366px屏幕无滚动 */\n.project-one-container .col-number { width: 40px; min-width: 40px; }\n.project-one-container .col-problem { width: 124px; min-width: 112px; } /* 第二列再减少一个字符宽度 */\n.project-one-container .col-type { width: 96px; min-width: 78px; } /* 第三列再增加一个字符宽度 */\n.project-one-container .col-target { width: 180px; min-width: 160px; }\n.project-one-container .col-deadline { width: 65px; min-width: 55px; }\n.project-one-container .col-form { width: 130px; min-width: 110px; }\n.project-one-container .col-responsible { width: 90px; min-width: 90px; } /* 保持三个字正常显示 */\n.project-one-container .col-month-plan { width: 140px; min-width: 120px; }\n.project-one-container .col-month-complete { width: 140px; min-width: 120px; }\n\n/* 可编辑单元格样式 - 优化多行文本显示 */\n.project-one-container .editable-cell {\n  min-height: 40px;\n  padding: 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: block;\n  width: 100%;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  background: transparent;\n  border: 2px solid transparent;\n  box-sizing: border-box;\n}\n\n.project-one-container .editable-cell:hover:not(.readonly) {\n  /* 悬停时显示柔和的虚线边框 - 只在最外层显示 */\n  border: 2px dashed rgba(167, 139, 250, 0.4);\n  background: transparent;\n  transform: scale(1.02);\n}\n\n.editable-cell:focus:not(.readonly),\n.project-one-container .editable-cell.editing {\n  /* 编辑时显示实线边框 - 只在最外层显示 */\n  border: 2px solid rgba(167, 139, 250, 0.6);\n  background: transparent;\n  box-shadow: 0 0 15px rgba(167, 139, 250, 0.2);\n}\n\n.project-one-container .editable-cell.readonly {\n  /* 只读单元格保持完全透明 */\n  background: transparent;\n  cursor: not-allowed;\n  opacity: 0.7;\n  border: 2px solid transparent;\n}\n\n.project-one-container .editable-cell.empty {\n  color: rgba(255, 255, 255, 0.5);\n  font-style: italic;\n  background: transparent !important;\n}\n\n/* 只对可编辑的空单元格显示占位符，只读单元格保持完全空白 */\n.project-one-container .editable-cell.empty:not(.readonly):before {\n  content: '点击编辑...';\n}\n\n.project-one-container .editable-cell.has-content {\n  color: #ffffff;\n  background: transparent !important;\n}\n\n.project-one-container .editable-cell.month-plan {\n  /* 月份计划单元格完全透明 */\n  background: transparent !important;\n}\n\n.project-one-container .editable-cell.month-complete {\n  /* 月份完成单元格完全透明 */\n  background: transparent !important;\n}\n\n/* 确保多行文本内部元素无背景色和边框 */\n.project-one-container .editable-cell * {\n  background: transparent !important;\n  border: none !important;\n  box-shadow: none !important;\n}\n\n/* 确保换行符不产生额外的样式 */\n.project-one-container .editable-cell br {\n  line-height: 1.4;\n  margin: 0;\n  padding: 0;\n}\n\n/* 单元格编辑器 */\n.project-one-container .cell-editor {\n  width: 100%;\n  min-height: 60px;\n  padding: 8px;\n  background: rgba(42, 26, 74, 0.95);\n  border: 2px solid #a78bfa;\n  border-radius: 6px;\n  color: #ffffff;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: 1.4;\n  resize: vertical;\n  outline: none;\n  backdrop-filter: blur(10px);\n}\n\n.project-one-container .cell-editor:focus {\n  border-color: #c4b5fd;\n  box-shadow: 0 0 15px rgba(167, 139, 250, 0.4);\n}\n\n/* 无数据提示 */\n.project-one-container .no-data {\n  text-align: center;\n  padding: 40px;\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 1.1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  margin: 20px 0;\n}\n\n/* 加载状态 */\n.project-one-container .loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  gap: 20px;\n}\n\n.project-one-container .loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(0, 212, 170, 0.3);\n  border-top: 3px solid #00d4aa;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.project-one-container .loading-text {\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 加载状态样式 */\n.project-one-container .project-one-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  gap: 20px;\n}\n\n.project-one-container .loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(0, 212, 170, 0.3);\n  border-top: 3px solid #00d4aa;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.project-one-container .project-one-loading p {\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 响应式设计 - 优化后无需水平滚动 */\n@media (max-width: 1366px) {\n  /* 针对1366px及以下屏幕优化 */\n  .project-one-container .project-one-table {\n    font-size: 0.85rem;\n  }\n\n  .project-one-table th,\n  .project-one-container .data-cell {\n    padding: 10px 8px;\n  }\n}\n\n@media (max-width: 1200px) {\n  /* 进一步压缩列宽 */\n  .project-one-container .col-number { width: 45px; min-width: 45px; }\n  .project-one-container .col-problem { width: 144px; min-width: 132px; } /* 第二列再减少一个字符宽度 */\n  .project-one-container .col-type { width: 106px; min-width: 88px; } /* 第三列再增加一个字符宽度 */\n  .project-one-container .col-target { width: 200px; min-width: 180px; }\n  .project-one-container .col-deadline { width: 70px; min-width: 60px; }\n  .project-one-container .col-form { width: 140px; min-width: 120px; }\n  .project-one-container .col-responsible { width: 85px; min-width: 85px; }\n  .project-one-container .col-month-plan { width: 150px; min-width: 130px; }\n  .project-one-container .col-month-complete { width: 150px; min-width: 130px; }\n\n  .project-one-container .control-panel {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .project-one-container .controls-left {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .project-one-container .action-buttons {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n@media (max-width: 768px) {\n  .project-one-container {\n    padding: 10px;\n  }\n\n  .page-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n\n  .header-center {\n    order: 1;\n  }\n\n  .project-one-container .back-btn-top {\n    order: 0;\n    align-self: flex-start;\n  }\n\n  .header-actions {\n    order: 2;\n    justify-content: center;\n  }\n\n  .project-one-container .filter-controls {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n\n  .project-one-container .filter-group {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 8px;\n  }\n\n  .project-one-container .month-navigation-new {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .project-one-responsible-filter-panel {\n    width: 95%;\n    margin: 10px;\n  }\n\n  .project-one-filter-options {\n    grid-template-columns: 1fr;\n  }\n\n  .project-one-filter-actions {\n    flex-direction: column;\n  }\n\n  /* 移动端表格优化 */\n  .project-one-container .project-one-table {\n    font-size: 0.75rem;\n  }\n\n  .project-one-table th,\n  .project-one-container .data-cell {\n    padding: 6px 4px;\n  }\n\n  /* 移动端列宽进一步优化 */\n  .project-one-container .col-number { width: 35px; min-width: 35px; }\n  .project-one-container .col-problem { width: 104px; min-width: 92px; } /* 第二列再减少一个字符宽度 */\n  .project-one-container .col-type { width: 86px; min-width: 68px; } /* 第三列再增加一个字符宽度 */\n  .project-one-container .col-target { width: 160px; min-width: 140px; }\n  .project-one-container .col-deadline { width: 60px; min-width: 50px; }\n  .project-one-container .col-form { width: 120px; min-width: 100px; }\n  .project-one-container .col-responsible { width: 70px; min-width: 70px; }\n  .project-one-container .col-month-plan { width: 120px; min-width: 100px; }\n  .project-one-container .col-month-complete { width: 120px; min-width: 100px; }\n}\n\n/* 滚动条样式 - 紫蓝色主题 */\n.project-one-container .table-container::-webkit-scrollbar {\n  height: 8px;\n  width: 8px;\n}\n\n.project-one-container .table-container::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n.project-one-container .table-container::-webkit-scrollbar-thumb {\n  background: rgba(167, 139, 250, 0.5);\n  border-radius: 4px;\n}\n\n.project-one-container .table-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(167, 139, 250, 0.7);\n}\n\n/* 移动端表格高度调整 */\n@media (max-width: 768px) {\n  .project-one-container .table-container {\n    max-height: calc(100vh - 280px);\n  }\n}\n\n/* 图标样式 */\n.icon-back::before { content: '←'; }\n.icon-refresh::before { content: '↻'; }\n.icon-download::before { content: '↓'; }\n.icon-arrow.up::before { content: '▲'; }\n.icon-arrow.down::before { content: '▼'; }\n.icon-prev::before { content: '‹'; }\n.icon-next::before { content: '›'; }\n", "/* ProjectOneDownloadModal.css - 1号项目责任状下载模态框样式 */\n\n.download-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  backdrop-filter: blur(5px);\n}\n\n.download-modal {\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 20px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  backdrop-filter: blur(20px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n}\n\n/* 模态框头部 */\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-family: 'Orbitron', monospace;\n  font-size: 1.5rem;\n  color: #00d4aa;\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\n}\n\n.close-button {\n  background: none;\n  border: none;\n  color: #ff5757;\n  font-size: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n}\n\n.close-button:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: scale(1.1);\n}\n\n/* 模态框内容 */\n.modal-content {\n  padding: 30px;\n  color: #ffffff;\n}\n\n/* 选择区域 */\n.selection-section {\n  margin-bottom: 30px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.select-all-label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  color: #20ff4d;\n  font-weight: 600;\n}\n\n.select-all-label input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.items-list {\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid rgba(0, 212, 170, 0.2);\n  border-radius: 10px;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.item-checkbox {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n  padding: 10px 0;\n  cursor: pointer;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.item-checkbox:last-child {\n  border-bottom: none;\n}\n\n.item-checkbox:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 5px;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n\n.item-checkbox input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.1);\n  margin-top: 2px;\n}\n\n.item-info {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n  flex: 1;\n}\n\n.item-number {\n  color: #00d4aa;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.item-title {\n  color: #ffffff;\n  line-height: 1.4;\n  font-weight: 500;\n}\n\n.item-responsible {\n  color: #20ff4d;\n  font-size: 0.9rem;\n  font-style: italic;\n}\n\n/* 格式选择 */\n.format-section {\n  margin-bottom: 30px;\n}\n\n.format-section h4 {\n  margin: 0 0 15px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.format-options {\n  display: flex;\n  gap: 20px;\n}\n\n.format-option {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  padding: 10px 15px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.format-option:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: #00d4aa;\n}\n\n.format-option input[type=\"radio\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.format-option span {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 月份范围选择 */\n.month-range-section {\n  margin-bottom: 30px;\n}\n\n.month-range-section h4 {\n  margin: 0 0 15px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.month-range-controls {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 15px;\n}\n\n.range-input {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.range-input label {\n  color: #20ff4d;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.range-input select {\n  background: rgba(26, 26, 46, 0.8);\n  border: 1px solid #00d4aa;\n  border-radius: 8px;\n  padding: 10px 15px;\n  color: #ffffff;\n  font-family: inherit;\n  font-size: 1rem;\n  cursor: pointer;\n  outline: none;\n  transition: all 0.3s ease;\n}\n\n.range-input select:focus {\n  border-color: #20ff4d;\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.3);\n}\n\n.range-input select option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #ffffff;\n}\n\n.range-preview {\n  color: #20ff4d;\n  font-weight: 600;\n  padding: 10px 15px;\n  background: rgba(32, 255, 77, 0.1);\n  border: 1px solid rgba(32, 255, 77, 0.3);\n  border-radius: 8px;\n}\n\n/* 其他选项 */\n.options-section {\n  margin-bottom: 30px;\n}\n\n.options-section h4 {\n  margin: 0 0 15px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.option-checkbox {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  cursor: pointer;\n  padding: 10px 15px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.option-checkbox:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: #00d4aa;\n}\n\n.option-checkbox input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.option-checkbox span {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 预览信息 */\n.preview-section {\n  margin-bottom: 20px;\n}\n\n.preview-section h4 {\n  margin: 0 0 15px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.preview-info {\n  background: rgba(0, 212, 170, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 10px;\n  padding: 20px;\n}\n\n.preview-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.preview-item:last-child {\n  border-bottom: none;\n}\n\n.preview-label {\n  color: #20ff4d;\n  font-weight: 600;\n}\n\n.preview-value {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 模态框底部 */\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding: 25px 30px;\n  border-top: 1px solid rgba(0, 212, 170, 0.2);\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.cancel-button {\n  background: rgba(255, 87, 87, 0.1);\n  border: 1px solid #ff5757;\n  border-radius: 8px;\n  padding: 12px 24px;\n  color: #ff5757;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.cancel-button:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: translateY(-2px);\n}\n\n.download-button {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  color: #000000;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.download-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n.download-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .download-modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .modal-header {\n    padding: 20px;\n  }\n  \n  .modal-content {\n    padding: 20px;\n  }\n  \n  .modal-footer {\n    padding: 20px;\n    flex-direction: column;\n  }\n  \n  .format-options {\n    flex-direction: column;\n  }\n  \n  .month-range-controls {\n    flex-direction: column;\n  }\n}\n", "/* 模块六主页面样式 - 高科技风格 + 金黄色主题 */\n.module6_module-six {\n  min-height: 100vh;\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  position: relative;\n  overflow-x: auto;\n}\n\n/* 背景动画 */\n.module6_module-six-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 0;\n}\n\n.module6_module-six-background .module6_particle {\n  position: absolute;\n  width: 2px;\n  height: 2px;\n  background: #ffd700;\n  border-radius: 50%;\n  animation: module6_float 6s ease-in-out infinite;\n}\n\n.module6_module-six-background .module6_particle:nth-child(1) {\n  top: 20%;\n  left: 10%;\n  animation-delay: 0s;\n}\n\n.module6_module-six-background .module6_particle:nth-child(2) {\n  top: 60%;\n  left: 80%;\n  animation-delay: 2s;\n}\n\n.module6_module-six-background .module6_particle:nth-child(3) {\n  top: 80%;\n  left: 30%;\n  animation-delay: 4s;\n}\n\n.module6_module-six-background .module6_grid-lines {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: \n    linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  animation: module6_gridMove 20s linear infinite;\n}\n\n/* 顶部导航栏 */\n.module6_module-six-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px 30px;\n  background: rgba(0, 0, 0, 0.6);\n  border-bottom: 2px solid rgba(255, 215, 0, 0.5);\n  backdrop-filter: blur(15px);\n  position: relative;\n  z-index: 10;\n}\n\n.module6_header-left {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  flex: 1;\n}\n\n.module6_header-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex: 2;\n}\n\n.module6_header-right {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  flex: 1;\n}\n\n.module6_back-button {\n  padding: 10px 20px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_back-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n.module6_module-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 2.4rem;\n  font-weight: 700;\n  color: #ffd700;\n  text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);\n  margin: 0;\n}\n\n.module6_header-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 状态按钮样式（类似返回首页按钮） */\n.module6_status-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 20px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: default;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n  min-width: 140px;\n  justify-content: center;\n}\n\n.module6_status-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n/* 不同状态的颜色变体 - 统一使用黄色主题 */\n.module6_status-button.success {\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_status-button.success:hover {\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n.module6_status-button.error {\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_status-button.error:hover {\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n.module6_status-button.syncing {\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_status-button.syncing:hover {\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n/* 保留原有的状态指示器样式 */\n.module6_sync-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #ffd700;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.module6_status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  animation: module6_pulse 2s ease-in-out infinite;\n}\n\n.module6_status-indicator.success {\n  background: #000;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\n}\n\n.module6_status-indicator.error {\n  background: #000;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\n}\n\n.module6_status-indicator.syncing {\n  background: #000;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\n}\n\n/* 表选择器 */\n.module6_table-selector {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n  padding: 20px;\n  background: rgba(0, 0, 0, 0.4);\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n  position: relative;\n  z-index: 10;\n}\n\n.module6_table-selector label {\n  font-size: 1.1rem;\n  color: #ffd700;\n  font-weight: 600;\n}\n\n.module6_table-dropdown {\n  padding: 10px 15px;\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid rgba(255, 215, 0, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.module6_table-dropdown:focus {\n  outline: none;\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_table-dropdown option {\n  background: #1a1a2e;\n  color: #ffffff;\n}\n\n\n\n/* 合并到一行的统一头部布局 */\n.module6_unified-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 12px;\n  padding: 15px;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  gap: 20px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\n}\n\n/* 左侧和右侧的指标/工作选择器 */\n.module6_header-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 15px 25px;\n  background: transparent;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.7);\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  min-width: 180px;\n  justify-content: center;\n}\n\n.module6_header-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);\n  transition: left 0.5s ease;\n}\n\n.module6_header-section:hover::before {\n  left: 100%;\n}\n\n.module6_header-section.active {\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\n  color: #ffd700;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);\n  border-color: rgba(255, 215, 0, 0.6);\n}\n\n.module6_header-section:hover {\n  color: #ffd700;\n  transform: translateY(-2px);\n  border-color: rgba(255, 215, 0, 0.5);\n}\n\n.module6_section-icon {\n  font-size: 1.3rem;\n  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));\n}\n\n.module6_section-title {\n  font-weight: 700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\n  white-space: nowrap;\n}\n\n/* 月份选择器 */\n.module6_month-selector {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  background: transparent;\n  border-radius: 8px;\n  padding: 12px 20px;\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  transition: all 0.3s ease;\n}\n\n.module6_month-selector .module6_month-nav-btn {\n  width: 35px;\n  height: 35px;\n  background: rgba(255, 215, 0, 0.2);\n  border: 1px solid rgba(255, 215, 0, 0.5);\n  border-radius: 6px;\n  color: #ffd700;\n  font-size: 1rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.module6_month-selector .module6_month-nav-btn:hover {\n  background: rgba(255, 215, 0, 0.3);\n  border-color: rgba(255, 215, 0, 0.7);\n  transform: scale(1.1);\n}\n\n.module6_month-selector .module6_month-display {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.1rem;\n  font-weight: 700;\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\n  min-width: 100px;\n  text-align: center;\n}\n\n\n\n\n\n/* 加载状态 */\n.module6_loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  gap: 20px;\n}\n\n.module6_loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(255, 215, 0, 0.3);\n  border-top: 3px solid #ffd700;\n  border-radius: 50%;\n  animation: module6_spin 1s linear infinite;\n}\n\n.module6_loading-text {\n  color: #ffd700;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n\n/* 动画 */\n@keyframes module6_float {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n    opacity: 0.7;\n  }\n  50% {\n    transform: translateY(-20px) rotate(180deg);\n    opacity: 1;\n  }\n}\n\n@keyframes module6_gridMove {\n  0% {\n    transform: translate(0, 0);\n  }\n  100% {\n    transform: translate(50px, 50px);\n  }\n}\n\n@keyframes module6_pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.1);\n  }\n}\n\n@keyframes module6_spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 表格样式优化 - 移除重复定义，使用KPITable.css中的样式 */\n\n/* 减少行间距 */\n.module6_kpi-table tbody tr {\n  height: 40px;\n}\n\n.module6_kpi-table tbody tr:nth-child(even) {\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.module6_kpi-table tbody tr:hover {\n  background: rgba(255, 215, 0, 0.05);\n}\n\n/* 特殊列样式 - 保持与KPITable.css一致 */\n.module6_sequence-cell,\n.module6_indicator-cell {\n  background: rgba(255, 215, 0, 0.05);\n  font-weight: 600;\n}\n\n/* 序号列特定样式覆盖 - 精确调整宽度 */\n.module6_sequence-cell {\n  min-width: 60px !important;\n  max-width: 80px !important;\n  width: 70px !important;\n  padding: 12px 4px !important;\n}\n\n.module6_month-cell {\n  /* 优化后的月份列宽度 */\n  min-width: 200px;\n}\n\n.module6_month-cell.editable {\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.module6_month-cell.editable:hover {\n  background: rgba(255, 215, 0, 0.1);\n}\n\n.module6_month-cell.readonly {\n  background: rgba(128, 128, 128, 0.1);\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 滚动条样式 */\n.module6_table-container::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n.module6_table-container::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n}\n\n.module6_table-container::-webkit-scrollbar-thumb {\n  background: rgba(255, 215, 0, 0.5);\n  border-radius: 4px;\n}\n\n.module6_table-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 215, 0, 0.7);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .module6_module-title {\n    font-size: 2rem;\n  }\n  \n  .module6_unified-header {\n    flex-direction: column;\n    gap: 15px;\n    padding: 15px;\n  }\n  \n  .module6_header-section {\n    min-width: 160px;\n    padding: 12px 20px;\n  }\n}\n\n@media (max-width: 768px) {\n  .module6_module-six-header {\n    flex-direction: column;\n    gap: 15px;\n    padding: 15px 20px;\n  }\n  \n  .module6_header-left {\n    flex-direction: column;\n    gap: 10px;\n  }\n  \n  .module6_module-title {\n    font-size: 1.6rem;\n    text-align: center;\n  }\n  \n  .module6_table-selector {\n    flex-direction: column;\n    gap: 10px;\n  }\n  \n  .module6_table-dropdown {\n    min-width: 150px;\n  }\n  \n  .module6_module-six-content {\n    padding: 20px 15px;\n  }\n} ", "/* KPI表格样式 - 模块六 */\n\n/* 表格控制区域 */\n.module6_table-controls {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.6);\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n}\n\n/* 月份导航 */\n.module6_month-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.module6_month-nav-btn {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-size: 1.2rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_month-nav-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n.module6_month-display {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n  min-width: 120px;\n  text-align: center;\n}\n\n/* 导出按钮 */\n.module6_export-btn {\n  padding: 12px 24px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_export-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n\n\n/* 主表格 - 合并容器样式，修复表头固定，移除外边框 */\n.module6_kpi-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  background: rgba(0, 0, 0, 0.4);\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.95rem;\n  position: relative;\n  border-radius: 12px;\n  overflow: hidden;\n  max-height: 70vh;\n  overflow-y: auto;\n  /* 确保表格本身没有外边框 */\n  border: none !important;\n  /* 移除所有可能的边框和outline */\n  outline: none !important;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;\n}\n\n/* 表头 - 修复固定表头 */\n.module6_kpi-table thead {\n  /* 移除thead的sticky，只在th上设置 */\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\n}\n\n.module6_kpi-table th {\n  padding: 15px 12px;\n  text-align: center;\n  vertical-align: middle;\n  font-weight: 700;\n  color: #ffd700;\n  border-bottom: 2px solid rgba(255, 215, 0, 0.4);\n  /* 移除所有其他边框 */\n  border-top: none;\n  border-left: none;\n  border-right: none;\n  /* 强化sticky定位 - 确保表头固定 */\n  position: sticky !important;\n  top: 0 !important;\n  z-index: 1000 !important;\n  background: rgba(0, 0, 0, 0.95) !important;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\n  white-space: nowrap;\n  min-width: 100px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\n  /* 确保背景完全覆盖 */\n  background-clip: padding-box;\n}\n\n.module6_month-header {\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05)) !important;\n  font-size: 0.9rem;\n  min-width: 120px;\n}\n\n/* 表格行 - 确保最后一行没有边框 */\n.module6_kpi-table tbody tr {\n  border-bottom: 1px solid rgba(255, 215, 0, 0.2);\n  transition: all 0.3s ease;\n}\n\n/* 移除最后一行的下边框，防止外边框线 */\n.module6_kpi-table tbody tr:last-child {\n  border-bottom: none !important;\n}\n\n.module6_kpi-table tbody tr:hover {\n  background: rgba(255, 215, 0, 0.1);\n  transform: scale(1.001);\n}\n\n.module6_kpi-table tbody tr:nth-child(even) {\n  background: rgba(255, 215, 0, 0.05);\n}\n\n.module6_kpi-table tbody tr:nth-child(even):hover {\n  background: rgba(255, 215, 0, 0.15);\n}\n\n/* 表格单元格 - 垂直居中优化，移除重复边框 */\n.module6_kpi-table td {\n  padding: 12px 8px;\n  text-align: center;\n  vertical-align: middle;\n  /* 移除所有边框 */\n  border: none;\n  position: relative;\n  min-width: 100px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  max-width: 300px;\n  height: auto;\n  min-height: 50px;\n}\n\n/* 固定列样式 - 垂直居中优化，精确调整序号列宽度 */\n.module6_sequence-cell {\n  background: rgba(255, 215, 0, 0.1);\n  font-weight: 700;\n  color: #ffd700;\n  /* 精确调整序号列宽度，仅容纳\"序号\"两字的最小宽度 */\n  min-width: 60px;\n  max-width: 80px;\n  width: 70px;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 4px;\n  font-size: 0.9rem;\n}\n\n.module6_indicator-cell {\n  background: rgba(255, 215, 0, 0.05);\n  text-align: left;\n  padding: 12px 15px;\n  font-weight: 600;\n  /* 由于序号、权重、评分列减小，可以进一步扩大指标列 */\n  min-width: 250px;\n  max-width: 420px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n}\n\n.module6_target-cell {\n  text-align: left;\n  padding: 12px 15px;\n  /* 由于序号、权重、评分列减小，可以进一步扩大目标值列宽度 */\n  min-width: 250px;\n  max-width: 380px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n}\n\n.module6_weight-cell {\n  font-weight: 700;\n  color: #ffd700;\n  /* 权重列与序号列保持相同宽度 */\n  min-width: 60px;\n  max-width: 80px;\n  width: 70px;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 4px;\n}\n\n.module6_category-cell {\n  /* 由于序号、权重、评分列减小，可以进一步扩大指标分类列 */\n  min-width: 150px;\n  max-width: 200px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.module6_frequency-cell {\n  /* 由于序号、权重、评分列减小，可以适当扩大频次列 */\n  min-width: 100px;\n  max-width: 130px;\n  width: 120px;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 6px;\n}\n\n/* 月份单元格 - 垂直居中优化，扩大显示空间 */\n.module6_month-cell {\n  /* 由于序号、权重、评分列减小，月份列可以进一步扩大 */\n  min-width: 220px;\n  max-width: 420px;\n  position: relative;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  vertical-align: middle;\n  padding: 12px 15px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.5;\n  min-height: 60px;\n  text-align: center;\n}\n\n/* 月份评分列专门样式 - 与序号列保持相同宽度 */\n.module6_score-cell {\n  min-width: 60px !important;\n  max-width: 80px !important;\n  width: 70px !important;\n  padding: 12px 4px !important;\n  text-align: center !important;\n}\n\n/* 可编辑单元格样式 */\n.module6_month-cell.editable {\n  background: rgba(255, 215, 0, 0.05);\n  border-left: 3px solid rgba(255, 215, 0, 0.3);\n}\n\n.module6_month-cell.editable:hover {\n  background: rgba(255, 215, 0, 0.15);\n  box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);\n  transform: scale(1.02);\n}\n\n/* 只读单元格样式 */\n.module6_month-cell.readonly {\n  background: rgba(128, 128, 128, 0.15);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: not-allowed;\n  border-left: 3px solid rgba(128, 128, 128, 0.5);\n}\n\n.module6_month-cell.readonly:hover {\n  background: rgba(128, 128, 128, 0.2);\n}\n\n/* 匹配状态指示 */\n.module6_month-cell.matched {\n  border-top: 2px solid rgba(0, 255, 0, 0.4);\n}\n\n.module6_month-cell.unmatched {\n  border-top: 2px solid rgba(255, 165, 0, 0.4);\n}\n\n/* 只读指示器 */\n.module6_readonly-indicator {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  font-size: 0.8rem;\n  opacity: 0.7;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 5;\n}\n\n/* 单元格内容容器 - 垂直居中优化 */\n.module6_cell-content {\n  width: 100%;\n  min-height: 24px;\n  line-height: 1.5;\n  word-break: break-word;\n  white-space: pre-wrap;\n  overflow-wrap: break-word;\n  text-align: center;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.95);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n/* 单元格行显示 */\n.module6_cell-line {\n  margin-bottom: 4px;\n  display: block;\n}\n\n.module6_cell-line:last-child {\n  margin-bottom: 0;\n}\n\n.module6_cell-line:empty {\n  display: none;\n}\n\n/* 长内容显示 */\n.module6_cell-long-content {\n  max-height: 150px;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  scrollbar-color: rgba(255, 215, 0, 0.5) transparent;\n  padding-right: 4px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-thumb {\n  background: rgba(255, 215, 0, 0.5);\n  border-radius: 3px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 215, 0, 0.7);\n}\n\n/* 内容截断提示 */\n.module6_cell-content.truncated::after {\n  content: \"...\";\n  color: rgba(255, 215, 0, 0.7);\n  font-weight: bold;\n}\n\n/* 空内容占位符 */\n.module6_cell-content:empty::before {\n  content: \"-\";\n  color: rgba(255, 255, 255, 0.3);\n  font-style: italic;\n}\n\n/* 输入框和文本域样式 - 优化版本 */\n.module6_cell-input {\n  width: 100%;\n  min-height: 30px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 6px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  text-align: center;\n  outline: none;\n  padding: 8px;\n  transition: all 0.3s ease;\n}\n\n.module6_cell-input:focus {\n  background: rgba(255, 215, 0, 0.2);\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);\n  transform: scale(1.02);\n}\n\n.module6_cell-textarea {\n  width: 100%;\n  min-height: 60px;\n  max-height: 120px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 6px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  outline: none;\n  padding: 8px;\n  resize: vertical;\n  line-height: 1.4;\n  transition: all 0.3s ease;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n\n.module6_cell-textarea:focus {\n  background: rgba(255, 215, 0, 0.2);\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);\n  transform: scale(1.02);\n}\n\n/* 文本域滚动条样式 */\n.module6_cell-textarea::-webkit-scrollbar {\n  width: 6px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-thumb {\n  background: rgba(255, 215, 0, 0.5);\n  border-radius: 3px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 215, 0, 0.7);\n}\n\n/* 同步状态工具栏 - 作为表格行 */\n.module6_sync-toolbar {\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  border-radius: 8px;\n  padding: 0;\n  margin: 0;\n}\n\n.module6_toolbar-content {\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.module6_pending-changes,\n.module6_sync-errors {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.module6_pending-count,\n.module6_error-count {\n  color: #ffd700;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.module6_retry-btn,\n.module6_clear-btn,\n.module6_clear-errors-btn,\n.module6_show-errors-btn {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.module6_retry-btn {\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n}\n\n.module6_retry-btn:hover {\n  background: linear-gradient(45deg, #45a049, #3d8b40);\n  transform: translateY(-1px);\n}\n\n.module6_clear-btn,\n.module6_clear-errors-btn {\n  background: linear-gradient(45deg, #f44336, #da190b);\n  color: white;\n}\n\n.module6_clear-btn:hover,\n.module6_clear-errors-btn:hover {\n  background: linear-gradient(45deg, #da190b, #c62828);\n  transform: translateY(-1px);\n}\n\n.module6_show-errors-btn {\n  background: linear-gradient(45deg, #2196F3, #1976D2);\n  color: white;\n}\n\n.module6_show-errors-btn:hover {\n  background: linear-gradient(45deg, #1976D2, #1565C0);\n  transform: translateY(-1px);\n}\n\n/* 错误模态框 */\n.module6_modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.module6_error-modal {\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 12px;\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n}\n\n.module6_modal-header {\n  background: rgba(255, 215, 0, 0.1);\n  padding: 16px 20px;\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.module6_modal-header h3 {\n  color: #ffd700;\n  margin: 0;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 700;\n}\n\n.module6_modal-close {\n  background: none;\n  border: none;\n  color: #ffd700;\n  font-size: 1.2rem;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.module6_modal-close:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.1);\n}\n\n.module6_modal-content {\n  padding: 20px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.module6_error-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.module6_error-item {\n  background: rgba(255, 0, 0, 0.1);\n  border: 1px solid rgba(255, 0, 0, 0.3);\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.module6_error-field {\n  color: #ffd700;\n  font-weight: 600;\n  font-size: 0.9rem;\n  margin-bottom: 4px;\n}\n\n.module6_error-message {\n  color: #ff6b6b;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.module6_error-time {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 0.8rem;\n  margin-bottom: 4px;\n}\n\n.module6_error-value {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.8rem;\n  font-style: italic;\n}\n\n.module6_modal-footer {\n  background: rgba(0, 0, 0, 0.3);\n  padding: 16px 20px;\n  border-top: 1px solid rgba(255, 215, 0, 0.3);\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n.module6_retry-all-btn,\n.module6_clear-all-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.module6_retry-all-btn {\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n}\n\n.module6_retry-all-btn:hover {\n  background: linear-gradient(45deg, #45a049, #3d8b40);\n  transform: translateY(-1px);\n}\n\n.module6_clear-all-btn {\n  background: linear-gradient(45deg, #f44336, #da190b);\n  color: white;\n}\n\n.module6_clear-all-btn:hover {\n  background: linear-gradient(45deg, #da190b, #c62828);\n  transform: translateY(-1px);\n}\n\n/* 合并单元格样式 */\n.module6_merged-cell {\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n}\n\n.module6_merged-cell::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);\n  pointer-events: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .module6_table-controls {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  \n  .module6_month-navigation {\n    justify-content: center;\n  }\n  \n  .module6_export-btn {\n    align-self: center;\n  }\n  \n  .module6_kpi-table {\n    font-size: 0.85rem;\n  }\n  \n  .module6_kpi-table th,\n  .module6_kpi-table td {\n    padding: 8px 6px;\n  }\n  \n  /* 序号列响应式优化 */\n  .module6_sequence-cell {\n    min-width: 50px;\n    max-width: 60px;\n    width: 55px;\n    padding: 8px 2px;\n    font-size: 0.8rem;\n  }\n  \n  /* 权重列响应式优化 */\n  .module6_weight-cell {\n    min-width: 50px;\n    max-width: 60px;\n    width: 55px;\n    padding: 8px 2px;\n  }\n  \n  /* 评分列响应式优化 */\n  .module6_score-cell {\n    min-width: 50px !important;\n    max-width: 60px !important;\n    width: 55px !important;\n    padding: 8px 2px !important;\n  }\n  \n  .module6_month-header {\n    min-width: 100px;\n    font-size: 0.8rem;\n  }\n  \n  .module6_month-cell {\n    min-width: 100px;\n  }\n}\n\n@media (max-width: 768px) {\n  .module6_table-controls {\n    padding: 15px 20px;\n  }\n  \n  .module6_month-display {\n    font-size: 1rem;\n    min-width: 100px;\n  }\n  \n  .module6_month-nav-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 1rem;\n  }\n  \n  .module6_export-btn {\n    padding: 10px 20px;\n    font-size: 0.9rem;\n  }\n  \n  .module6_kpi-table {\n    font-size: 0.8rem;\n  }\n  \n  .module6_kpi-table th,\n  .module6_kpi-table td {\n    padding: 6px 4px;\n  }\n  \n  /* 序号列移动端优化 */\n  .module6_sequence-cell {\n    min-width: 45px;\n    max-width: 50px;\n    width: 48px;\n    padding: 6px 2px;\n    font-size: 0.75rem;\n  }\n  \n  /* 权重列移动端优化 */\n  .module6_weight-cell {\n    min-width: 45px;\n    max-width: 50px;\n    width: 48px;\n    padding: 6px 2px;\n  }\n  \n  /* 评分列移动端优化 */\n  .module6_score-cell {\n    min-width: 45px !important;\n    max-width: 50px !important;\n    width: 48px !important;\n    padding: 6px 2px !important;\n  }\n  \n  .module6_indicator-cell,\n  .module6_target-cell {\n    padding-left: 8px;\n  }\n  \n  .module6_month-header {\n    min-width: 80px;\n    font-size: 0.75rem;\n  }\n  \n  .module6_month-cell {\n    min-width: 80px;\n  }\n  \n  .module6_month-cell input {\n    font-size: 0.8rem;\n    padding: 3px;\n  }\n} ", "/* 导出模态框样式 - 模块六 */\r\n.module6_export-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.module6_export-modal {\r\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\r\n  border: 2px solid rgba(255, 215, 0, 0.5);\r\n  border-radius: 16px;\r\n  width: 90%;\r\n  max-width: 600px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n  animation: module6_modalSlideIn 0.3s ease-out;\r\n}\r\n\r\n/* 模态框头部 */\r\n.module6_modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20px 30px;\r\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\r\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_modal-header h3 {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: #ffd700;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_close-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 50%;\r\n  color: #000;\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_close-btn:hover {\r\n  transform: scale(1.1);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n/* 模态框内容 */\r\n.module6_modal-content {\r\n  padding: 30px;\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* 选择区域 */\r\n.module6_selection-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.module6_section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.module6_section-header h4 {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: #ffd700;\r\n  margin: 0;\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_selection-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.module6_select-all-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #ffffff;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.module6_select-all-label:hover {\r\n  color: #ffd700;\r\n}\r\n\r\n.module6_select-all-label input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_selection-count {\r\n  color: #ffd700;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  padding: 4px 8px;\r\n  background: rgba(255, 215, 0, 0.1);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n/* 指标列表 */\r\n.module6_indicators-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 8px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  padding: 10px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-track {\r\n  background: rgba(255, 215, 0, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-thumb {\r\n  background: rgba(255, 215, 0, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(255, 215, 0, 0.7);\r\n}\r\n\r\n.module6_indicator-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 8px 12px;\r\n  color: #ffffff;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-radius: 6px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.module6_indicator-item:hover {\r\n  background: rgba(255, 215, 0, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.module6_indicator-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.module6_indicator-item input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_indicator-text {\r\n  flex: 1;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 格式选择区域 */\r\n.module6_format-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.module6_format-section h4 {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: #ffd700;\r\n  margin: 0 0 20px 0;\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_format-options {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.module6_format-option {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 15px 20px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border: 2px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 10px;\r\n  color: #ffffff;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.module6_format-option:hover {\r\n  border-color: #ffd700;\r\n  background: rgba(255, 215, 0, 0.1);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);\r\n}\r\n\r\n.module6_format-option input[type=\"radio\"] {\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_format-icon {\r\n  font-size: 1.5rem;\r\n  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));\r\n}\r\n\r\n.module6_format-text {\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n/* 模态框底部 */\r\n.module6_modal-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  gap: 15px;\r\n  padding: 20px 30px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-top: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_cancel-btn {\r\n  padding: 12px 24px;\r\n  background: transparent;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.module6_cancel-btn:hover {\r\n  border-color: #ffffff;\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.module6_cancel-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.module6_export-btn {\r\n  padding: 12px 24px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 8px;\r\n  color: #000;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_export-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_export-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes module6_modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-50px) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .module6_export-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .module6_modal-header {\r\n    padding: 15px 20px;\r\n  }\r\n  \r\n  .module6_modal-header h3 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .module6_modal-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .module6_section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_selection-controls {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .module6_format-options {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .module6_format-option {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .module6_modal-footer {\r\n    padding: 15px 20px;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_cancel-btn,\r\n  .module6_export-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n  }\r\n} "], "names": [], "sourceRoot": ""}